<template>
  <div class="file-preview-container">
    <div class="preview-wrapper" :style="wrapperStyle">
      <!-- 图片预览 -->
      <n-image
        v-if="props.type === 'img'"
        :src="props.url"
        :width="parseInt(props.width)"
        :height="parseInt(props.height)"
        object-fit="cover"
      />
      <!-- 视频预览 -->
      <video
        v-else-if="props.type === 'video'"
        :src="props.url"
        style="width: 100%; height: 100%; object-fit: cover"
        muted
      ></video>
      <!-- 文档占位 -->
      <div
        v-else-if="props.type === 'doc'"
        class="doc-placeholder"
        :style="{ backgroundColor: docInfo.color }"
      >
        <span class="doc-type-name">{{ docInfo.typeName }}</span>
      </div>
      <!-- 操作蒙层 -->
      <div v-if="props.type !== 'img'" class="preview-actions">
        <n-icon class="action-icon" :size="40" @click="handlePreview">
          <PlayIcon v-if="props.type === 'video'" />
          <template v-else-if="props.type === 'doc'">
            <PreviewIcon v-if="isPdf" />
            <DownloadIcon v-else />
          </template>
        </n-icon>
      </div>
    </div>
    <!-- 标签 -->
    <div v-if="props.label" class="label-text">{{ props.label }}</div>
    <!-- 视频播放器 -->
    <VideoPlayerModal v-model:show="showVideoModal" :video-url="props.url || ''" />
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { NImage, NIcon } from 'naive-ui';
  import {
    EyeOutline as PreviewIcon,
    PlayCircleOutline as PlayIcon,
    CloudDownloadOutline as DownloadIcon,
  } from '@vicons/ionicons5';
  import { filePreviewProps } from './props';
  import { getFileExtension, getDocInfo } from './helper';
  import VideoPlayerModal from './components/VideoPlayerModal.vue';

  const props = defineProps(filePreviewProps);
  const showVideoModal = ref(false);

  const wrapperStyle = computed(() => ({
    width: props.width,
    height: props.height,
  }));

  const isPdf = computed(() => {
    if (props.type === 'doc' && props.url) {
      const extension = getFileExtension(props.url);
      return extension === 'pdf';
    }
    return false;
  });

  const docInfo = computed(() => {
    if (props.type === 'doc' && props.url) {
      const extension = getFileExtension(props.url);
      return getDocInfo(extension);
    }
    return { typeName: 'DOC', color: '#a0a0a0' };
  });

  function handlePreview() {
    if (!props.url) return;

    if (props.type === 'video') {
      showVideoModal.value = true;
    } else if (props.type === 'doc') {
      const extension = getFileExtension(props.url);
      if (extension === 'pdf') {
        window.open(props.url, '_blank');
      } else {
        const link = document.createElement('a');
        link.href = props.url;
        link.setAttribute('download', '');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  }
</script>

<style lang="less" scoped>
  .file-preview-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .preview-wrapper {
    position: relative;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    border: 1px dashed #d9d9d9;

    &:hover .preview-actions {
      opacity: 1;
    }
  }
  .preview-actions {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    opacity: 0;
    transition: opacity 0.3s;
  }
  .action-icon {
    font-size: 40px;
    &:hover {
      color: #2080f0;
    }
  }
  .label-text {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
  }
  .video-placeholder,
  .doc-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f0f0f0;
  }
  .doc-placeholder {
    color: white;
    font-size: 18px;
    font-weight: bold;
  }
  // :deep(.overlay-mask) {
  //   img {
  //     filter: blur(10px);
  //     transition: filter 0.5s ease;
  //   }
  // }
</style>
