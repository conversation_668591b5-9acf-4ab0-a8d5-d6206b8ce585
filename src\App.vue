<template>
  <NConfigProvider
    :locale="zhCN"
    :theme="getDarkTheme"
    :theme-overrides="getThemeOverrides"
    :date-locale="dateZhCN"
  >
    <n-watermark
      :content="`杭州优易润科技有限公司 ${userInfo.username || ''} ${userInfo.mobileNo || ''}`"
      cross
      fullscreen
      :font-size="16"
      :line-height="16"
      :width="454"
      :height="400"
      :x-offset="12"
      :y-offset="60"
      :rotate="-5"
      fontColor="rgba(198,198,198,0.3)"
      :z-index="9999"
    />
    <AppProvider>
      <RouterView />
    </AppProvider>
  </NConfigProvider>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { zhCN, dateZhCN, darkTheme } from 'naive-ui';
  import { AppProvider } from '@/components/Application';
  import { useDesignSettingStore } from '@/store/modules/designSetting';
  import { lighten } from '@/utils/index';
  import { useUserStore } from '@/store/modules/user';

  const designStore = useDesignSettingStore();
  const userStore = useUserStore();
  const userInfo = computed(() => userStore.info);
  /**
   * @type import('naive-ui').GlobalThemeOverrides
   */
  const getThemeOverrides = computed(() => {
    const appTheme = designStore.appTheme;
    const lightenStr = lighten(designStore.appTheme, 6);
    return {
      common: {
        primaryColor: appTheme,
        primaryColorHover: lightenStr,
        primaryColorPressed: lightenStr,
        primaryColorSuppl: appTheme,
      },
      LoadingBar: {
        colorLoading: appTheme,
      },
    };
  });

  const getDarkTheme = computed(() => (designStore.darkTheme ? darkTheme : undefined));
</script>
