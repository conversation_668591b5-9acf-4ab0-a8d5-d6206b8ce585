import { computed, ref, watchEffect } from 'vue';
import { useDocumentVisibility, useTimeoutFn } from '@vueuse/core';
import TodoDialog from '@/components/TodoMessage/components/TodoDialog/index.vue';
import { batchUserMessage, getUserMessageCount, batchClueReminded } from '@/api/system/message';
import { useRouter } from 'vue-router';
import { JumpWorkbench } from '@/views/message/detail/enum';
import emitter, { bufferedEmitter, EventNames } from '@/utils/eventBus';
import { useMountComponent } from '@/composables/useMountComponent';

export function useTodoMessage() {
  const { mount } = useMountComponent();
  const router = useRouter();
  const data = ref<any>({});
  const count = computed(() => data.value.unHandledCount || 0);

  const getMessage = async () => {
    // return getUserMessageCount().then((res) => {
    //   if (res.code === 200) {
    //     data.value = res.data;
    //   }
    // });
  };

  const onHandleMsg = async (operationType: 1 | 2, messageIds: number[]) => {
    try {
      const result = await batchUserMessage({
        operationType,
        messageIds,
      });
      if (result.code !== 200) {
        throw new Error(result.msg || '操作失败');
      }

      emitter.emit(EventNames.HANDLE_MESSAGE_SUCCESS, {
        operationType,
        messageIds,
      });
    } catch (error) {
      throw error;
    }
  };

  const onHandleClueReminded = async () => {
    try {
      const result = await batchClueReminded();
      if (result.code !== 200) {
        throw new Error(result.msg || '操作失败');
      }

      emitter.emit(EventNames.HANDLE_MESSAGE_SUCCESS, {
        operationType: 1,
        messageIds: [],
      });
    } catch (error) {
      throw error;
    }
  };

  const showTodoMessage = (param) => {
    const props = {
      title: param.content,
      subtitle: param.contentData,
      time: param.createTime,
      onPositiveClick: async () => {
        await onHandleMsg(1, [param.id]);
        unmount();
        goWorkplace(JumpWorkbench.OpenDetail, param.entityId);
      },
      onNegativeClick: async () => {
        await onHandleMsg(1, [param.id]);
        unmount();
      },
      onClose: () => {
        unmount();
      },
    };
    const unmount = mount({
      render: <TodoDialog {...props} />,
      transition: 'slide-up',
    });
  };

  const showAllTodoMessage = (param) => {
    const props = {
      title: `当前已累计消息${param.count}条未查阅，请及时处理！`,
      subtitle: '',
      time: Date.now(),
      onPositiveClick: async () => {
        unmount();
        goMessageDetail();
      },
      onNegativeClick: async () => {
        unmount();
      },
      onClose: () => {
        unmount();
      },
    };
    const unmount = mount({
      render: <TodoDialog {...props} />,
      transition: 'slide-up',
    });
  };

  const showClueMessage = (val: number) => {
    const notification = window.$notification.warning({
      title: '消息提醒',
      content: `当前新增${val}条待领取线索，请合理分配或领取！`,
      duration: 1000 * 5,
      onClose: async () => {
        try {
          await onHandleClueReminded();

          notification?.destroy?.();
        } catch (error) {}
      },
    });
  };

  const goWorkplace = (type: JumpWorkbench, clueId?: number) => {
    bufferedEmitter.emit(EventNames.JUMP_WORKBENCH_AFTER, {
      fromMessage: type,
      clueId,
    });
    return router.push({
      path: `/dashboard/workplace`,
    });
  };

  const goMessageDetail = () => {
    return router.push({
      path: '/message/detail',
    });
  };

  const visibility = useDocumentVisibility();

  const { start, stop } = useTimeoutFn(() => {
    getMessage().finally(() => {
      start();

      if (data.value.unRemindClueCount > 0) {
        showClueMessage(data.value.unRemindClueCount);
        emitter.emit(EventNames.ADD_MESSAGE_SUCCESS);
      }

      if (
        data.value.message &&
        data.value.message.messageTypeId === 1 &&
        data.value.unRemindAgentCount === 1
      ) {
        showTodoMessage(data.value.message);
        emitter.emit(EventNames.ADD_MESSAGE_SUCCESS);
      } else if (
        data.value.message &&
        data.value.message.messageTypeId === 1 &&
        data.value.unRemindAgentCount > 1
      ) {
        showAllTodoMessage({ count: data.value.unRemindAgentCount });
        emitter.emit(EventNames.ADD_MESSAGE_SUCCESS);
      }
    });
  }, 1000);

  watchEffect(() => {
    if (visibility.value === 'visible') {
      start();
    } else {
      stop();
    }
  });

  return {
    count,
    data,
  };
}

export default useTodoMessage;
