<template>
  <n-card>
    <!-- 德易绑卡 -->
    <n-collapse arrow-placement="right" :expanded-names="expandedNames">
      <n-collapse-item name="2-5">
        <template #header>
          <Title :mainNode="2" :subNode="5" @click="toggleExpanded('2-5')" />
        </template>

        <div v-if="!isCurrentStagePending('2-5')">
          <SubTitle title="德易绑卡" />

          <n-descriptions label-placement="top" bordered :column="2">
            <n-descriptions-item label="德易获取绑卡状态">
              <n-tag
                v-if="initialData.deyiCardBindStatus"
                size="small"
                :type="
                  String(initialData.deyiCardBindStatus) === String(CardBindStatus.Bind)
                    ? 'success'
                    : 'warning'
                "
                >{{ CardBindStatusMap[initialData.deyiCardBindStatus] }}</n-tag
              >
              <template v-else> - </template>
            </n-descriptions-item>
            <n-descriptions-item label="操作">
              <n-space>
                <n-button
                  size="small"
                  type="primary"
                  :disabled="String(initialData.deyiCardBindStatus) === String(CardBindStatus.Bind)"
                  @click="handleVerify"
                >
                  发起验证
                </n-button>
                <n-button
                  size="small"
                  type="primary"
                  :disabled="String(initialData.deyiCardBindStatus) === String(CardBindStatus.Bind)"
                  @click="queryDeYiCardBindStatus"
                >
                  状态查询
                </n-button>
              </n-space>
            </n-descriptions-item>
          </n-descriptions>
        </div>
      </n-collapse-item>
    </n-collapse>
  </n-card>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import VerifyModal from './components/VerifyModal/index.vue';
  import { useMountComponent } from '@/composables/useMountComponent';

  import { onMounted, inject } from 'vue';
  import { useBaseData } from '../../composables/useBaseData';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import { CardBindStatus, CardBindStatusMap } from '@/components/DrawerOrder/enum';
  import { getDeyiCardBindStatus } from '@/api/dashboard/deyi';
  import { useFormDisabled } from '@/components/DrawerOrder/composables/useFormDisabled';

  interface Props {
    innerOrderNo: string;
  }
  const { isCurrentStagePending } = useFormDisabled();
  const props = defineProps<Props>();
  const expandedNames = inject('expandedNames');
  const toggleExpanded = inject('toggleExpanded') as (name: string) => void;
  const orderDrawerStore = useOrderDrawerStore();

  const { mountPromisify } = useMountComponent();

  // 拉取数据
  const {
    data: initialData,
    getData,
    getOptions,
  } = useBaseData({
    innerOrderNo: props.innerOrderNo,
    keys: ['deyiCardBindStatus'],
  });

  // 验证
  const handleVerify = async () => {
    await mountPromisify({
      render: (ctx) => <VerifyModal innerOrderNo={props.innerOrderNo} onClose={ctx.unmount} />,
    });
    orderDrawerStore.triggerRefresh();
    getData();
  };

  // 查询状态
  const queryDeYiCardBindStatus = async () => {
    await getDeyiCardBindStatus({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };

  onMounted(() => {
    getData();
    getOptions();
  });
</script>

<style lang="less" scoped></style>
