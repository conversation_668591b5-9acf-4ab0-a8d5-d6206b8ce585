<template>
  <div class="vehicle-appraisal">
    <div class="flex items-center justify-between mb-5">
      <span>车辆信息</span>
      <n-button type="primary" size="small" @click="showModal = true">获取估价</n-button>
    </div>
    <n-data-table :columns="columns" :data="tableData" :render-cell="renderCell" />
    <VehicleAppraisalModel
      v-model:show="showModal"
      :clue-id="clueId"
      :localDetail="data"
      @submit-success="handleRefresh"
    />
  </div>
</template>

<script lang="tsx" setup>
  import { NDataTable } from 'naive-ui';
  import { usePreviewImage } from '@/composables/usePreviewImage';
  import type { DataTableColumns } from 'naive-ui';
  import type { CustomerDetail } from '@/api/detail';
  import VehicleAppraisalModel from './VehicleAppraisalModel.vue';
  import { onMounted, ref, watch } from 'vue';
  import { getCarEstimate } from '@/api/detail';
  const props = defineProps({
    data: {
      type: Object as () => CustomerDetail,
      required: true,
    },
    clueId: {
      type: [Number, String],
      required: true,
    },
  });
  const emit = defineEmits(['submit-success']);
  interface ClueInfo {
    [key: string]: any;
  }
  const showModal = ref(false);
  const { previewImage } = usePreviewImage();
  const onPreview = (url) => {
    previewImage([url] as string[]);
  };
  onMounted(() => {
    getData();
  });
  let tableData = ref([]);

  const columns: DataTableColumns<ClueInfo> = [
    { title: '序号', key: 'index', width: 80, align: 'center', render: (_, index) => index + 1 },
    {
      title: '车牌号',
      key: 'carNo',
      width: 100,
      align: 'center',
    },
    {
      title: '车辆评估价',
      key: 'carEstimatePrice',
      width: 100,
      align: 'center',
    },
    {
      title: '行驶证图片',
      key: 'licensePhoto',
      width: 100,
      align: 'center',
      render(row) {
        return (
          <n-button text type="primary" onClick={() => onPreview(row.licensePhoto)}>
            查看
          </n-button>
        );
      },
    },
    {
      title: '车300报告',
      key: 'car300Report',
      width: 120,
      align: 'center',
    },
    {
      title: '车辆VIN码',
      key: 'vin',
      width: 120,
      align: 'center',
    },
    {
      title: '首次上牌时间',
      key: 'regDate',
      width: 120,
      align: 'center',
    },
    {
      title: '发动机号',
      key: 'engineNumber',
      width: 120,
      align: 'center',
    },
    {
      title: '车辆类别',
      key: 'carType',
      width: 120,
      align: 'center',
    },
  ];

  const renderCell = (value: any) => {
    if (!value) return '--';
    return value;
  };
  function getData() {
    if (!props.data.clueInfoVo?.mobileNo) return;
    getCarEstimate({ mobile: props.data.clueInfoVo?.mobileNo }).then((res) => {
      console.log(res, 'res');
      tableData.value = res.data;
    });
  }
  function handleRefresh() {
    getData();
    emit('submit-success');
  }
</script>
<style lang="less" scoped>
  .vehicle-appraisal {
    padding: 16px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 20px;
    font-size: 14px;
  }
</style>
