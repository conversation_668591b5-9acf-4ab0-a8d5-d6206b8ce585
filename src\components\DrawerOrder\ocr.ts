import { getIdCard, getVehicleRegistration, getVehicleLicense } from '@/api/dashboard/deyi';
import dayjs from 'dayjs';
import { useAddressOptions } from '@/components/AddressInput/useAddressOptions';

export async function idCardOcr(params: { url: string }) {
  const { parseAddress } = useAddressOptions();
  const res = await getIdCard(params);

  const data = res?.data ?? {};
  const validPeriod = data?.validPeriod;
  const ipIdStartDate = validPeriod ? validPeriod.split('-')?.[0] : '';
  const ipIdExpireDate = validPeriod ? validPeriod.split('-')?.[1] : '';

  const address = await parseAddress(data?.address);

  return {
    preAuditName: data?.name,
    preAuditIdCardNum: data.idNumber,
    ipEthnicity: data.ethnicity ? data.ethnicity + '族' : '',
    ipIdStartDate: ipIdStartDate ? dayjs(ipIdStartDate).format('YYYY-MM-DD') : '',
    ipIdExpireDate: ipIdExpireDate ? dayjs(ipIdExpireDate).format('YYYY-MM-DD') : '',
    ipHukouProvince: address?.province || '',
    ipHukouCity: address?.city || '',
    ipHukouDistrict: address?.district || '',
    ipHukouDetail: address?.detail || '',
    idCardInfo: data,
  };
}

/**
 * 行驶证
 */
export function vehicleLicenseOcr(params: { url: string }) {
  return getVehicleLicense(params).then((res) => {
    const data = res?.data ?? {};
    return {
      psvaVinCode: data?.vinCode,
      psvaEngineNumber: data.engineNumber,
      psvaVehicleCate: data.vehicleType,
      psvaPlateNumber: data?.licensePlateNumber,
      vehicleRegisInfo: data,
      psvaFirstRegisDate: data?.registrationDate,
    };
  });
}
/**
 * 注册登记证
 */
export function recognizeVehicleLicenseOcr(params: { url: string }) {
  return getVehicleRegistration(params).then((res) => {
    const data = res?.data ?? {};
    return {
      psvaFirstRegisDate: data?.registrationDate,
      psvaDisplace: data?.displacement,
      psvaCarColoe:
        data.vehicleColor && data.vehicleColor?.indexOf?.('色') === -1
          ? data.vehicleColor + '色'
          : data.vehicleColor && data.vehicleColor?.indexOf?.('色') !== -1
          ? data.vehicleColor
          : '',
      psvaFuelType: data?.fuelType,
      gimaIssuingAuthority: data?.issueAuthority,
      gimaCertificateNo: data?.barCode,
      vehicleLicenInfo: data,
    };
  });
}
