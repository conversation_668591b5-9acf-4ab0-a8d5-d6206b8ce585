/**
 * 预审进件状态
 */
export const PreApprovalStatus = {
  // 待发起
  WaitStart: '待发起',
  // 已发起
  Start: '已发起',
} as const;

export const PreApprovalStatusMap = {
  [PreApprovalStatus.WaitStart]: '待发起',
  [PreApprovalStatus.Start]: '已发起',
} as const;

/**
 * 预审授权签署状态: 0未签署，1签署完成
 */
export const PreApprovalSignStatus = {
  // 未签署
  UnSign: 0,
  // 签署完成
  SignFinish: 1,
} as const;

export const PreApprovalSignStatusMap = {
  [PreApprovalSignStatus.UnSign]: '未签署',
  [PreApprovalSignStatus.SignFinish]: '签署完成',
} as const;

/**
 * 预审阶段授权书签署状态 说明: 1. 未提交 2. 预审中 3. 通过 4. 退回 5. 拒绝
 */
export const PreAuditPhaseSignStatus = {
  // 未提交
  UnSubmit: 1,
  // 预审中
  PreAudit: 2,
  // 通过
  Pass: 3,
  // 退回
  Back: 4,
  // 拒绝
  Refuse: 5,
} as const;

export const PreAuditPhaseSignStatusMap = {
  [PreAuditPhaseSignStatus.UnSubmit]: '未提交',
  [PreAuditPhaseSignStatus.PreAudit]: '预审中',
  [PreAuditPhaseSignStatus.Pass]: '通过',
  [PreAuditPhaseSignStatus.Back]: '退回',
  [PreAuditPhaseSignStatus.Refuse]: '拒绝',
} as const;

/**
 * 与申请人关系
 */
export const RelationShip = {
  // 父母
  Parent: '父母',
  // 其他亲属
  OtherFamily: '其他亲属',
  // 朋友
  Friend: '朋友',
  // 兄弟/姐妹
  Brother: '兄弟/姐妹',
  // 子女
  Child: '子女',
} as const;

export const RelationShipMap = {
  [RelationShip.Parent]: '父母',
  [RelationShip.OtherFamily]: '其他亲属',
  [RelationShip.Friend]: '朋友',
  [RelationShip.Brother]: '兄弟/姐妹',
  [RelationShip.Child]: '子女',
} as const;

export const RelationShipOptions = Object.keys(RelationShipMap).map((key) => ({
  label: RelationShipMap[key],
  value: key,
}));

/**
 * 资方进件 签约结果 1待提交，2审批中，3通过，4退回，5拒绝
 */
export const SignStatus = {
  // 待提交
  WaitSubmit: 1,
  // 审批中
  Audit: 2,
  // 通过
  Pass: 3,
  // 退回
  Back: 4,
  // 拒绝
  Refuse: 5,
} as const;

export const SignStatusMap = {
  [SignStatus.WaitSubmit]: '待提交',
  [SignStatus.Audit]: '审批中',
  [SignStatus.Pass]: '通过',
  [SignStatus.Back]: '退回',
  [SignStatus.Refuse]: '拒绝',
} as const;

/**
 * 德易进件 审批结果 1: 未提交2: 审批中3: 通过4: 退回5: 拒绝
 */
export const OrgSignStatus = {
  // 未提交
  WaitSubmit: 1,
  // 审批中
  Audit: 2,
  // 通过
  Pass: 3,
  // 退回
  Back: 4,
  // 拒绝
  Refuse: 5,
} as const;

export const OrgSignStatusMap = {
  [OrgSignStatus.WaitSubmit]: '未提交',
  [OrgSignStatus.Audit]: '审批中',
  [OrgSignStatus.Pass]: '通过',
  [OrgSignStatus.Back]: '退回',
  [OrgSignStatus.Refuse]: '拒绝',
} as const;

/**
 * 资方进件 授权书签署状态 0未签署，1签署完成
 */
export const AuthSignStatus = {
  // 未签署
  UnSign: 0,
  // 签署完成
  SignFinish: 1,
} as const;

export const AuthSignStatusMap = {
  [AuthSignStatus.UnSign]: '未签署',
  [AuthSignStatus.SignFinish]: '签署完成',
} as const;

/**
 * 资方绑卡合同签署 0未签署，1签署完成
 */
export const ContractSignStatus = {
  // 未签署
  UnSign: 0,
  // 签署完成
  SignFinish: 1,
} as const;

export const ContractSignStatusMap = {
  [ContractSignStatus.UnSign]: '未签署',
  [ContractSignStatus.SignFinish]: '签署完成',
} as const;

/**
 * 德易获取绑卡状态 0未绑卡，1已绑卡
 */
export const CardBindStatus = {
  // 未绑卡
  UnBind: 0,
  // 已绑卡
  Bind: 1,
} as const;

export const CardBindStatusMap = {
  [CardBindStatus.UnBind]: '未绑卡',
  [CardBindStatus.Bind]: '已绑卡',
} as const;

/**
 * 德易签约签署结果
 */
export const DeyiSignStatus = {
  // 未签署
  UnSign: 0,
  // 签署完成
  SignFinish: 1,
} as const;

export const DeyiSignStatusMap = {
  [DeyiSignStatus.UnSign]: '未签署',
  [DeyiSignStatus.SignFinish]: '签署完成',
} as const;

/**
 * 面签状态 开始面签、1签署中、2处理中、3已完成、4签署失败 5未发起
 */
export const FaceSignStatus = {
  // 开始面签
  StartFaceSign: 0,
  // 签署中
  Signing: 1,
  // 处理中
  Processing: 2,
  // 已完成
  Finish: 3,
  // 签署失败
  SignFail: 4,
  // 未发起
  UnStart: 5,
} as const;

export const FaceSignStatusMap = {
  [FaceSignStatus.StartFaceSign]: '开始面签',
  [FaceSignStatus.Signing]: '签署中',
  [FaceSignStatus.Processing]: '处理中',
  [FaceSignStatus.Finish]: '已完成',
  [FaceSignStatus.SignFail]: '签署失败',
  [FaceSignStatus.UnStart]: '未发起',
} as const;

/**
 * 面签审批结果 1待提交，2审批中，3通过，4退回，5拒绝
 */
export const FaceApproveStatus = {
  // 待提交
  WaitSubmit: 1,
  // 审批中
  Audit: 2,
  // 通过
  Pass: 3,
  // 退回
  Back: 4,
  // 拒绝
  Refuse: 5,
} as const;

export const FaceApproveStatusMap = {
  [FaceApproveStatus.WaitSubmit]: '待提交',
  [FaceApproveStatus.Audit]: '审批中',
  [FaceApproveStatus.Pass]: '通过',
  [FaceApproveStatus.Back]: '退回',
  [FaceApproveStatus.Refuse]: '拒绝',
} as const;

/**
 * gps安装进度查询 0暂存 1进行中 2已完成
 */
export const gpsInstallationStatus = {
  // 暂存
  Pending: 0,
  // 进行中
  Ongoing: 1,
  // 已完成
  Completed: 2,
} as const;
export const gpsInstallationStatusMap = {
  [gpsInstallationStatus.Pending]: '暂存',
  [gpsInstallationStatus.Ongoing]: '进行中',
  [gpsInstallationStatus.Completed]: '已完成',
} as const;
/**
 * GPS类型0、有线 1、无线
 *
 */
export const GpsType = {
  // 有线
  Wired: 0,
  // 无线
  Wireless: 1,
} as const;
export const GpsTypeMap = {
  [GpsType.Wired]: '有线',
  [GpsType.Wireless]: '无线',
} as const;
/**
 * GPS安装方式 1、带货安装 2、自行安装
 *
 */
export const GpsInstallWay = {
  // 带货安装
  BringGoods: 1,
  // 自行安装
  SelfInstall: 2,
} as const;
export const GpsInstallWayMap = {
  [GpsInstallWay.BringGoods]: '带货安装',
  [GpsInstallWay.SelfInstall]: '自行安装',
} as const;
/**
 * GPS、保险、审批状态查询 1待提交，2审批中，3通过，4退回，5 拒绝
 */
export const GpsInsuranceApproveStatus = {
  // 待提交
  Pending: 1,
  // 审批中
  Approving: 2,
  // 通过
  Pass: 3,
  // 退回
  Back: 4,
  // 拒绝
  Refuse: 5,
} as const;

export const GpsInsuranceApproveStatusMap = {
  [GpsInsuranceApproveStatus.Pending]: '待提交',
  [GpsInsuranceApproveStatus.Approving]: '审批中',
  [GpsInsuranceApproveStatus.Pass]: '通过',
  [GpsInsuranceApproveStatus.Back]: '退回',
  [GpsInsuranceApproveStatus.Refuse]: '拒绝',
} as const;
/**
 * 放款结果 ：1待提交，2审批中，3通过，4退回，5 拒绝
 */
export const LoanStatus = {
  // 待提交
  Pending: 1,
  // 审批中
  Approving: 2,
  // 通过
  Pass: 3,
  // 退回
  Back: 4,
  // 拒绝
  Refuse: 5,
} as const;

export const LoanStatusMap = {
  [LoanStatus.Pending]: '待提交',
  [LoanStatus.Approving]: '审批中',
  [LoanStatus.Pass]: '通过',
  [LoanStatus.Back]: '退回',
  [LoanStatus.Refuse]: '拒绝',
} as const;

/**
 * 公牌
 */
export const PublicType = {
  // 一般公牌
  Company: '一般公牌',
  // 非公牌
  Person: '非公牌',
};

export const PublicTypeMap = {
  [PublicType.Company]: '一般公牌',
  [PublicType.Person]: '非公牌',
};
