import { get, post } from '@/utils/lib/axios.package';

interface IBaseParams {
  innerOrderNo: string;
}
interface IFieldParams {
  fieldName: string[];
}
interface IGPSParams {
  clueId: string;
}
/**
 * 跳到预审阶段
 */
export function moveNode2PreAudit(params: IBaseParams) {
  return get(`/cms/loan-order-record/move/node2PreAudit/${params.innerOrderNo}`);
}

/**
 * 查询当前订单所有节点（不包含未开始）
 */
export function getOrderNode(params: IBaseParams) {
  return get(`/cms/loan-order-record/node/${params.innerOrderNo}`);
}

/**
 * 德易订单节点中的数据查询
 */
export function getFieldData(params: IBaseParams & IFieldParams) {
  return post(`/cms/loan-order-record/select/field/data`, params);
}

/**
 * 德易订单节点中前端需要的字段信息
 */
export function getFieldConfig(params: IFieldParams) {
  return post(`/cms/loan-order-record/select/field/config`, params);
}

/**
 * 德易订单节点中的数据保存
 */
export function saveFieldData(data: IBaseParams & { fieldData: Record<string, unknown> }) {
  return post(`/cms/loan-order-record/update/field/data`, data);
}

/**
 * 德易订单节点中的表单提交
 */
export function submitForm(data: IBaseParams & { mainNodeCode: number; subNodeCode: number }) {
  return post(`/cms/loan-order-record/data/commit`, data);
}

/**
 * 预审生成状态查询（德易进件状态）
 */
export function getPreAuditStatus(params: IBaseParams) {
  return get(`/cms/loan-order-record/PRE_AUDIT/status/query/${params.innerOrderNo}`);
}

/**
 * 授权书签署地址生成
 */
export function getAuthorizationSign(params: IBaseParams) {
  return get(`/cms/loan-order-record/AUTHORIZATION_SIGN/getUrl/${params.innerOrderNo}`);
}

/**
 * 授权书签署状态查询
 */
export function getAuthorizationSignStatus(params: IBaseParams) {
  return get(`/cms/loan-order-record/AUTHORIZATION_SIGN/status/query/${params.innerOrderNo}`);
}

/**
 * 预审阶段 重新提交身份证
 */
export function reSubmitIdCard(params: IBaseParams) {
  return get(`/cms/loan-order-record/AUTHORIZATION_SIGN/idCard/fileUpload/${params.innerOrderNo}`);
}

/**
 * 预审阶段 签署状态查询
 */
export function getPreAuditPhaseStatus(params: IBaseParams) {
  return get(`/cms/loan-order-record/PRE_AUDIT_PHASE/status/query/${params.innerOrderNo}`);
}

/**
 * 选择产品&车辆评估 查询产品列表
 */
export function getProductList(params: IBaseParams) {
  return get(`/cms/loan-order-record/PSVA/list/product/${params.innerOrderNo}`);
}

/**
 * 选择产品&车辆评估 选择产品
 */
export function selectProduct(params: IBaseParams) {
  return get(`/cms/loan-order-record/PSVA/confirm/product/${params.innerOrderNo}`);
}

/**
 * 选择产品&车辆评估 评估查询
 */
export function getProductEvaluation(params: IBaseParams) {
  return get(`/cms/loan-order-record/PSVA/eval/price/${params.innerOrderNo}`);
}

/**
 * 机构进件 状态查询
 */
export function getInnerOrderStatus(params: IBaseParams) {
  return get(`/cms/loan-order-record/IP/status/query/${params.innerOrderNo}`);
}

/**
 * 资方进件 审批状态查询
 */
export function getFunderOrderStatus(params: IBaseParams) {
  return get(`/cms/loan-order-record/FUNDER_AP/status/query/${params.innerOrderNo}`);
}

/**
 * 资方进件 签署地址查询
 */
export function getFunderSignUrl(params: IBaseParams) {
  return get(`/cms/loan-order-record/FUNDER_AP/sign/url/query/${params.innerOrderNo}`);
}

/**
 * 资方进件 签署状态查询
 */
export function getFunderSignStatus(params: IBaseParams) {
  return get(`/cms/loan-order-record/FUNDER_AP/sign/status/query/${params.innerOrderNo}`);
}

/**
 * 资方绑卡合同签署 签署状态查询
 */
export function getFunderCardSignStatus(params: IBaseParams) {
  return get(`/cms/loan-order-record/FUNDER_CARD/sign/status/query/${params.innerOrderNo}`);
}

/**
 * 资方绑卡合同签署 获取签署地址
 */
export function getFunderCardSignUrl(params: IBaseParams) {
  return get(`/cms/loan-order-record/FUNDER_CARD/sign/url/query/${params.innerOrderNo}`);
}

/**
 * 德易绑卡 获取验证码
 */
export function getDeyiCardBindCode(params: IBaseParams) {
  return get(`/cms/loan-order-record/DEYI_CARD_BIND/code/get/${params.innerOrderNo}`);
}

/**
 * 德易绑卡 输入验证码后提交
 */
export function submitDeyiCardBindCode(data: IBaseParams & { code: string }) {
  return post(`/cms/loan-order-record/DEYI_CARD_BIND/code/commit`, data);
}

/**
 * 德易绑卡 获取绑卡状态
 */
export function getDeyiCardBindStatus(params: IBaseParams) {
  return get(`/cms/loan-order-record/DEYI_CARD_BIND/bind/status/query/${params.innerOrderNo}`);
}

/**
 * 德易签约 获取签署地址
 */
export function getDeyiSignUrl(params: IBaseParams) {
  return get(`/cms/loan-order-record/DEYI_SIGN/sign/url/query/${params.innerOrderNo}`);
}

/**
 * 德易签约 签署状态查询
 */
export function getDeyiSignStatus(params: IBaseParams) {
  return get(`/cms/loan-order-record/DEYI_SIGN/sign/status/query/${params.innerOrderNo}`);
}

/**
 * 德易签约 审批结果查询
 */
export function getDeyiSignApprovalStatus(params: IBaseParams) {
  return get(`/cms/loan-order-record/DEYI_SIGN/approval/status/query/${params.innerOrderNo}`);
}

/**
 * 面签 获取签署地址
 */
export function getFaceSignUrl(params: IBaseParams) {
  return get(`/cms/loan-order-record/FACE_SIGN/sign/url/query/${params.innerOrderNo}`);
}

/**
 * 面签 签署状态查询
 */
export function getFaceSignStatus(params: IBaseParams) {
  return get(`/cms/loan-order-record/FACE_SIGN/sign/status/query/${params.innerOrderNo}`);
}

/**
 * 行驶证识别
 */
export function getVehicleLicense(params: { url: string }) {
  return get(`/cms/ocr/recognizeVehicleLicense`, params);
}

/**
 * 机动车注册登记证识别
 */
export function getVehicleRegistration(params: { url: string }) {
  return get(`/cms/ocr/recognizeVehicleRegistration`, params);
}

/**
 * 身份证识别
 */
export function getIdCard(params: { url: string }) {
  return get(`/cms/ocr/recognizeIdCard`, params);
}
/**
 *
 * 德易GPS、保险、抵押信息提交
 *
 */
export function submitGpsMortgage(data: IBaseParams) {
  return post(`/cms/loan-order-record/mortgage/audit/commit/${data.innerOrderNo}`);
}
/**
 * 德易 GPS、保险、抵押信息提交查询
 */
export function getMortgageAuditResult(data: IBaseParams) {
  return get(`/cms/loan-order-record/query/mortgageAuditResult/${data.innerOrderNo}`);
}
/**
 *
 * 德易警邮回执提交
 *
 */
export function submitGPSPoliceReceipt(data: IBaseParams) {
  return post(`/cms/loan-order-record/police/PostalAckCommit/${data.innerOrderNo}`);
}
/**
 * 德易警邮回执结果查询
 */
export function getGPSPoliceReceipt(data: IBaseParams) {
  return get(`/cms/loan-order-record/police/PostalAckResult/${data.innerOrderNo}`);
}
/**
 * 德易还款计划查询
 */
export function getRepaymentSchedule(data: IBaseParams) {
  return get(`/cms/loan-order-record/query/returnMoney/plan/${data.innerOrderNo}`);
}
/**
 * 德易 发起放款状态查询
 */
export function sendLoanStatus(data: IBaseParams) {
  return get(`/cms/loan-order-record/query/giveMoney/status/${data.innerOrderNo}`);
}

/**
 * GPS安装进度查询
 */
export function sendGPSInstallStatus(data: IBaseParams) {
  return get(`/cms/loan-order-record/gps/install/work/status/${data.innerOrderNo}`);
}
/**
 * 获取gps支付状态
 * @param clueId
 * @returns
 */
export function getGPSPaymentStatus(data: IGPSParams) {
  return post(`/cms/contract/record/gps/payStatus`, data);
}
// 面签签约重新签署

export function onQuerySign(data: IBaseParams) {
  return get(`/cms/loan-order-record/DEYI_SIGN/sign/commit/${data.innerOrderNo}`);
}
