/**
 * 对象路径工具函数
 * 用于处理嵌套对象的 get 和 set 操作
 */

/**
 * 根据路径获取对象的值
 * @param obj 目标对象
 * @param path 路径字符串，支持点号分隔，如 'vehicleInfo.bodyColor'
 * @param defaultValue 默认值
 * @returns 获取到的值或默认值
 */
export function getValueByPath(obj: any, path: string, defaultValue?: any): any {
  if (!obj || typeof obj !== 'object' || !path) {
    return defaultValue;
  }

  // 将路径字符串转换为数组
  const pathArray = path.split('.').filter((key) => key.length > 0);

  let result = obj;
  for (const key of pathArray) {
    if (result === null || result === undefined || typeof result !== 'object') {
      return defaultValue;
    }
    result = result[key];
  }

  return result !== undefined ? result : defaultValue;
}

/**
 * 根据路径设置对象的值
 * @param obj 目标对象
 * @param path 路径字符串，支持点号分隔，如 'vehicleInfo.bodyColor'
 * @param value 要设置的值
 * @returns 修改后的对象
 */
export function setValueByPath(obj: any, path: string, value: any): any {
  if (!obj || typeof obj !== 'object' || !path) {
    return obj;
  }

  // 将路径字符串转换为数组
  const pathArray = path.split('.').filter((key) => key.length > 0);

  if (pathArray.length === 0) {
    return obj;
  }

  let current = obj;

  // 遍历路径，创建不存在的中间对象
  for (let i = 0; i < pathArray.length - 1; i++) {
    const key = pathArray[i];

    if (current[key] === null || current[key] === undefined || typeof current[key] !== 'object') {
      current[key] = {};
    }

    current = current[key];
  }

  // 设置最终的值
  const lastKey = pathArray[pathArray.length - 1];
  current[lastKey] = value;

  return obj;
}

/**
 * 检查路径是否为嵌套路径（包含点号）
 * @param path 路径字符串
 * @returns 是否为嵌套路径
 */
export function isNestedPath(path: string): boolean {
  return typeof path === 'string' && path.includes('.');
}

/**
 * 初始化嵌套对象结构
 * @param obj 目标对象
 * @param paths 路径数组
 * @returns 初始化后的对象
 */
export function initializeNestedPaths(obj: any, paths: string[]): any {
  if (!obj || typeof obj !== 'object') {
    obj = {};
  }

  paths.forEach((path) => {
    if (isNestedPath(path)) {
      const pathArray = path.split('.').filter((key) => key.length > 0);
      let current = obj;

      // 创建嵌套对象结构，但不覆盖已存在的值
      for (let i = 0; i < pathArray.length - 1; i++) {
        const key = pathArray[i];
        if (
          current[key] === undefined ||
          current[key] === null ||
          typeof current[key] !== 'object'
        ) {
          current[key] = {};
        }
        current = current[key];
      }
    }
  });

  return obj;
}

/**
 * 删除嵌套路径的值
 * @param obj 目标对象
 * @param path 路径字符串
 * @returns 修改后的对象
 */
export function deleteValueByPath(obj: any, path: string): any {
  if (!obj || typeof obj !== 'object' || !path) {
    return obj;
  }

  const pathArray = path.split('.').filter((key) => key.length > 0);

  if (pathArray.length === 0) {
    return obj;
  }

  if (pathArray.length === 1) {
    delete obj[pathArray[0]];
    return obj;
  }

  let current = obj;

  // 遍历到倒数第二层
  for (let i = 0; i < pathArray.length - 1; i++) {
    const key = pathArray[i];

    if (current[key] === null || current[key] === undefined || typeof current[key] !== 'object') {
      return obj; // 路径不存在，无需删除
    }

    current = current[key];
  }

  // 删除最终的值
  const lastKey = pathArray[pathArray.length - 1];
  delete current[lastKey];

  return obj;
}
