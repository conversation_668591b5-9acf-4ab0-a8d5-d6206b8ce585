import { reactive } from 'vue';
import { getFieldData, getFieldConfig } from '@/api/dashboard/deyi';
import { debounce } from 'lodash-es';
import { Fields } from '@/components/DrawerOrder/types';

const data = reactive({} as Partial<Fields>);
const options = reactive({} as Record<string, Array<{ label: string; value: string }>>);

const getData = debounce(async (innerOrderNo: string) => {
  try {
    // 获取表单数据
    const { data: fieldData } = await getFieldData({
      innerOrderNo,
      fieldName: Object.keys(options),
    });

    if (Array.isArray(fieldData)) {
      fieldData.forEach((item: any) => {
        if (item.fieldName in data) {
          data[item.fieldName] = item.fieldValue;
        }
      });
    }
  } catch (e) {
    console.error('获取数据失败:', e);
  }
}, 500);

const getOptions = debounce(async () => {
  try {
    // 获取枚举选项
    const { data } = await getFieldConfig({
      fieldName: Object.keys(options),
    });

    if (Array.isArray(data)) {
      data.forEach((item: any) => {
        if (item.fieldName in options) {
          let op = [] as { label: string; value: string }[];
          try {
            op = item.fieldOptions
              ? JSON.parse(item.fieldOptions).map((o: any) => ({
                  label: o,
                  value: o,
                }))
              : [];
          } catch {}
          options[item.fieldName] = op;
        }
      });
    }
  } catch (e) {
    console.error('获取枚举失败:', e);
  }
}, 500);

export function useBaseData(opts: { keys: string[]; innerOrderNo: string }) {
  opts.keys.forEach((key) => {
    data[key] = null;
    options[key] = [];
  });

  return {
    data,
    options,
    getData: getData.bind(null, opts.innerOrderNo),
    getOptions: getOptions.bind(null),
  };
}
