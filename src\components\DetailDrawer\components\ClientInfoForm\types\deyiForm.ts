export interface IUpdateCustomerModalFormModel {
  clueId: number | null;
  personalInfo: {
    id: number | null;
    educationLevel: number | null;
    maritalStatus: number | null;
    // childCount: number | null;
    residenceAddress: string;
    houseType: number | null;
    company: string;
    companyCityCode: string | null | number;
    companyAddress: string;
    companyPhone: string | null;
    companyNature: number | null;
    monthlyIncome: number | null;
    contactName1: string;
    contactPhone1: string | null;
    contactRelation1: number | null;
    contactName2: string;
    contactPhone2: string | null;
    contactRelation2: number | null;
    clueId: number | null;
    idCardNumber: string | null;
    name: string | null;
    cityCode: number | string | null;
    cityName: string | null;
    provinceName: string | null;
    provinceCode: number | string | null;
    mateName: string | null; //配偶姓名
    mateMobile: string | null; //配偶手机号
    mateIdCardNumber: string | null; //配偶身份证号
    // mateWorkCompany: string | null; //配偶工作单位
    // mateWorkCompanyCity: string | null; //配偶工作单位地址
    // mateWorkCompanyAddress: string | null; //配偶工作单位详情地址
    loanPurpose: string | number | null; //申请用途新增
    workType: string | null; //从事行业种类
    occupation: string | null; //职业
  };
  certificateInfo: {
    id: number | null;
    idCardImgUrl: string;
    idCardImgBackUrl: string;
    drivingLicenseImgUrl: string;
    // drivingLicenseImgBackUrl: string;
    paperDriverLicenseImgUrl: string;
    // paperDriverLicenseImgBackUrl: string;
    // bankCardImgUrl: string;
    // bankCardBackUrl: string;
    odographImgUrl: string; //里程表 新增
    vehicleRegistrationImgUrl: string | string[]; //车辆登记证(2) 新增
    vehicleStatusImgUrl: string; //机动车状态（12123截图）
    clueId: number | null;
  };
  otherDocuments: {
    id: number | null;
    vehiclePhotosLeft45: string | null;
    vehiclePhotoRight45: string | null;
    vehiclePhotoPeopleCar: string | null; //人车合影
    // vehiclePhotoFront: string | null;    //车头全景
    clueId: number | null;
  };
}
//大专；本科；硕士；其他；博士；高中；小学；初中；中专
export const educationLevelOptions = [
  { label: '大专', value: 1 },
  { label: '本科', value: 2 },
  { label: '硕士', value: 3 },
  { label: '其他', value: 4 },
  { label: '博士', value: 5 },
  { label: '高中', value: 6 },
  { label: '小学', value: 7 },
  { label: '初中', value: 8 },
  { label: '中专', value: 9 },
];

export const maritalStatusOptions = [
  { label: '已婚', value: 1 },
  { label: '未婚', value: 2 },
  { label: '离婚', value: 3 },
  { label: '丧偶', value: 4 },
];

export const houseTypeOptions = [
  { label: '自置', value: 1 },
  { label: '按揭', value: 2 },
  { label: '亲属楼宇', value: 3 },
  { label: '集体宿舍', value: 4 },
  { label: '租房', value: 5 },
  { label: '共有住宅', value: 6 },
  { label: '其他', value: 7 },
  { label: '自有', value: 8 },
  { label: '借住', value: 9 },
];
//政府机构；事业单位；国有企业；金融机构；三资企业；私营企业；上市公司；其他
export const companyNatureOptions = [
  { label: '政府机构', value: 1 },
  { label: '事业单位', value: 2 },
  { label: '国有企业', value: 3 },
  { label: '金融机构', value: 4 },
  { label: '三资企业', value: 5 },
  { label: '私营企业', value: 6 },
  { label: '上市公司', value: 7 },
  { label: '其他', value: 8 },
];
export const loanPurposeOptions = [
  { label: '经营周转', value: 1 },
  { label: '购车', value: 2 },
  { label: '婚庆', value: 3 },
  { label: '旅游', value: 4 },
  { label: '教育', value: 5 },
  { label: '装修', value: 6 },
];
export const monthlyIncomeOptions = [
  { label: '5000以下', value: 1 },
  { label: '5000-10000', value: 2 },
  { label: '10000-15000', value: 3 },
  { label: '15000-20000', value: 4 },
  { label: '20000以上', value: 5 },
];
//直系亲属联系人 父母；其他亲属；子女
export const contactRelationOptions = [
  { label: '父母', value: 1 },
  { label: '其他亲属', value: 2 },
  { label: '子女', value: 3 },
];
//联系人 父母；其他亲属；兄弟姐妹；子女
export const contactRelationOptions1 = [
  { label: '父母', value: 1 },
  { label: '其他亲属', value: 2 },
  { label: '兄弟姐妹', value: 3 },
  { label: '子女', value: 4 },
];
export const transferCountOptions = [
  { label: '0', value: '0' },
  { label: '1', value: '1' },
  { label: '2', value: '2' },
  { label: '3', value: '3' },
  { label: '4', value: '4' },
  { label: '5', value: '5' },
  { label: '6', value: '6' },
  { label: '7', value: '7' },
  { label: '8', value: '8' },
  { label: '9', value: '9' },
  { label: '10', value: '10' },
];

export const vehicleStatusOptions = [
  { label: '全款', value: 1 },
  { label: '按揭已结清', value: 2 },
  { label: '按揭未结清', value: 3 },
];

export const vehicleAuthStatusOptions = [
  { label: '一致', value: 1 },
  { label: '不一致', value: 0 },
];

export const threeElementAuthStatusOptions = [
  { label: '一致', value: 1 },
  { label: '不一致', value: 0 },
];
export const childCountOptions = [
  { label: '0', value: '0' },
  { label: '1', value: '1' },
  { label: '2', value: '2' },
  { label: '3', value: '3' },
  { label: '4', value: '4' },
  { label: '5', value: '5' },
];
//从事行业种类 农、林、牧、渔业；采矿业；制造业；电力、人力、燃气及水生产和供应业；建筑业；批发和零售业；交通运输、仓储和邮储业；住宿和餐饮业；信息传输、软件和信息技术服务；金融业；房地产业；租赁和商务服务业；科学研究和技术服务业；水利、环境和公共设施管理业；居民服务、修理和其他服务业；教育；卫生和社会工作；文化、体育和娱乐业；公共管理、社会保障和社会组织；国际组织；未知
export const workTypeOptions = [
  { label: '农、林、牧、渔业', value: 1 },
  { label: '采矿业', value: 2 },
  { label: '制造业', value: 3 },
  { label: '电力、燃气及水生产和供应业', value: 4 },
  { label: '建筑业', value: 5 },
  { label: '批发和零售业', value: 6 },
  { label: '交通运输、仓储和邮政业', value: 7 },
  { label: '住宿和餐饮业', value: 8 },
  { label: '信息传输、软件和信息技术服务业', value: 9 },
  { label: '金融业', value: 10 },
  { label: '房地产业', value: 11 },
  { label: '租赁和商务服务业', value: 12 },
  { label: '科学研究和技术服务业', value: 13 },
  { label: '水利、环境和公共设施管理业', value: 14 },
  { label: '居民服务、修理和其他服务业', value: 15 },
  { label: '教育', value: 16 },
  { label: '卫生和社会工作', value: 17 },
  { label: '文化、体育和娱乐业', value: 18 },
  { label: '公共管理、社会保障和社会组织', value: 19 },
  { label: '国际组织', value: 20 },
  { label: '未知', value: 21 },
];
//职业：国家公务员；专业技术人员；职员；企业管理人员；工人；农民；学生；现役军人；自有职业者；个体经营者；无业人员；退（离）休人员；其他
export const occupationOptions = [
  { label: '国家公务员', value: 1 },
  { label: '专业技术人员', value: 2 },
  { label: '职员', value: 3 },
  { label: '企业管理人员', value: 4 },
  { label: '工人', value: 5 },
  { label: '农民', value: 6 },
  { label: '学生', value: 7 },
  { label: '现役军人', value: 8 },
  { label: '自有职业者', value: 9 },
  { label: '个体经营者', value: 10 },
  { label: '无业人员', value: 11 },
  { label: '退（离）休人员', value: 12 },
  { label: '其他', value: 13 },
];
