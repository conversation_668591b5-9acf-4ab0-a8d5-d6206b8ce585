<template>
  <div>
    <n-modal v-model:show="showModal" :mask-closable="false" :close-on-esc="false">
      <n-card
        class="w-[900px]"
        title="补充客户资料"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header-extra>
          <!-- 关闭 -->
          <n-icon class="cursor-pointer" size="20" @click="closeModal(true)">
            <CloseOutlined />
          </n-icon>
        </template>
        <n-form
          ref="formRef"
          label-placement="left"
          size="medium"
          :model="formModel"
          :rules="formRules"
          label-width="120px"
        >
          <div class="h-[70vh] overflow-y-auto">
            <!-- 车辆信息 -->
            <n-divider title-placement="left">
              <div
                class="flex items-center cursor-pointer text-lg text-blue-500 font-bold"
                @click="vehicleInfoExpanded = !vehicleInfoExpanded"
              >
                <span>车辆信息</span>
                <n-icon size="16" class="ml-2">
                  <DownOutlined v-if="vehicleInfoExpanded" />
                  <RightOutlined v-else />
                </n-icon>
              </div>
            </n-divider>
            <n-collapse-transition :show="vehicleInfoExpanded">
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="车身颜色" path="vehicleInfo.bodyColor">
                    <n-input
                      v-model:value="formModel.vehicleInfo.bodyColor"
                      placeholder="请输入车身颜色"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="内饰颜色" path="vehicleInfo.interiorColor">
                    <n-input
                      v-model:value="formModel.vehicleInfo.interiorColor"
                      placeholder="请输入内饰颜色"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="上牌城市" path="vehicleInfo.licenseCity">
                    <CitySelect
                      v-model="formModel.vehicleInfo.licenseCity"
                      change-on-select
                      :multiple="false"
                      ref="citySelectRef"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="出厂日期" path="vehicleInfo.factoryDate">
                    <n-date-picker
                      v-model:value="formModel.vehicleInfo.factoryDate"
                      type="date"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="过户次数" path="vehicleInfo.transferCount">
                    <n-select
                      v-model:value="formModel.vehicleInfo.transferCount"
                      :options="transferCountOptions"
                      placeholder="请选择过户次数"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="车辆状态" path="vehicleInfo.vehicleStatus">
                    <n-select
                      v-model:value="formModel.vehicleInfo.vehicleStatus"
                      :options="vehicleStatusOptions"
                      placeholder="请选择车辆状态"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="人车认证状态" path="vehicleInfo.carOfPersonStatus">
                    <n-select
                      v-model:value="formModel.vehicleInfo.carOfPersonStatus"
                      :options="vehicleAuthStatusOptions"
                      placeholder="请选择人车认证状态"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item
                    label="车牌号"
                    path="vehicleInfo.licensePlateNumber"
                    :rule="formRules.licensePlateNumber"
                  >
                    <n-input
                      v-model:value="formModel.vehicleInfo.licensePlateNumber"
                      placeholder="请输入车牌号"
                      :maxlength="8"
                    />
                  </n-form-item>
                </n-grid-item>
                <!-- <n-grid-item>
                  <n-form-item
                    label="车300报告"
                    path="vehicleInfo.carThreeHundred"
                    label-placement="top"
                    label-width="120px"
                    class="pl-[50px]"
                    :rule="formRules.carThreeHundred"
                  >
                    <div class="flex flex-col gap-[10px]">
                      <n-input
                        class="w-[100%]"
                        v-model:value="formModel.vehicleInfo.carThreeHundred"
                        placeholder="请输入车300报告"
                      />
                      <UploadFile
                        ref="uploadFileRef"
                        v-model:fileList="formModel.vehicleInfo.carThreeHundred"
                        accept=".pdf,.doc,.docx"
                        width="340px"
                        height="40px"
                        :max-size="20"
                        :max-count="2"
                        :multiple="false"
                        :customShowPreview="false"
                        :single="true"
                        :on-remove="removeLicenseReport"
                        list-type="image"
                        :show-preview-button="true"
                        :on-preview="
                          () => {
                            uploadFileRef?.handlePreview?.(formModel.vehicleInfo.carThreeHundred);
                          }
                        "
                      >
                        <template #uploadButton="{ canUpload, uploading }">
                          <n-button
                            class="h-[34px] ml-[4px]"
                            text
                            :disabled="!canUpload || !!formModel.vehicleInfo.carThreeHundred"
                            :loading="uploading"
                            type="primary"
                          >
                            上传报告
                          </n-button>
                        </template>
                      </UploadFile>
                    </div>
                  </n-form-item>
                </n-grid-item> -->
              </n-grid>
            </n-collapse-transition>

            <!-- 个人信息 -->
            <n-divider title-placement="left">
              <div
                class="flex items-center cursor-pointer text-lg text-blue-500 font-bold"
                @click="personalInfoExpanded = !personalInfoExpanded"
              >
                <span>个人信息</span>
                <n-icon size="16" class="ml-2">
                  <DownOutlined v-if="personalInfoExpanded" />
                  <RightOutlined v-else />
                </n-icon>
              </div>
            </n-divider>
            <n-collapse-transition :show="personalInfoExpanded">
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="姓名" path="personalInfo.name">
                    <n-input v-model:value="formModel.personalInfo.name" placeholder="请输入姓名" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item v-if="ShowCity" label="所在城市" path="personalInfo.cityCode">
                    <CitySelect
                      v-model="formModel.personalInfo.cityCode"
                      change-on-select
                      :multiple="false"
                      ref="citySelectRef1"
                      key="cityCode"
                      @update="handleCityUpdate"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="三要素认证状态:" path="personalInfo.threeElementsStatus">
                    <n-select
                      v-model:value="formModel.personalInfo.threeElementsStatus"
                      :options="threeElementAuthStatusOptions"
                      placeholder="请选择三要素认证状态"
                      :disabled="true"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="电子邮箱" path="personalInfo.email" :rule="formRules.email">
                    <n-input
                      v-model:value="formModel.personalInfo.email"
                      placeholder="请输入电子邮箱"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="学历" path="personalInfo.educationLevel">
                    <n-select
                      v-model:value="formModel.personalInfo.educationLevel"
                      :options="educationLevelOptions"
                      placeholder="请选择学历"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="婚姻状况" path="personalInfo.maritalStatus">
                    <n-select
                      v-model:value="formModel.personalInfo.maritalStatus"
                      :options="maritalStatusOptions"
                      placeholder="请选择婚姻状况"
                      @update:value="handleMaritalStatusChange"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="子女数量" path="personalInfo.childCount">
                    <n-input-number
                      v-model:value="formModel.personalInfo.childCount"
                      :min="0"
                      :max="5"
                      placeholder="请输入子女数量"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="居住地址" path="personalInfo.residenceAddress">
                    <n-input
                      v-model:value="formModel.personalInfo.residenceAddress"
                      placeholder="请输入居住地址"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="房产类型" path="personalInfo.houseType">
                    <n-select
                      v-model:value="formModel.personalInfo.houseType"
                      :options="houseTypeOptions"
                      placeholder="请选择房产类型"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="工作单位" path="personalInfo.company">
                    <n-input
                      v-model:value="formModel.personalInfo.company"
                      placeholder="请输入工作单位"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="单位地址" path="personalInfo.companyAddress">
                    <n-input
                      v-model:value="formModel.personalInfo.companyAddress"
                      placeholder="请输入单位地址"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="单位电话" path="personalInfo.companyPhone">
                    <n-input
                      :value="formModel.personalInfo.companyPhone"
                      placeholder="请输入单位电话"
                      @input="(val) => (formModel.personalInfo.companyPhone = val ? val : null)"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="单位性质" path="personalInfo.companyNature">
                    <n-select
                      v-model:value="formModel.personalInfo.companyNature"
                      :options="companyNatureOptions"
                      placeholder="请选择单位性质"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="月收入" path="personalInfo.monthlyIncome">
                    <n-select
                      v-model:value="formModel.personalInfo.monthlyIncome"
                      :options="monthlyIncomeOptions"
                      placeholder="请选择月收入"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="联系人姓名" path="personalInfo.contactName">
                    <n-input
                      v-model:value="formModel.personalInfo.contactName"
                      placeholder="请输入联系人姓名"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item
                    label="联系人号码"
                    path="personalInfo.contactPhone"
                    :rule="formRules.contactPhone"
                  >
                    <n-input
                      :value="formModel.personalInfo.contactPhone"
                      placeholder="请输入联系人号码"
                      @input="(val) => (formModel.personalInfo.contactPhone = val ? val : null)"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="联系人关系" path="personalInfo.contactRelation">
                    <n-select
                      v-model:value="formModel.personalInfo.contactRelation"
                      :options="_contactRelationOptions"
                      placeholder="请选择联系人关系"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="身份证号" path="personalInfo.idCardNumber">
                    <n-input
                      v-model:value="formModel.personalInfo.idCardNumber"
                      placeholder="请输入身份证号"
                      maxlength="18"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-transition>

            <!-- 证照信息 -->
            <n-divider title-placement="left">
              <div
                class="flex items-center cursor-pointer text-lg text-blue-500 font-bold"
                @click="certificateInfoExpanded = !certificateInfoExpanded"
              >
                <span>证照信息</span>
                <n-icon size="16" class="ml-2">
                  <DownOutlined v-if="certificateInfoExpanded" />
                  <RightOutlined v-else />
                </n-icon>
              </div>
            </n-divider>
            <n-collapse-transition :show="certificateInfoExpanded">
              <n-grid :cols="2" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="身份证正面" path="certificateInfo.idCardImgUrl">
                    <UploadFile
                      v-model:fileList="formModel.certificateInfo.idCardImgUrl"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="身份证背面" path="certificateInfo.idCardImgBackUrl">
                    <UploadFile
                      v-model:fileList="formModel.certificateInfo.idCardImgBackUrl"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="行驶证正面" path="certificateInfo.drivingLicenseImgUrl">
                    <UploadFile
                      v-model:fileList="formModel.certificateInfo.drivingLicenseImgUrl"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="行驶证背面" path="certificateInfo.drivingLicenseImgBackUrl">
                    <UploadFile
                      v-model:fileList="formModel.certificateInfo.drivingLicenseImgBackUrl"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="驾驶执照正面" path="certificateInfo.paperDriverLicenseImgUrl">
                    <UploadFile
                      v-model:fileList="formModel.certificateInfo.paperDriverLicenseImgUrl"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item
                    label="驾驶执照背面"
                    path="certificateInfo.paperDriverLicenseImgBackUrl"
                  >
                    <UploadFile
                      v-model:fileList="formModel.certificateInfo.paperDriverLicenseImgBackUrl"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="银行卡正面" path="certificateInfo.bankCardImgUrl">
                    <UploadFile
                      v-model:fileList="formModel.certificateInfo.bankCardImgUrl"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="银行卡背面" path="certificateInfo.bankCardBackUrl">
                    <UploadFile
                      v-model:fileList="formModel.certificateInfo.bankCardBackUrl"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-transition>

            <!-- 其他资料 -->
            <n-divider title-placement="left">
              <div
                class="flex items-center cursor-pointer text-lg text-blue-500 font-bold"
                @click="otherDocumentsExpanded = !otherDocumentsExpanded"
              >
                <span>其他资料</span>
                <n-icon size="16" class="ml-2">
                  <DownOutlined v-if="otherDocumentsExpanded" />
                  <RightOutlined v-else />
                </n-icon>
              </div>
            </n-divider>
            <n-collapse-transition :show="otherDocumentsExpanded">
              <n-grid :cols="1" :x-gap="24">
                <n-grid-item>
                  <n-form-item label="车辆照片" path="otherDocuments.vehiclePhotos">
                    <UploadFile
                      :file-list="
                        formModel.otherDocuments.vehiclePhotos.map((item) => ({
                          url: item,
                          id: item,
                        }))
                      "
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                      :max-count="8"
                      multiple
                      @update:file-list="
                        (val) =>
                          (formModel.otherDocuments.vehiclePhotos = val.map((item) => item.url))
                      "
                    />
                    <n-text depth="3" class="ml-4 text-xs">
                      包括以下照片：1. 左前45°；2.右后45°；3. 前挡风玻璃车架号；4.
                      车辆仪表盘（含公里数）；5. 铭牌；6. 中控台；7. 前排车辆座椅、后排车辆座椅；8.
                      人车合影
                    </n-text>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="通讯录视频或截图" path="otherDocuments.contactListMedias">
                    <UploadFile
                      :file-list="
                        formModel.otherDocuments.contactListMedias.map((item) => ({
                          url: item,
                          id: item,
                        }))
                      "
                      accept=".jpg,.jpeg,.png,.pdf,.mp4"
                      :max-size="200"
                      :max-count="10"
                      multiple
                      @update:file-list="
                        (val) =>
                          (formModel.otherDocuments.contactListMedias = val.map((item) => item.url))
                      "
                    />
                    <n-text depth="3" class="ml-4 text-xs">
                      要求清晰录制10联系人名字和号码/10张截屏也可以。视频内容含姓名+点开显示完整电话号码。
                    </n-text>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="交管12123视频或截图" path="otherDocuments.traffic12123Medias">
                    <UploadFile
                      :file-list="
                        formModel.otherDocuments.traffic12123Medias.map((item) => ({
                          url: item,
                          id: item,
                        }))
                      "
                      accept=".jpg,.jpeg,.png,.pdf,.mp4"
                      :max-size="200"
                      :max-count="4"
                      multiple
                      @update:file-list="
                        (val) =>
                          (formModel.otherDocuments.traffic12123Medias = val.map(
                            (item) => item.url
                          ))
                      "
                    />
                    <n-text depth="3" class="ml-4 text-xs">
                      视频或者截图，包括4张照片（年检到期、电子驾照、违章明细、抵押状态）/1个视频
                    </n-text>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="流水" path="otherDocuments.bankStatements">
                    <UploadFile
                      :file-list="
                        formModel.otherDocuments.bankStatements.map((item) => ({
                          url: item,
                          id: item,
                        }))
                      "
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                      :max-count="5"
                      multiple
                      @update:file-list="
                        (val) =>
                          (formModel.otherDocuments.bankStatements = val.map((item) => item.url))
                      "
                    />
                    <n-text depth="3" class="ml-4 text-xs">
                      微信/银行/支付宝都可以（导出半年的PDF版本）
                    </n-text>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="开户行信息" path="otherDocuments.bankAccountInfos">
                    <UploadFile
                      :file-list="
                        formModel.otherDocuments.bankAccountInfos.map((item) => ({
                          url: item,
                          id: item,
                        }))
                      "
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                      :max-count="5"
                      multiple
                      @update:file-list="
                        (val) =>
                          (formModel.otherDocuments.bankAccountInfos = val.map((item) => item.url))
                      "
                    />
                    <n-text depth="3" class="ml-4 text-xs">
                      银行卡APP截屏(包含卡号和一类卡字样)
                    </n-text>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="交强险和商业险" path="otherDocuments.insurances">
                    <UploadFile
                      :file-list="
                        formModel.otherDocuments.insurances.map((item) => ({
                          url: item,
                          id: item,
                        }))
                      "
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="10"
                      :max-count="5"
                      multiple
                      @update:file-list="
                        (val) => (formModel.otherDocuments.insurances = val.map((item) => item.url))
                      "
                    />
                    <n-text depth="3" class="ml-4 text-xs">
                      有限期必须大于一月；商业险投保人、被保险人和车主必须为主贷人；商业险必须有三者（50万以上）和车损（大于贷款额的九成）
                    </n-text>
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-transition>
          </div>
          <div style="margin-top: 24px; text-align: center">
            <n-space justify="center">
              <n-button @click="closeModal(true)">取消</n-button>
              <n-button type="primary" @click="handleSubmit" :loading="submitLoading">
                提交
              </n-button>
            </n-space>
          </div>
        </n-form>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, computed } from 'vue';
  import { CloseOutlined, DownOutlined, RightOutlined } from '@vicons/antd';
  import { cloneDeep } from 'lodash-es';
  import CitySelect from '@/components/CitySelect/index.vue';
  import UploadFile from '@/components/UploadFile/index.vue';
  import { updateCustomerInfoApi, getCustomerInfoApi } from '@/api/client';
  import { validEmail, validPhone, validIdCard, validHttps } from '@/utils/formValidationRules';
  import { promisifyDialog } from '@/utils/nativeUtils';
  // 教育程度枚举
  const educationLevelOptions = [
    { label: '高中', value: 1 },
    { label: '大专', value: 2 },
    { label: '本科', value: 3 },
    { label: '研究生', value: 4 },
    { label: '博士', value: 5 },
  ];

  // 婚姻状况枚举
  const maritalStatusOptions = [
    { label: '未婚', value: 1 },
    { label: '已婚', value: 2 },
  ];

  // 房产类型枚举
  const houseTypeOptions = [
    { label: '商品房', value: 1 },
    { label: '自建房', value: 2 },
    { label: '别墅', value: 3 },
    { label: '写字楼', value: 4 },
    { label: '公寓', value: 5 },
  ];

  // 公司性质枚举
  const companyNatureOptions = [
    { label: '企事业单位', value: 1 },
    { label: '民营企业', value: 2 },
    { label: '工商个体户', value: 3 },
    { label: '外资公司', value: 4 },
    { label: '其他', value: 5 },
  ];

  // 月收入枚举
  const monthlyIncomeOptions = [
    { label: '5000以下', value: 1 },
    { label: '5000-10000', value: 2 },
    { label: '10000-15000', value: 3 },
    { label: '15000-20000', value: 4 },
    { label: '20000以上', value: 5 },
  ];

  // 联系人关系枚举
  const contactRelationOptions = [
    { label: '父/母', value: 1 },
    { label: '配偶', value: 2 },
  ];

  // 过户次数枚举
  const transferCountOptions = [
    { label: '1', value: '1' },
    { label: '2', value: '2' },
    { label: '3', value: '3' },
    { label: '4', value: '4' },
    { label: '5', value: '5' },
    { label: '6', value: '6' },
    { label: '7', value: '7' },
    { label: '8', value: '8' },
    { label: '9', value: '9' },
    { label: '10', value: '10' },
  ];

  // 车辆状态枚举
  const vehicleStatusOptions = [
    { label: '全款', value: 1 },
    { label: '按揭已结清', value: 2 },
    { label: '按揭未结清', value: 3 },
  ];

  // 人车认证状态枚举
  const vehicleAuthStatusOptions = [
    { label: '一致', value: 1 },
    { label: '不一致', value: 0 },
  ];

  // 三要素认证状态枚举
  const threeElementAuthStatusOptions = [
    { label: '一致', value: 1 },
    { label: '不一致', value: 0 },
  ];

  interface IUpdateCustomerModalFormModel {
    clueId: number | null;
    vehicleInfo: {
      id: number | null;
      bodyColor: string;
      interiorColor: string;
      licenseCity: string;
      factoryDate: string | null;
      transferCount: string;
      vehicleStatus: number | null;
      carOfPersonStatus: number | null;
      clueId: number | null;
      carThreeHundred: string | null;
      licensePlateNumber: string;
    };
    personalInfo: {
      id: number | null;
      threeElementsStatus: number | null;
      email: string;
      educationLevel: number | null;
      maritalStatus: number | null;
      childCount: number | null;
      residenceAddress: string;
      houseType: number | null;
      company: string;
      companyAddress: string;
      companyPhone: string | null;
      companyNature: number | null;
      monthlyIncome: number | null;
      contactName: string;
      contactPhone: string | null;
      contactRelation: number | null;
      clueId: number | null;
      idCardNumber: string | null;
      name: string | null;
      cityCode: number | string | null;
      cityName: string | null;
      provinceName: string | null;
      provinceCode: number | string | null;
    };
    certificateInfo: {
      id: number | null;
      idCardImgUrl: string;
      idCardImgBackUrl: string;
      drivingLicenseImgUrl: string;
      drivingLicenseImgBackUrl: string;
      paperDriverLicenseImgUrl: string;
      paperDriverLicenseImgBackUrl: string;
      bankCardImgUrl: string;
      bankCardBackUrl: string;
      clueId: number | null;
    };
    otherDocuments: {
      id: number | null;
      vehiclePhotos: string[];
      contactListMedias: string[];
      traffic12123Medias: string[];
      bankStatements: string[];
      bankAccountInfos: string[];
      insurances: string[];
      clueId: number | null;
    };
  }

  const emit = defineEmits(['update:modelValue', 'success']);

  const showModal = ref(false);
  const submitLoading = ref(false);
  const formRef = ref();
  const citySelectRef = ref();
  const citySelectRef1 = ref();
  // 表单验证规则，都是非必填，填了必须符合格式
  const formRules = {
    // 邮箱
    email: [
      {
        validator: (_rule, value) => {
          if (value) {
            return validEmail(_rule, value);
          }
          return true;
        },
        trigger: ['blur', 'input', 'change'],
      },
    ],
    // 联系人号码
    contactPhone: [
      {
        validator: (_rule, value) => {
          if (value) {
            return validPhone(_rule, value);
          }
          return true;
        },
        trigger: ['blur', 'input', 'change'],
      },
    ],
    // 身份证号
    idCardNumber: [
      {
        validator: (_rule, value) => {
          if (value) {
            return validIdCard(_rule, value);
          }
          return true;
        },
        trigger: ['blur', 'input', 'change'],
      },
    ],
    licensePlateNumber: [
      {
        validator: (_rule, value) => {
          if (value && value?.length < 7) {
            return new Error('请输入正确的车牌号');
          }
          return true;
        },
        trigger: ['blur', 'input', 'change'],
      },
    ],
    // carThreeHundred: [
    //   {
    //     validator: (_rule, value) => {
    //       if (value) {
    //         return validHttps(_rule, value);
    //       }
    //       return true;
    //     },
    //     message: '输入格式有误，请输入http/https的地址',
    //     trigger: ['blur', 'input', 'change'],
    //   },
    // ],
  };

  // 控制各个部分的展开收起状态
  const vehicleInfoExpanded = ref(true);
  const personalInfoExpanded = ref(true);
  const certificateInfoExpanded = ref(true);
  const otherDocumentsExpanded = ref(true);

  const DEFAULT_FORM_MODEL: IUpdateCustomerModalFormModel = {
    clueId: null,
    vehicleInfo: {
      id: null,
      bodyColor: '',
      interiorColor: '',
      licenseCity: '',
      factoryDate: null,
      transferCount: '',
      vehicleStatus: null,
      carOfPersonStatus: null,
      clueId: null,
      carThreeHundred: null,
      licensePlateNumber: '',
    },
    personalInfo: {
      id: null,
      name: '',
      cityCode: null,
      cityName: '',
      provinceName: '',
      provinceCode: null,
      threeElementsStatus: null,
      email: '',
      educationLevel: null,
      maritalStatus: null,
      childCount: null,
      residenceAddress: '',
      houseType: null,
      company: '',
      companyAddress: '',
      companyPhone: null,
      companyNature: null,
      monthlyIncome: null,
      contactName: '',
      contactPhone: null,
      contactRelation: null,
      clueId: null,
      idCardNumber: null,
    },
    certificateInfo: {
      id: null,
      idCardImgUrl: '',
      idCardImgBackUrl: '',
      drivingLicenseImgUrl: '',
      drivingLicenseImgBackUrl: '',
      paperDriverLicenseImgUrl: '',
      paperDriverLicenseImgBackUrl: '',
      bankCardImgUrl: '',
      bankCardBackUrl: '',
      clueId: null,
    },
    otherDocuments: {
      id: null,
      vehiclePhotos: [],
      contactListMedias: [],
      traffic12123Medias: [],
      bankStatements: [],
      bankAccountInfos: [],
      insurances: [],
      clueId: null,
    },
  };

  const formModel = ref(cloneDeep(DEFAULT_FORM_MODEL));
  const ShowCity = computed(() => {
    return citySelectRef?.value?.options?.length > 0;
  });
  const uploadFileRef = ref();
  watch(
    () => showModal.value,
    async (val) => {
      if (val) {
        await getData(formModel.value.clueId);
      } else {
        formModel.value = cloneDeep(DEFAULT_FORM_MODEL);
      }
    },
    { immediate: true }
  );

  // 联系人关系选项，根据婚姻状况选择联动
  const _contactRelationOptions = computed(() => {
    const maritalStatus = formModel.value.personalInfo.maritalStatus;

    if (maritalStatus === 1) {
      return [contactRelationOptions[0]];
    }

    if (maritalStatus === 2) {
      return [contactRelationOptions[1]];
    }

    return [];
  });

  function handleMaritalStatusChange(val: number) {
    formModel.value.personalInfo.contactRelation = null;
  }

  // 提交表单
  async function handleSubmit() {
    //调用API接口
    try {
      await formRef.value?.validate();
      submitLoading.value = true;
      let licenseCity = formModel.value?.vehicleInfo?.licenseCity
        ? citySelectRef.value?.findPathByValue(
            citySelectRef.value.options,
            formModel.value.vehicleInfo.licenseCity
          )
        : [];
      let licenseCityName = licenseCity?.[licenseCity.length - 1]?.label;
      formModel.value.vehicleInfo.licenseCityName = licenseCityName;
      await updateCustomerInfoApi(formModel.value);

      window.$message.success('客户资料提交成功');
      emit('success');
      closeModal();
    } catch (error) {
      console.error('表单验证失败:', error);
      window.$message.error('提交失败，请检查表单内容');
    } finally {
      submitLoading.value = false;
    }
  }

  function getData(clueId: number) {
    return getCustomerInfoApi({ clueId }).then((res) => {
      if (res.code === 200 && res.data) {
        const data = res.data;
        const val = cloneDeep(formModel.value);
        Object.keys(data).forEach((key) => {
          if (key !== 'clueId' && typeof data[key] === 'object' && data[key] !== null) {
            Object.keys(data[key]).forEach((subKey) => {
              if (data[key][subKey] !== null && data[key][subKey] !== undefined) {
                val[key][subKey] = data[key][subKey];
              }
            });
          }
        });
        formModel.value = val;
        formModel.value.vehicleInfo.licenseCity = data.vehicleInfo?.licenseCity
          ? Number(data.vehicleInfo.licenseCity)
          : null;
        formModel.value.personalInfo.cityCode = data.personalInfo?.cityCode
          ? Number(data.personalInfo.cityCode)
          : null;
        formModel.value.personalInfo.provinceCode = data.personalInfo?.provinceCode
          ? Number(data.personalInfo.provinceCode)
          : null;
        console.log(formModel.value, 'formModel.value');
      }
    });
  }

  function openModal(clueId: number | string) {
    formModel.value.clueId = typeof clueId === 'string' ? parseInt(clueId) : clueId;
    setTimeout(() => {
      showModal.value = true;
    }, 0);
  }

  async function closeModal(intercept = false) {
    if (intercept === true) {
      const result = await promisifyDialog(window.$dialog.warning)({
        title: '提示',
        content: '您确定要关闭吗？',
        positiveText: '确定',
        negativeText: '取消',
        maskClosable: false,
      });

      if (result.source !== 'positive') {
        return;
      }
    }

    showModal.value = false;
    // 重置表单
    formModel.value = cloneDeep(DEFAULT_FORM_MODEL);
  }
  function handleCityUpdate(data: Array<any> | null) {
    formModel.value.personalInfo.cityName = data?.[1]?.label ?? '';
    formModel.value.personalInfo.provinceName = data?.[0]?.label ?? '';
    formModel.value.personalInfo.provinceCode = data?.[0]?.value ?? null;
  }
  async function removeLicenseReport() {
    const result = await promisifyDialog(window.$dialog.warning)({
      title: '提示',
      content: '您确定删除文件吗？',
      positiveText: '确定',
      negativeText: '取消',
      maskClosable: false,
    });
    if (result.source === 'positive') {
      formModel.value.vehicleInfo.carThreeHundred = null;
    }
    return result.source === 'positive';
  }
  defineExpose({
    openModal,
    closeModal,
  });
</script>

<style lang="less" scoped></style>
