import { reactive } from 'vue';
import { getFieldData, getFieldConfig } from '@/api/dashboard/deyi';
import { Fields } from '@/components/DrawerOrder/types';
import { isEmpty } from '@/utils/is';
import { latestOnly } from '@/utils';

const cache = new Map<string, string>();

// 每个组件实例创建独立的 latestOnly 包装，保证只返回最后一次结果
const getDataDebounce = latestOnly(async (innerOrderNo: string) => {
  try {
    // 获取表单数据
    const { data: fieldData } = await getFieldData({
      innerOrderNo,
      fieldName: Array.from(cache.keys()),
    });

    if (Array.isArray(fieldData)) {
      return fieldData;
    }
  } catch (e) {
    console.error('获取数据失败:', e);
  }
});

const getOptionsDebounce = latestOnly(async () => {
  try {
    // 获取枚举选项
    const { data: configData } = await getFieldConfig({
      fieldName: Array.from(cache.keys()),
    });

    if (Array.isArray(configData)) {
      return configData;
    }
  } catch (e) {
    console.error('获取枚举失败:', e);
  }
});

export function useBaseData(opts: { keys: string[]; innerOrderNo: string }) {
  // 每个组件实例创建独立的状态
  const data = reactive({ serverTime: '' } as Partial<Fields>);
  const options = reactive({} as Record<string, Array<{ label: string; value: string }>>);

  // 初始化 keys
  opts.keys.forEach((key) => {
    data[key] = null;
    options[key] = [];
    if (!cache.has(key)) {
      cache.set(key, key);
    }
  });

  const getData = async () => {
    try {
      const fieldData = await getDataDebounce(opts.innerOrderNo);

      if (Array.isArray(fieldData)) {
        fieldData.forEach((item: any) => {
          if (item.fieldName in data) {
            data[item.fieldName] = item.fieldValue || null;
          }
        });
      }
    } catch (e) {
      console.error('获取数据失败:', e);
    }
  };

  const getOptions = async () => {
    try {
      const configData = await getOptionsDebounce();
      if (Array.isArray(configData)) {
        configData.forEach((item: any) => {
          if (item.fieldName in options) {
            let op = [] as { label: string; value: string }[];
            try {
              op = item.fieldOptions
                ? JSON.parse(item.fieldOptions).map((o: any) => ({
                    label: o,
                    value: o,
                  }))
                : [];
            } catch (e) {
              console.error('枚举解析失败:', e);
            }
            options[item.fieldName] = op;
          }

          // 回显默认值
          if (item.fieldName in data) {
            const defaultValue = item.fieldDefaultValue;

            const val = data[item.fieldName];
            if (isEmpty(val) && defaultValue) {
              data[item.fieldName] = defaultValue;
            }
          }
        });
      }
    } catch (e) {
      console.error('获取枚举失败:', e);
    }
  };

  return {
    data,
    options,
    getData,
    getOptions,
  };
}
