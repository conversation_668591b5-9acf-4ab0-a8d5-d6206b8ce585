<template>
  <div class="form-examples">
    <n-card title="基础动态表单示例" class="mb-4">
      <DynamicForm
        ref="basicFormRef"
        :config="basicFormConfig"
        v-model="basicFormData"
        @submit="handleBasicSubmit"
        @field-change="handleFieldChange"
      />
    </n-card>

    <n-card title="复杂联动表单示例" class="mb-4">
      <DynamicForm
        ref="linkageFormRef"
        :config="linkageFormConfig"
        v-model="linkageFormData"
        @submit="handleLinkageSubmit"
      />
    </n-card>

    <n-card title="表单构建器示例" class="mb-4">
      <DynamicForm
        ref="builderFormRef"
        :config="builderFormConfig"
        v-model="builderFormData"
        @submit="handleBuilderSubmit"
      />
    </n-card>

    <n-card title="表单操作示例" class="mb-4">
      <n-space>
        <n-button @click="handleValidateBasic">校验基础表单</n-button>
        <n-button @click="handleResetBasic">重置基础表单</n-button>
        <n-button @click="handleSetBasicData">设置基础表单数据</n-button>
        <n-button @click="handleToggleField">切换字段显示</n-button>
        <n-button @click="handleAddField">动态添加字段</n-button>
      </n-space>
    </n-card>

    <n-card title="表单数据" class="mb-4">
      <n-tabs>
        <n-tab-pane name="basic" tab="基础表单数据">
          <pre>{{ JSON.stringify(basicFormData, null, 2) }}</pre>
        </n-tab-pane>
        <n-tab-pane name="linkage" tab="联动表单数据">
          <pre>{{ JSON.stringify(linkageFormData, null, 2) }}</pre>
        </n-tab-pane>
        <n-tab-pane name="builder" tab="构建器表单数据">
          <pre>{{ JSON.stringify(builderFormData, null, 2) }}</pre>
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { NCard, NTabs, NTabPane, NSpace, NButton, useMessage } from 'naive-ui';
  import { DynamicForm, validators } from '../index';
  import { FiledOptions } from '../index';

  import type { DynamicFormConfig, DynamicFormInstance } from '../src/types/dynamicForm';

  const message = useMessage();

  // 表单引用
  const basicFormRef = ref<DynamicFormInstance>();
  const linkageFormRef = ref<DynamicFormInstance>();
  const builderFormRef = ref<DynamicFormInstance>();

  // 表单数据
  const basicFormData = ref({});
  const linkageFormData = ref({});
  const builderFormData = ref({});

  // 基础表单配置
  const basicFormConfig: DynamicFormConfig = {
    labelWidth: 120,
    columns: 12,
    fields: [
      {
        field: 'name',
        label: '姓名',
        type: FiledOptions.INPUT,
        required: true,
        placeholder: '请输入姓名',
        rules: [
          validators.required('姓名不能为空'),
          validators.length(2, 20, '姓名长度应在2-20个字符之间'),
        ],
      },
      {
        field: 'email',
        label: '邮箱',
        type: FiledOptions.INPUT,
        required: true,
        placeholder: '请输入邮箱',
        rules: [validators.required('邮箱不能为空'), validators.email('请输入正确的邮箱格式')],
      },
      {
        field: 'phone',
        label: '手机号',
        type: FiledOptions.INPUT,
        placeholder: '请输入手机号',
        rules: [validators.phone('请输入正确的手机号')],
      },
      {
        field: 'age',
        label: '年龄',
        type: FiledOptions.NUMBER,
        componentProps: {
          min: 1,
          max: 120,
        },
        rules: [validators.numberRange(1, 120, '年龄应在1-120之间')],
      },
      {
        field: 'gender',
        label: '性别',
        type: FiledOptions.RADIO,
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' },
        ],
      },
      {
        field: 'hobbies',
        label: '爱好',
        type: FiledOptions.CHECKBOX_GROUP,
        options: [
          { label: '读书', value: 'reading' },
          { label: '运动', value: 'sports' },
          { label: '音乐', value: 'music' },
          { label: '旅行', value: 'travel' },
        ],
      },
      {
        field: 'birthday',
        label: '生日',
        type: FiledOptions.DATE,
        componentProps: {
          type: 'date',
          clearable: true,
        },
      },
      {
        field: 'bio',
        label: '个人简介',
        type: FiledOptions.TEXTAREA,
        span: 24,
        componentProps: {
          rows: 4,
          maxlength: 500,
          showCount: true,
        },
      },
    ],
    onSubmit: async (data) => {
      message.success('基础表单提交成功！');
      console.log('基础表单数据:', data);
    },
  };

  // 联动表单配置
  const linkageFormConfig: DynamicFormConfig = {
    labelWidth: 120,
    columns: 12,
    fields: [
      {
        field: 'userType',
        label: '用户类型',
        type: FiledOptions.SELECT,
        required: true,
        options: [
          { label: '个人用户', value: 'personal' },
          { label: '企业用户', value: 'enterprise' },
        ],
      },
      {
        field: 'personalName',
        label: '个人姓名',
        type: FiledOptions.INPUT,
        required: true,
      },
      {
        field: 'idCard',
        label: '身份证号',
        type: FiledOptions.INPUT,
        rules: [validators.idCard()],
      },
      {
        field: 'companyName',
        label: '公司名称',
        type: FiledOptions.INPUT,
        required: true,
      },
      {
        field: 'businessLicense',
        label: '营业执照号',
        type: FiledOptions.INPUT,
      },
      {
        field: 'contactPerson',
        label: '联系人',
        type: FiledOptions.INPUT,
      },
      {
        field: 'hasContract',
        label: '是否签署合同',
        type: FiledOptions.SWITCH,
        span: 24,
      },
      {
        field: 'contractFile',
        label: '合同文件',
        type: FiledOptions.UPLOAD,
        span: 24,
        componentProps: {
          accept: '.pdf,.doc,.docx',
          max: 1,
        },
      },
    ],
    onSubmit: async (data) => {
      message.success('联动表单提交成功！');
      console.log('联动表单数据:', data);
    },
  };

  // 手动创建表单配置（替代FormTemplates.userRegister()）
  const builderFormConfig = {
    labelWidth: 100,
    columns: 24,
    fields: [
      {
        field: 'username',
        label: '用户名',
        type: FiledOptions.INPUT,
        required: true,
        rules: [validators.required('请输入用户名'), validators.length(3, 20)],
      },
      {
        field: 'password',
        label: '密码',
        type: FiledOptions.PASSWORD,
        required: true,
        rules: [validators.required('请输入密码'), validators.length(6, 20)],
      },
      {
        field: 'confirmPassword',
        label: '确认密码',
        type: FiledOptions.PASSWORD,
        required: true,
        rules: [validators.required('请确认密码'), validators.confirmPassword('password')],
      },
      {
        field: 'email',
        label: '邮箱',
        type: FiledOptions.INPUT,
        required: true,
        rules: [validators.required('请输入邮箱'), validators.email()],
      },
      {
        field: 'phone',
        label: '手机号',
        type: FiledOptions.INPUT,
        rules: [validators.phone()],
      },
      {
        field: 'agreement',
        label: '同意用户协议',
        type: FiledOptions.CHECKBOX,
        required: true,
        span: 24,
        rules: [
          validators.custom((value) => {
            return value ? null : '请同意用户协议';
          }),
        ],
      },
    ],
    onSubmit: async (data: any) => {
      message.success('构建器表单提交成功！');
      console.log('构建器表单数据:', data);
    },
  };

  // 事件处理
  const handleBasicSubmit = (data: any) => {
    console.log('基础表单提交:', data);
  };

  const handleLinkageSubmit = (data: any) => {
    console.log('联动表单提交:', data);
  };

  const handleBuilderSubmit = (data: any) => {
    console.log('构建器表单提交:', data);
  };

  const handleFieldChange = (field: string, value: any, formData: any) => {
    console.log('字段变化:', { field, value, formData });
  };

  // 表单操作方法
  const handleValidateBasic = async () => {
    const isValid = await basicFormRef.value?.validate();
    message.info(isValid ? '表单校验通过' : '表单校验失败');
  };

  const handleResetBasic = () => {
    basicFormRef.value?.resetForm();
    message.info('表单已重置');
  };

  const handleSetBasicData = () => {
    const sampleData = {
      name: '张三',
      email: '<EMAIL>',
      phone: '13800138000',
      age: 25,
      gender: 'male',
      hobbies: ['reading', 'sports'],
      birthday: new Date('1998-01-01').getTime(),
      bio: '这是一个示例用户的个人简介。',
    };
    basicFormRef.value?.setFormData(sampleData);
    message.success('已设置示例数据');
  };

  const handleToggleField = () => {
    const field = basicFormConfig.fields.find((f) => f.field === 'bio');
    if (field) {
      field.hidden = !field.hidden;
      message.info(field.hidden ? '已隐藏个人简介字段' : '已显示个人简介字段');
    }
  };

  const handleAddField = () => {
    const newField = {
      field: `dynamic_${Date.now()}`,
      label: '动态字段',
      type: FiledOptions.INPUT,
      placeholder: '这是一个动态添加的字段',
      span: 12,
    };
    basicFormConfig.fields.push(newField);
    message.success('已添加动态字段');
  };
</script>

<style scoped>
  .form-examples {
    padding: 20px;
  }

  .mb-4 {
    margin-bottom: 16px;
  }

  pre {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 300px;
    overflow: auto;
  }
</style>
