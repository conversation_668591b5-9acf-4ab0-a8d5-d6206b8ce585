<script setup lang="tsx">
  import { DynamicForm } from '@/components/Form';
  import { useDeyiForm } from '@/components/DetailDrawer/components/ClientInfoForm/composables/useDeyiForm';
  const { linkageFormRef, linkageFormData, linkageFormConfig } = useDeyiForm();
  defineExpose({
    linkageFormData,
  });
</script>
<template>
  <DynamicForm ref="linkageFormRef" v-model:config="linkageFormConfig" v-model="linkageFormData" />
</template>
<style lang="less" scoped></style>
