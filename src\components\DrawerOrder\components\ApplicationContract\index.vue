<template>
  <n-space vertical>
    <!-- 车辆资料上传 -->
    <VehicleUpload :inner-order-no="props.innerOrderNo" />
    <!-- 德易进件 -->
    <InstitutionalEntry :inner-order-no="props.innerOrderNo" />
    <!-- 资方进件 -->
    <CapitalProviderEntry :inner-order-no="props.innerOrderNo" />
    <!-- 资方绑卡合同签署 -->
    <ContractSigning :inner-order-no="props.innerOrderNo" />
    <!-- 德易绑卡 -->
    <DeYiCardBinding :inner-order-no="props.innerOrderNo" />
    <!-- 德易签约 -->
    <DeYiSigning :inner-order-no="props.innerOrderNo" />
  </n-space>
</template>

<script setup lang="tsx">
  import VehicleUpload from './components/VehicleUpload/index.vue';
  import InstitutionalEntry from './components/InstitutionalEntry/index.vue';
  import CapitalProviderEntry from './components/CapitalProviderEntry/index.vue';
  import ContractSigning from './components/ContractSigning/index.vue';
  import DeYiCardBinding from './components/DeYiCardBinding/index.vue';
  import DeYiSigning from './components/DeYiSigning/index.vue';

  const props = defineProps<{ innerOrderNo: string }>();
</script>

<style lang="less" scoped></style>
