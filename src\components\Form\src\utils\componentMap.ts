import type { Component } from 'vue';
import {
  NInput,
  NInputNumber,
  NSelect,
  NCheckbox,
  NCheckboxGroup,
  NRadio,
  NRadioButton,
  NRadioGroup,
  NSwitch,
  NDatePicker,
  NTimePicker,
  NSlider,
  NRate,
  NUpload,
  NColorPicker,
  NCascader,
  NTreeSelect,
  NTransfer,
  NMention,
  NDynamicInput,
  NDynamicTags,
  NDivider,
  NAutoComplete,
  NInputGroup,
  NDataTable,
} from 'naive-ui';
import UploadFile from '@/components/UploadFile/index.vue';
import { FiledOptions, type FieldType } from '../types/dynamicForm';
import type { ComponentType } from '../types/index';

/**
 * 字段类型到组件类型的映射
 */
export const fieldTypeToComponentMap: Record<FieldType, ComponentType> = {
  [FiledOptions.INPUT]: 'NInput',
  [FiledOptions.TEXTAREA]: 'NInput',
  [FiledOptions.PASSWORD]: 'NInput',
  [FiledOptions.NUMBER]: 'NInputNumber',
  [FiledOptions.SELECT]: 'NSelect',
  [FiledOptions.RADIO]: 'NRadioGroup',
  [FiledOptions.CHECKBOX]: 'NCheckbox',
  [FiledOptions.CHECKBOX_GROUP]: 'NCheckboxGroup',
  [FiledOptions.SWITCH]: 'NSwitch',
  [FiledOptions.DATE]: 'NDatePicker',
  [FiledOptions.DATE_RANGE]: 'NDatePicker',
  [FiledOptions.TIME]: 'NTimePicker',
  [FiledOptions.TIME_RANGE]: 'NTimePicker',
  [FiledOptions.DATETIME]: 'NDatePicker',
  [FiledOptions.DATETIME_RANGE]: 'NDatePicker',
  [FiledOptions.UPLOAD]: 'NUpload',
  [FiledOptions.RATE]: 'NRate',
  [FiledOptions.SLIDER]: 'NSlider',
  [FiledOptions.COLOR_PICKER]: 'NColorPicker',
  [FiledOptions.CASCADER]: 'NCascader',
  [FiledOptions.TREE_SELECT]: 'NTreeSelect',
  [FiledOptions.TRANSFER]: 'NTransfer',
  [FiledOptions.MENTION]: 'NMention',
  [FiledOptions.DYNAMIC_INPUT]: 'NDynamicInput',
  [FiledOptions.DYNAMIC_TAGS]: 'NDynamicTags',
  [FiledOptions.DIVIDER]: 'NDivider',
  [FiledOptions.CUSTOM]: 'NRender',
  [FiledOptions.DATA_TABLE]: 'NDataTable',
};

/**
 * 组件类型到实际组件的映射
 */
export const componentTypeToComponentMap: Record<ComponentType, Component> = {
  NInput,
  NInputGroup,
  NInputPassword: NInput,
  NInputSearch: NInput,
  NInputTextArea: NInput,
  NInputNumber,
  NInputCountDown: NInput,
  NSelect,
  NTreeSelect,
  NRadioButtonGroup: NRadioGroup,
  NRadioGroup,
  NRadio,
  NRadioButton,
  NCheckbox,
  NCheckboxGroup,
  NAutoComplete,
  NCascader,
  NDatePicker,
  NMonthPicker: NDatePicker,
  NRangePicker: NDatePicker,
  NWeekPicker: NDatePicker,
  NTimePicker,
  NSwitch,
  NStrengthMeter: NInput,
  NUpload: UploadFile,
  NIconPicker: NInput,
  NRender: NInput,
  NSlider,
  NRate,
  NDivider,
  NColorPicker,
  NTransfer,
  NMention,
  NDynamicInput,
  NDynamicTags,
  NDataTable,
};

/**
 * 字段类型的默认属性配置
 */
export const fieldTypeDefaultProps: Record<FieldType, Record<string, any>> = {
  [FiledOptions.INPUT]: {
    clearable: true,
    placeholder: '请输入',
  },
  [FiledOptions.TEXTAREA]: {
    type: 'textarea',
    clearable: true,
    placeholder: '请输入',
    autosize: { minRows: 3, maxRows: 6 },
  },
  [FiledOptions.PASSWORD]: {
    type: 'password',
    showPasswordOn: 'click',
    placeholder: '请输入密码',
  },
  [FiledOptions.NUMBER]: {
    clearable: true,
    placeholder: '请输入数字',
  },
  [FiledOptions.SELECT]: {
    clearable: true,
    placeholder: '请选择',
    filterable: true,
  },
  [FiledOptions.RADIO]: {
    // 单选组默认属性
  },
  [FiledOptions.CHECKBOX]: {
    // 复选框默认属性
  },
  [FiledOptions.CHECKBOX_GROUP]: {
    // 复选框组默认属性
  },
  [FiledOptions.SWITCH]: {
    // 开关默认属性
  },
  [FiledOptions.DATE]: {
    type: 'date',
    clearable: true,
    placeholder: '请选择日期',
  },
  [FiledOptions.DATE_RANGE]: {
    type: 'daterange',
    clearable: true,
    placeholder: ['开始日期', '结束日期'],
  },
  [FiledOptions.TIME]: {
    clearable: true,
    placeholder: '请选择时间',
  },
  [FiledOptions.TIME_RANGE]: {
    type: 'timerange',
    clearable: true,
    placeholder: ['开始时间', '结束时间'],
  },
  [FiledOptions.DATETIME]: {
    type: 'datetime',
    clearable: true,
    placeholder: '请选择日期时间',
  },
  [FiledOptions.DATETIME_RANGE]: {
    type: 'datetimerange',
    clearable: true,
    placeholder: ['开始时间', '结束时间'],
  },
  [FiledOptions.UPLOAD]: {
    multiple: false,
    max: 1,
  },
  [FiledOptions.RATE]: {
    allowHalf: true,
  },
  [FiledOptions.SLIDER]: {
    // 滑块默认属性
  },
  [FiledOptions.COLOR_PICKER]: {
    // 颜色选择器默认属性
  },
  [FiledOptions.CASCADER]: {
    clearable: true,
    placeholder: '请选择',
    filterable: true,
  },
  [FiledOptions.TREE_SELECT]: {
    clearable: true,
    placeholder: '请选择',
    filterable: true,
  },
  [FiledOptions.TRANSFER]: {
    // 穿梭框默认属性
  },
  [FiledOptions.MENTION]: {
    // 提及默认属性
  },
  [FiledOptions.DYNAMIC_INPUT]: {
    // 动态输入默认属性
  },
  [FiledOptions.DYNAMIC_TAGS]: {
    // 动态标签默认属性
  },
  [FiledOptions.DIVIDER]: {
    // 分割线默认属性
  },
  [FiledOptions.CUSTOM]: {
    // 自定义组件默认属性
  },
};

/**
 * 获取字段类型对应的组件
 */
export function getComponentByFieldType(fieldType: FieldType): Component {
  const componentType = fieldTypeToComponentMap[fieldType];
  return componentTypeToComponentMap[componentType];
}

/**
 * 获取字段类型对应的组件类型
 */
export function getComponentTypeByFieldType(fieldType: FieldType): ComponentType {
  return fieldTypeToComponentMap[fieldType];
}

/**
 * 获取字段类型的默认属性
 */
export function getDefaultPropsByFieldType(fieldType: FieldType): Record<string, any> {
  return { ...fieldTypeDefaultProps[fieldType] };
}

/**
 * 注册自定义组件
 */
export function registerComponent(componentType: ComponentType, component: Component) {
  componentTypeToComponentMap[componentType] = component;
}

/**
 * 注册自定义字段类型
 */
export function registerFieldType(
  fieldType: string,
  componentType: ComponentType,
  defaultProps?: Record<string, any>
) {
  // @ts-ignore
  fieldTypeToComponentMap[fieldType] = componentType;
  if (defaultProps) {
    // @ts-ignore
    fieldTypeDefaultProps[fieldType] = defaultProps;
  }
}

/**
 * 获取所有支持的字段类型
 */
export function getSupportedFieldTypes(): FieldType[] {
  return Object.values(FiledOptions);
}

/**
 * 获取所有支持的组件类型
 */
export function getSupportedComponentTypes(): ComponentType[] {
  return Object.keys(componentTypeToComponentMap) as ComponentType[];
}

/**
 * 检查字段类型是否支持
 */
export function isFieldTypeSupported(fieldType: string): fieldType is FieldType {
  return Object.values(FiledOptions).includes(fieldType as FieldType);
}

/**
 * 检查组件类型是否支持
 */
export function isComponentTypeSupported(componentType: string): componentType is ComponentType {
  return componentType in componentTypeToComponentMap;
}

/**
 * 组件映射管理器
 */
export class ComponentMapManager {
  private static customComponents: Map<string, Component> = new Map();
  private static customFieldTypes: Map<
    string,
    { componentType: ComponentType; defaultProps?: Record<string, any> }
  > = new Map();

  /**
   * 注册自定义组件
   */
  static registerComponent(name: string, component: Component) {
    this.customComponents.set(name, component);
    // @ts-ignore
    componentTypeToComponentMap[name] = component;
  }

  /**
   * 注册自定义字段类型
   */
  static registerFieldType(
    fieldType: string,
    componentType: ComponentType,
    defaultProps?: Record<string, any>
  ) {
    this.customFieldTypes.set(fieldType, { componentType, defaultProps });
    // @ts-ignore
    fieldTypeToComponentMap[fieldType] = componentType;
    if (defaultProps) {
      // @ts-ignore
      fieldTypeDefaultProps[fieldType] = defaultProps;
    }
  }

  /**
   * 获取自定义组件
   */
  static getCustomComponent(name: string): Component | undefined {
    return this.customComponents.get(name);
  }

  /**
   * 获取自定义字段类型配置
   */
  static getCustomFieldType(fieldType: string) {
    return this.customFieldTypes.get(fieldType);
  }

  /**
   * 清除所有自定义注册
   */
  static clear() {
    this.customComponents.clear();
    this.customFieldTypes.clear();
  }
}

export default {
  fieldTypeToComponentMap,
  componentTypeToComponentMap,
  fieldTypeDefaultProps,
  getComponentByFieldType,
  getComponentTypeByFieldType,
  getDefaultPropsByFieldType,
  registerComponent,
  registerFieldType,
  getSupportedFieldTypes,
  getSupportedComponentTypes,
  isFieldTypeSupported,
  isComponentTypeSupported,
  ComponentMapManager,
};
