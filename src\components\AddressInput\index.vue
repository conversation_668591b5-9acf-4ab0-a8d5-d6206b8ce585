<template>
  <div class="w-full flex gap-[10px]">
    <n-select
      v-model:value="province"
      :placeholder="placeholders.province"
      :options="provinceOptions"
      clearable
      class="w-[100px]"
      :disabled="disabled"
    />
    <n-select
      v-model:value="city"
      :placeholder="placeholders.city"
      :options="cityOptions"
      clearable
      class="w-[100px]"
      :disabled="!province || disabled"
    />
    <n-select
      v-model:value="district"
      :placeholder="placeholders.district"
      :options="districtOptions"
      clearable
      class="w-[100px]"
      :disabled="!city || disabled"
    />
    <n-input
      v-model:value="detail"
      :placeholder="placeholders.detail"
      clearable
      :maxlength="detailMaxLength"
      style="flex: 1"
      :disabled="disabled"
    />
  </div>
</template>

<script setup lang="ts">
  import { onMounted, computed } from 'vue';
  import { useAddressOptions } from './useAddressOptions';
  import type { Options } from './useAddressOptions';

  interface AddressValue {
    province: string | null;
    city: string | null;
    district: string | null;
    detail: string;
  }

  const props = defineProps<{
    disabled?: boolean;
    detailMaxLength?: number;
    placeholders?: Partial<Record<'province' | 'city' | 'district' | 'detail', string>>;
  }>();

  const placeholders = computed(() => ({
    province: props.placeholders?.province ?? '省份',
    city: props.placeholders?.city ?? '城市',
    district: props.placeholders?.district ?? '区县',
    detail: props.placeholders?.detail ?? '详细地址',
  }));

  const detailMaxLength = computed(() => props.detailMaxLength ?? 50);

  const { options, getOptions } = useAddressOptions();

  const addressModel = defineModel<AddressValue>({
    required: true,
    default: () => ({ province: null, city: null, district: null, detail: '' }),
  });

  // 将父组件的 v-model 映射为本地可双向绑定的字段
  const province = computed<string | null>({
    get: () => (addressModel.value.province == null ? null : String(addressModel.value.province)),
    set: (val) => {
      const nextProvince = val == null ? null : String(val);
      addressModel.value = {
        ...addressModel.value,
        province: nextProvince,
        // 省份变更时重置下级
        city: null,
        district: null,
      };
    },
  });

  const city = computed<string | null>({
    get: () => (addressModel.value.city == null ? null : String(addressModel.value.city)),
    set: (val) => {
      const nextCity = val == null ? null : String(val);
      addressModel.value = {
        ...addressModel.value,
        city: nextCity,
        // 城市变更时重置区县
        district: null,
      };
    },
  });

  const district = computed<string | null>({
    get: () => (addressModel.value.district == null ? null : String(addressModel.value.district)),
    set: (val) => {
      addressModel.value = {
        ...addressModel.value,
        district: val == null ? null : String(val),
      };
    },
  });

  const detail = computed<string>({
    get: () => addressModel.value.detail,
    set: (val) => {
      addressModel.value = {
        ...addressModel.value,
        detail: val,
      };
    },
  });

  const provinceOptions = computed(() =>
    options.value.map(({ label, value }) => ({ label, value: String(value) }))
  );
  const cityOptions = computed(() => {
    const provinceCode = province.value;
    const p = options.value.find((item: Options) => String(item.value) === String(provinceCode));
    return (p?.children || []).map(({ label, value }) => ({ label, value: String(value) }));
  });
  const districtOptions = computed(() => {
    const provinceCode = province.value;
    const cityCode = city.value;
    const p = options.value.find((item: Options) => String(item.value) === String(provinceCode));
    const c = (p?.children || []).find((item: Options) => String(item.value) === String(cityCode));
    return (c?.children || []).map(({ label, value }) => ({ label, value: String(value) }));
  });

  onMounted(() => {
    getOptions();
  });
</script>
