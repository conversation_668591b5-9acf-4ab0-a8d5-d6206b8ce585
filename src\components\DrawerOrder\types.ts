import type { ListData } from '@/views/dashboard/workplace/orderManage/columns';
import { GpsInsuranceApproveStatus } from './enum';

export type DrawerOrderProps = {
  rowData?: ListData; // 可选的完整行数据
};

export type DrawerOrderEmits = {
  (e: 'close'): void;
};

// 阶段状态类型
export type StageStatus = 'pending' | 'ongoing' | 'completed' | 'returned' | 'failed';

// 子阶段类型
export interface SubStage {
  id: string | number;
  title: string;
  description?: string;
  status: StageStatus;
  createTime?: string; // 创建时间
}

// 阶段类型
export interface Stage {
  id: string | number;
  title: string;
  status: StageStatus;
  subStages?: SubStage[];
}

// API返回的扁平化数据类型
export interface StageNodeData {
  mainNodeCode: number; // 主节点值
  subNodeCode?: number; // 子节点值
  subNodeStatus?: number; // 0-未开始，1-进行中，2-已完成，3-失败，4-退回
  createTime?: string; // 创建时间
}

// 状态映射：API状态码 -> 组件状态
export const statusCodeToStageStatus = (code?: number): StageStatus => {
  switch (code) {
    case 2:
      return 'completed'; // 已完成
    case 1:
      return 'ongoing'; // 进行中
    case 3:
      return 'failed'; // 失败
    case 4:
      return 'returned'; // 退回
    case 0:
    default:
      return 'pending'; // 未开始
  }
};

// 主节点配置
export const mainNodeConfig: Record<number, string> = {
  1: '预审阶段',
  2: '进件签约',
  3: '面签阶段',
  4: 'GPS安装',
  5: '抵押阶段',
  6: '放款阶段',
};

// 子节点配置
export const subNodeConfig: Record<number, Record<number, string>> = {
  1: {
    // 预审阶段
    1: '预审生成',
    2: '授权书签署',
    3: '预审阶段',
  },
  2: {
    // 进件签约
    1: '选择产品&车辆评估',
    2: '德易进件',
    3: '资方进件',
    4: '资方绑卡合同签署',
    5: '德易绑卡',
    6: '德易签约',
  },
  3: {
    // 面签阶段
    1: '面签阶段',
  },
  4: {
    // GPS安装
    1: 'GPS安装要求',
    2: 'GPS安装',
  },
  5: {
    // 抵押阶段
    1: 'GPS、保险、抵押审批',
    2: '警邮回执',
  },
  6: {
    // 放款阶段
    1: '放款阶段',
  },
};
export const NodeCodeMap = {
  //预审阶段
  preTrial: {
    mainNode: 1,
    subNode: {
      //发起进件
      apply: 1,
      //授权书签署
      authorization: 2,
      //预审阶段
      preTrial: 3,
    },
  },
  //进行签约
  applicationContract: {
    mainNode: 2,
    subNode: {
      //选择产品&车辆评估
      selectProduct: 1,
      //德易进件
      institutionalEntry: 2,
      //资方进件
      capitalProviderEntry: 3,
      //资方绑卡合同签署
      funderCardSign: 4,
      //德易绑卡
      deyiCardBind: 5,
      //德易签约
      deyiSign: 6,
    },
  },
  //面签阶段
  faceToFaceSigning: {
    mainNode: 3,
    subNode: {
      //面签阶段
      faceToFaceSigning: 1,
    },
  },
  //GPS安装
  gpsInstallation: {
    mainNode: 4,
    subNode: {
      //GPS安装要求
      gpsInstallationRequirement: 1,
      //GPS安装
      gpsInstallation: 2,
    },
  },
  //抵押阶段
  mortgage: {
    mainNode: 5,
    subNode: {
      //GPS、保险、抵押审批
      gpsInsuranceMortgageApproval: 1,
      //警邮回执
      policePostReceipt: 2,
    },
  },
  //放款阶段
  loanDisbursement: {
    mainNode: 6,
    subNode: {
      //放款阶段
      loanDisbursement: 1,
    },
  },
};
// 转换函数：将扁平化数据转换为嵌套的Stage数组
export const transformStageData = (
  data: StageNodeData[],
  fieldData?: { gimaApprovalQuery?: string; gimaMortgageMaterial?: string }
): Stage[] => {
  // 按主节点分组
  const groupedByMain = data.reduce((acc, item) => {
    const mainCode = item.mainNodeCode;
    if (!acc[mainCode]) {
      acc[mainCode] = [];
    }
    acc[mainCode].push(item);
    return acc;
  }, {} as Record<number, StageNodeData[]>);

  // 构建Stage数组
  const stages: Stage[] = [];
  const mainNodeCodes = Object.keys(mainNodeConfig)
    .map(Number)
    .sort((a, b) => a - b);

  mainNodeCodes.forEach((mainCode) => {
    const subStageData = groupedByMain[mainCode] || [];
    const subStages: SubStage[] = [];

    // 构建子阶段
    if (subNodeConfig[mainCode]) {
      const subNodeCodes = Object.keys(subNodeConfig[mainCode])
        .map(Number)
        .sort((a, b) => a - b);

      subNodeCodes.forEach((subCode) => {
        // 对于抵押阶段的警邮回执节点（主节点5，子节点2），需要根据条件判断是否展示
        if (mainCode === 5 && subCode === 2) {
          // 警邮回执节点的展示条件：
          // gimaApprovalQuery 存在且不等于 Pending（待提交）状态码 且 gimaMortgageMaterial === '警邮回执'
          // 注意：gimaApprovalQuery 存储的是状态码（数字或字符串形式的数字），需要与枚举值比较
          const shouldShow =
            fieldData?.gimaApprovalQuery &&
            String(fieldData.gimaApprovalQuery) !== String(GpsInsuranceApproveStatus.Pending) &&
            fieldData?.gimaMortgageMaterial === '警邮回执';

          // 如果不符合条件，跳过此节点
          if (!shouldShow) {
            return;
          }
        }

        const subData = subStageData.find((d) => d.subNodeCode === subCode);
        const status = statusCodeToStageStatus(subData?.subNodeStatus);

        subStages.push({
          id: `${mainCode}-${subCode}`,
          title: subNodeConfig[mainCode][subCode],
          status,
          createTime: subData?.createTime,
        });
      });
    }

    // 计算主阶段状态
    let mainStatus: StageStatus = 'pending';
    if (subStages.length > 0) {
      const hasOngoing = subStages.some((s) => s.status === 'ongoing');
      const hasReturned = subStages.some((s) => s.status === 'returned');
      const hasFailed = subStages.some((s) => s.status === 'failed');
      const hasCompleted = subStages.some((s) => s.status === 'completed');
      const allCompleted = subStages.every((s) => s.status === 'completed');

      if (allCompleted) {
        mainStatus = 'completed';
      } else if (hasReturned) {
        mainStatus = 'returned';
      } else if (hasFailed) {
        mainStatus = 'failed';
      } else if (hasOngoing || hasCompleted) {
        // 有进行中或已完成的子节点，说明主阶段正在进行中
        mainStatus = 'ongoing';
      } else {
        mainStatus = 'pending';
      }
    }

    stages.push({
      id: mainCode,
      title: mainNodeConfig[mainCode],
      status: mainStatus,
      subStages: subStages.length > 0 ? subStages : undefined,
    });
  });

  return stages;
};

export type Fields = {
  serverTime: string; // 服务端时间
  preAuditIdCardReverse: string; // 身份证国徽面,单个文件
  preAuditIdCardFront: string; // 身份证人像面,单个文件
  preAuditName: string; // 姓名
  preAuditPhoneNum: string; // 手机号
  preAuditIdCardNum: string; // 身份证号
  preAuditApplyStatus: string; // 进件状态,code 200
  authorizationSignUrl: string; // 授权书签署地址
  authorizationSignStatus: string; // 授权书签署状态
  authorizationSignTime: string; // 签署时间,每次签署更新,用来做24小时过期判断
  preAuditPhaseCity: string; // 预审申请城市
  preAuditPhaseStatus: string; // 预审状态
  preAuditPhaseCause: string; // 授权书签署不通过原因
  psvaCarRegisCert: string; // 车辆登记证书
  psvaCarDrivCert: string; // 行驶证
  psvaLoanAmout: string; // 贷款金额
  psvaLoanTime: string; // 贷款期限
  psvaPrice: string; // 车辆售价
  psvaMileage: string; // 里程
  psvaScBox: string; // 变速箱类型
  psvaCiey: string; // 抵押城市
  psvaPublicType: string; // 申请人公牌类型
  psvaPublicPhoneNum: string; // 公牌公司电话
  psvaOrganCode: string; // 公牌组织机构代码
  psvaComName: string; // 公牌公司名称
  psvaComPerName: string; // 公牌公司法人姓名
  psvaComPerIdCard: string; // 公牌公司法人身份证
  psvaComRegiDate: string; // 公牌公司注册公司时间
  psvaDisplace: string; // 排量/L
  psvaFuelType: string; // 燃料类型
  psvaVehicleCate: string; // 车辆类别
  psvaPlateNumber: string; // 车牌号
  psvaCarColoe: string; // 车辆颜色
  psvaEngineNumber: string; // 发动机号
  psvaFirstRegisDate: string; // 首次上牌时间
  psvaVinCode: string; // 车辆VIN码
  psvaTransferTimes: string; // 过户次数
  psvaProductList: string; // 产品列表
  psvaSelectProduct: string; // 选择产品
  psvaCarEvalPrice: string; // 车辆评估价
  psvaRemainEvalTimes: string; // 剩余评估次数
  ipResProvince: string; // 申请人居住地址(省份)
  ipResCity: string; // 申请人居住地址(城市)
  ipResDistrict: string; // 申请人居住地址(区县)
  ipResDetail: string; // 申请人居住地址(详细地址)
  ipHasDrivingLicense: string; // 申请人有无驾照
  ipResStatus: string; // 申请人居住状况
  ipLocalResYears: string; // 申请人本地居住年限
  ipLocalResProof: string; // 申请人本地居住证明
  ipEduLevel: string; // 申请人学历情况
  ipEmpStatus: string; // 申请人就业状况
  ipIndustry: string; // 申请人所属行业
  ipEmployerName: string; // 申请人单位名称
  ipEmployerType: string; // 申请人单位性质
  ipWorkProvince: string; // 申请人单位地址(省份)
  ipWorkCity: string; // 申请人单位地址(城市)
  ipWorkDistrict: string; // 申请人单位地址(区县)
  ipWorkDetail: string; // 申请人单位地址(详细地址)
  ipJobPosition: string; // 申请人工作职务
  ipJobTitle: string; // 申请人工作职称
  ipNetMonthlyIncome: string; // 申请人税后月收入
  ipFirstWorkDate: string; // 申请人首次工作日期
  ipCurrentJobYears: string; // 申请人现单位工作年限
  ipWorkPhone: string; // 申请人现工作固话/手机
  ipLoanPurpose: string; // 申请用途
  ipCompProvince: string; // 公牌公司地址(省份)
  ipCompCity: string; // 公牌公司地址(城市)
  ipCompDistrict: string; // 公牌公司地址(区县)
  ipCompDetail: string; // 公牌公司地址(详细地址)
  ipMaritalStatus: string; // 婚姻状况
  ipSpouseName: string; // 配偶姓名
  ipSpouseId: string; // 配偶身份证号
  ipSpousePhone: string; // 配偶手机号
  ipEmergencyContact: string; // 紧急联系人
  ipIdStartDate: string; // 申请人身份证起始日期
  ipIdExpireDate: string; // 申请人身份证截止日期
  ipEthnicity: string; // 申请人民族
  ipHukouProvince: string; // 申请人户籍地址(省份)
  ipHukouCity: string; // 申请人户籍地址(城市)
  ipHukouDistrict: string; // 申请人户籍地址(区县)
  ipHukouDetail: string; // 申请人户籍地址(详细地址)
  ipAttachResStability: string; // 附件资料*居住稳定性凭证(1)
  ipAttachVehicleLicense: string; // 附件资料*行驶证
  ipAttachDrivingLicense: string; // 附件资料*驾驶证(1)
  ipAttachDashboard: string; // 附件资料*中控台
  ipAttachOdometer: string; // 附件资料*里程表
  ipAttachOrigRegForm: string; // 附件资料*原登记表(2)
  ipContractOrg: string; // 签约机构
  ipApprovalRemarks: string; // 审批备注
  ipApprovedAmount: string; // 批复金额
  ipApprovedMonthFee: string; // 批复月供
  ipApprovalResult: string; // 审批结果
  ipBusinessLicense: string; // 营业执照
  ipWealthProve: string; // 资产证明
  funderApContractFunder1: string; // 资方签约-资方1
  funderApContractFunder2: string; // 资方签约-资方2
  funderApApprovalResult: string; // 资方签约-审批结果
  funderApApprovalRemarks: string; // 资方签约-审批备注
  funderApAuthSignAddress: string; // 资方授权书签署地址
  funderApSignStatus: string; // 资方授权书签署状态
  funderApSignTime: string; // 资方授权书签署时间
  funderCardVehiclePhoto: string; // 人车合影
  funderCardPersonalInfoPage: string; // 个人信息页
  funderCardViolationPage: string; // 违章信息页
  funderCardGpsContract: string; // GPS安装合同
  funderCardFrontLeft45: string; // 车身左前方45°
  funderCardRearRight45: string; // 车身右后方45°
  funderCardAffiliationAgreementThree: string; // 挂靠协议3方
  funderCardEnginePanorama: string; // 发动机全景
  funderCardClientAffiliationCo: string; // 客户&挂靠公司
  funderCardShareholderResolution: string; // 股东会决议
  funderCardMortgageContractOne: string; // 抵押合同-公牌1
  funderCardMortgageContractTwo: string; // 抵押合同-公牌2
  funderCardAffiliationAgreementOne: string; // 挂靠协议1
  funderCardAffiliationAgreementTwo: string; // 挂靠协议2
  funderCardGpsContractCommit: string; // 手持确认函(GPS安装合同)
  funderCardBindSignAddress: string; // 资方绑卡签约合同签署地址
  funderCardSignResult: string; // 签署结果
  funderCardSignTime: string; // 签署时间
  deyiCardBindStatus: string; // 德易获取绑卡状态
  deyiSignAddress: string; // 德易绑卡签约合同签署地址
  deyiSignResult: string; // 德易签署结果
  deyiSignTime: string; // 德易签署时间
  deyiContractOrg: string; // 德易签约签约机构
  deyiApprovalResult: string; // 德易签约审批结果
  deyiApprovalRemarks: string; // 德易签约审批备注
  faceSignRemoteAddress: string; // 远程面签地址
  faceSignResult: string; // 签署结果
  faceSignTime: string; // 签署时间
  gpsInstRequResult: string; // 查询结果
  gpsProvider: string; // GPS供应商(,拼接)
  gpsContactName: string; // GPS联系人姓名
  gpsContactPhoneNum: string; // GPS联系人手机号码
  nowGpsProvider: string; // GPS供应商选择
  gpsProcInstallProvince: string; // 安装区域地址(省份)
  gpsProcInstallCity: string; // 安装区域地址(城市)
  gpsProcInstallDistrict: string; // 安装区域地址(区县)
  gpsProcInstallDetail: string; // 安装区域地址(详细地址)
  gpsProcInstallRequirement: string; // GPS安装要求
  gpsProcWiredCount: string; // 有线GPS数
  gpsProcWirelessCount: string; // 无线GPS数
  gpsProcWaterCount: string; // 水箱GPS数
  gpsProcPlan: string; // 安装进度查询
  gimaCommercialExpireDate: string; // 商业险到期日期
  gimaInsuranceCompany: string; // 保险公司
  gimaInsuranceChannel: string; // 保险渠道
  gimaCertificateNo: string; // 证书编号
  gimaMortgageDate: string; // 抵押日期
  gimaMortgageMaterial: string; // 抵押材料
  gimaIssuingAuthority: string; // 发证机关
  gimaMortgageRegCert1: string; // 抵押信息登记证(1)
  gimaMortgageRegCert2: string; // 抵押信息登记证(2)
  gimaCommercialPolicy: string; // 商业险保单
  gimaVehicleStatus: string; // 机动车状态（12123截图）
  gimaPolicePostReceipt: string; // 警邮回执
  gimaApprovalQuery: string; // 审批结果查询
  pprMortgageRegForm1: string; // 抵押信息登记表(1)
  pprMortgageRegForm2: string; // 抵押信息登记表(2)
  pprApprovalResult: string; // 审批结果
  pprApprovalRemarks: string; // 审批备注
  loanPhaseResult: string; // 放款结果
  loanPhaseRemark: string; // 审批备注
  loanPhasePlan: string; // 还款计划
  preCustomerLevel: string; // 预计客户等级
  psvaMaximumLoanAmount: string; // 最大贷款金额
};

/**
 * CombineSelect 组件上传的数据结构
 */
export interface UploadFileList {
  url: string;
  id: string | number;
}

//// 还款状态：1-未扣款，2-扣款成功，3-已提前结清，4-已取消合同，5-还款中
export const repaymentStatus = {
  1: '未扣款',
  2: '扣款成功',
  3: '已提前结清',
  4: '已取消合同',
  5: '还款中',
};

//结算状态 0-正常，1-已代扣，2-固购
export const examineRemark = {
  0: '正常',
  1: '已代扣',
  2: '固购',
};
