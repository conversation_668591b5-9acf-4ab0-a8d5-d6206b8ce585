import { ref, reactive, computed, watch, nextTick } from 'vue';
import type {
  DynamicFormConfig,
  DynamicFormField,
  DynamicFormInstance,
  FieldOption,
  FieldType,
  EnhancedFormItemRule,
} from '../types/dynamicForm';
import { FormValidators } from '../utils/validators';
import { ComponentMapManager } from '../utils/componentMap';

/**
 * 动态表单 Hook
 */
export function useDynamicForm(initialConfig?: Partial<DynamicFormConfig>) {
  // 表单配置
  const config = reactive<DynamicFormConfig>({
    fields: [],
    labelWidth: 120,
    labelPlacement: 'left',
    size: 'medium',
    inline: false,
    disabled: false,
    layout: 'horizontal',
    columns: 24,
    showSubmitButton: true,
    showResetButton: true,
    showCancelButton: false,
    submitButtonText: '提交',
    resetButtonText: '重置',
    cancelButtonText: '取消',
    validateOnRuleChange: true,
    validateOnValueChange: false,
    loading: false,
    readonly: false,
    debug: false,
    ...initialConfig,
  });

  // 表单实例引用
  const formInstance = ref<DynamicFormInstance>();

  // 表单数据
  const formData = ref<Record<string, any>>({});

  // 字段映射
  const fieldMap = computed(() => {
    const map = new Map<string, DynamicFormField>();
    config.fields.forEach((field) => {
      map.set(field.field, field);
    });
    return map;
  });

  // 可见字段
  const visibleFields = computed(() => {
    return config.fields.filter((field) => !field.hidden);
  });

  // 必填字段
  const requiredFields = computed(() => {
    return config.fields.filter((field) => field.required).map((field) => field.field);
  });

  /**
   * 获取字段配置
   */
  const getField = (fieldName: string): DynamicFormField | undefined => {
    return fieldMap.value.get(fieldName);
  };

  /**
   * 设置字段值
   */
  const setFieldValue = (fieldName: string, value: any) => {
    formData.value[fieldName] = value;
    formInstance.value?.setFieldValue(fieldName, value);
  };

  /**
   * 获取字段值
   */
  const getFieldValue = (fieldName: string) => {
    return formData.value[fieldName];
  };

  /**
   * 设置多个字段值
   */
  const setFieldsValue = (values: Record<string, any>) => {
    Object.assign(formData.value, values);
    formInstance.value?.setFormData(formData.value);
  };

  /**
   * 获取所有字段值
   */
  const getFieldsValue = () => {
    return { ...formData.value };
  };

  /**
   * 添加字段校验规则
   */
  const addFieldRule = (fieldName: string, rule: EnhancedFormItemRule) => {
    const field = getField(fieldName);
    if (field) {
      if (!field.rules) {
        field.rules = [];
      }
      field.rules.push(rule);
    }
  };

  /**
   * 校验表单
   */
  const validate = async (fields?: string[]): Promise<boolean> => {
    return formInstance.value?.validate(fields) || false;
  };

  /**
   * 校验单个字段
   */
  const validateField = async (fieldName: string): Promise<boolean> => {
    return formInstance.value?.validateField(fieldName) || false;
  };

  /**
   * 清除校验
   */
  const clearValidate = (fields?: string[]) => {
    formInstance.value?.clearValidate(fields);
  };

  /**
   * 重置表单
   */
  const resetForm = () => {
    formInstance.value?.resetForm();
  };

  /**
   * 清空表单
   */
  const clearForm = () => {
    formInstance.value?.clearForm();
    formData.value = {};
  };

  /**
   * 提交表单
   */
  const submit = async (): Promise<boolean> => {
    return formInstance.value?.submit() || false;
  };

  /**
   * 设置表单加载状态
   */
  const setLoading = (loading: boolean) => {
    config.loading = loading;
  };

  /**
   * 设置表单只读状态
   */
  const setReadonly = (readonly: boolean) => {
    config.readonly = readonly;
  };

  /**
   * 设置表单禁用状态
   */
  const setDisabled = (disabled: boolean) => {
    config.disabled = disabled;
  };

  /**
   * 根据条件过滤字段
   */
  const filterFields = (predicate: (field: DynamicFormField) => boolean): DynamicFormField[] => {
    return config.fields.filter(predicate);
  };

  /**
   * 查找字段
   */
  const findField = (
    predicate: (field: DynamicFormField) => boolean
  ): DynamicFormField | undefined => {
    return config.fields.find(predicate);
  };

  /**
   * 字段排序
   */
  const sortFields = (compareFn: (a: DynamicFormField, b: DynamicFormField) => number) => {
    config.fields.sort(compareFn);
  };

  /**
   * 获取字段索引
   */
  const getFieldIndex = (fieldName: string): number => {
    return config.fields.findIndex((field) => field.field === fieldName);
  };

  /**
   * 移动字段位置
   */
  const moveField = (fieldName: string, newIndex: number) => {
    const currentIndex = getFieldIndex(fieldName);
    if (currentIndex > -1 && newIndex >= 0 && newIndex < config.fields.length) {
      const field = config.fields.splice(currentIndex, 1)[0];
      config.fields.splice(newIndex, 0, field);
    }
  };

  /**
   * 监听表单数据变化
   */
  watch(
    formData,
    (newData) => {
      if (config.onFieldChange) {
        // 这里可以添加更详细的变化检测逻辑
      }
    },
    { deep: true }
  );

  return {
    // 配置
    config,
    formData,

    // 计算属性
    fieldMap,
    visibleFields,
    requiredFields,

    // 表单实例
    formInstance,

    // 字段操作
    getField,

    // 值操作
    setFieldValue,
    getFieldValue,
    setFieldsValue,
    getFieldsValue,

    // 校验规则
    addFieldRule,

    // 表单操作
    validate,
    validateField,
    clearValidate,
    resetForm,
    clearForm,
    submit,

    // 状态控制
    setLoading,
    setReadonly,
    setDisabled,

    // 批量操作
    filterFields,
    findField,
    sortFields,
    getFieldIndex,
    moveField,

    // 工具方法
    validators: FormValidators,
    componentManager: ComponentMapManager,
  };
}

export default useDynamicForm;
