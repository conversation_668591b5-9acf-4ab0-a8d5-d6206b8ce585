<template>
  <n-card>
    <!-- 车辆资料上传 -->
    <n-collapse arrow-placement="right" :expanded-names="expandedNames">
      <n-collapse-item name="2-1">
        <template #header>
          <Title :mainNode="2" :subNode="1" @click="toggleExpanded('2-1')" />
        </template>

        <div>
          <n-form
            ref="formRef"
            label-placement="left"
            size="medium"
            :model="formModel"
            :rules="currentRules"
            label-width="160px"
            :disabled="formDisabled"
            @submit.prevent
          >
            <n-grid :cols="GRID_COLS">
              <!-- <n-grid-item>
                <n-form-item label="车辆登记证书" path="psvaCarRegisCert">
                  <UploadFile
                    :file-list="jsonToImage(formModel.psvaCarRegisCert || '')"
                    accept=".jpg,.jpeg,.png,.bmp"
                    :max-size="10"
                    :max-count="1"
                    :force-array="true"
                    :disabled="formDisabled"
                    @update:file-list="
                      (val) => {
                        formModel.psvaCarRegisCert = imageToJson(val);
                        onOcr(
                          'psvaCarRegisCert',
                          val,
                          [
                            'psvaFirstRegisDate',
                            'psvaDisplace',
                            'psvaTransferTimes',
                            'psvaCarColoe',
                            'psvaFuelType',
                            'gimaIssuingAuthority',
                            'gimaCertificateNo',
                          ],
                          recognizeVehicleLicenseOcr
                        );
                      }
                    "
                  />
                </n-form-item>
              </n-grid-item> -->
              <n-grid-item>
                <!-- 行驶证 -->
                <n-form-item label="行驶证" path="psvaCarDrivCert" required>
                  <UploadFile
                    :file-list="jsonToImage(formModel.psvaCarDrivCert || '')"
                    accept=".jpg,.jpeg,.png,.bmp"
                    :max-size="10"
                    :max-count="1"
                    :force-array="true"
                    :disabled="formDisabled"
                    @update:file-list="
                      (val) => {
                        formModel.psvaCarDrivCert = imageToJson(val);
                        onOcr(
                          'psvaCarDrivCert',
                          val,
                          [
                            'psvaVinCode',
                            'psvaEngineNumber',
                            'psvaVehicleCate',
                            'psvaPlateNumber',
                            'psvaFirstRegisDate',
                          ],
                          vehicleLicenseOcr
                        );
                      }
                    "
                  />
                </n-form-item>
              </n-grid-item>
            </n-grid>

            <!-- 贷款信息 -->
            <n-grid :cols="GRID_COLS">
              <n-grid-item>
                <n-form-item label="期望贷款金额" path="psvaLoanAmout" required>
                  <n-input
                    v-model:value="formModel.psvaLoanAmout"
                    placeholder="请输入期望贷款金额"
                    clearable
                    maxlength="9"
                  />
                  <span class="money-tips">金额必须≥15000 元</span>
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="期望贷款期限" path="psvaLoanTime" required>
                  <n-select
                    v-model:value="formModel.psvaLoanTime"
                    placeholder="请选择期望贷款期限"
                    :options="fieldOptions.psvaLoanTime || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="车辆售价" path="psvaPrice" required>
                  <n-input
                    v-model:value="formModel.psvaPrice"
                    placeholder="请输入车辆售价"
                    clearable
                    maxlength="9"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="里程公里" path="psvaMileage" required>
                  <n-input
                    v-model:value="formModel.psvaMileage"
                    placeholder="请输入里程"
                    clearable
                    maxlength="9"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="抵押城市" path="psvaCiey" required>
                  <CitySelect
                    v-model="formModel.psvaCiey"
                    change-on-select
                    :multiple="false"
                    :disabled="formDisabled"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="变速箱类型" path="psvaScBox" required>
                  <n-select
                    v-model:value="formModel.psvaScBox"
                    placeholder="请选择变速箱类型"
                    :options="fieldOptions.psvaScBox || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人公牌类型" path="psvaPublicType" required>
                  <n-select
                    v-model:value="formModel.psvaPublicType"
                    placeholder="请选择公牌类型"
                    :options="fieldOptions.psvaPublicType || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <template v-if="formModel.psvaPublicType === '一般公牌'">
                <n-grid-item>
                  <n-form-item label="公牌公司电话" path="psvaPublicPhoneNum" required>
                    <n-input
                      v-model:value="formModel.psvaPublicPhoneNum"
                      placeholder="请输入公司电话"
                      clearable
                      maxlength="20"
                    />
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      公牌类型必传
                    </n-tooltip>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="公牌组织机构代码" path="psvaOrganCode" required>
                    <n-input
                      v-model:value="formModel.psvaOrganCode"
                      placeholder="请输入组织机构代码"
                      clearable
                      maxlength="50"
                    />
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      公牌类型必传
                    </n-tooltip>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="公牌公司名称" path="psvaComName" required>
                    <n-input
                      v-model:value="formModel.psvaComName"
                      placeholder="请输入公司名称"
                      clearable
                      maxlength="50"
                    />
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      公牌类型必传
                    </n-tooltip>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="公牌公司法人姓名" path="psvaComPerName" required>
                    <n-input
                      v-model:value="formModel.psvaComPerName"
                      placeholder="请输入法人姓名"
                      clearable
                      maxlength="20"
                    />
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      公牌类型必传
                    </n-tooltip>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="公牌公司法人身份证" path="psvaComPerIdCard" required>
                    <n-input
                      v-model:value="formModel.psvaComPerIdCard"
                      placeholder="请输入法人身份证号"
                      maxlength="18"
                      clearable
                    />
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      公牌类型必传
                    </n-tooltip>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="公牌公司注册公司时间" path="psvaComRegiDate" required>
                    <n-date-picker
                      :formatted-value="isDate(formModel.psvaComRegiDate as string) ? formModel.psvaComRegiDate : undefined"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="请选择注册时间"
                      clearable
                      @update:formatted-value="(formModel.psvaComRegiDate = $event) as any"
                    />
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      公牌类型必传
                    </n-tooltip>
                  </n-form-item>
                </n-grid-item>
              </template>
            </n-grid>

            <!-- 行驶证相关 -->
            <n-card>
              <n-flex align="center">
                <div>
                  <SubTitle title="行驶证相关" />
                  <n-button
                    type="primary"
                    size="small"
                    :disabled="!formModel.psvaCarDrivCert"
                    @click="onPreview(jsonToImage(formModel.psvaCarDrivCert || '').map((item) => item.url) as string[])"
                  >
                    预览
                  </n-button>
                </div>
                <div class="flex-1 mb-[-25px]">
                  <n-grid :cols="GRID_COLS">
                    <n-grid-item>
                      <n-form-item label="车辆VIN码" path="psvaVinCode" required>
                        <n-input
                          v-model:value="formModel.psvaVinCode"
                          placeholder="请输入VIN码"
                          maxlength="17"
                          clearable
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="首次上牌时间" path="psvaFirstRegisDate" required>
                        <n-date-picker
                          :formatted-value="
                            isDate(formModel.psvaFirstRegisDate as string)
                              ? formModel.psvaFirstRegisDate
                              : undefined
                          "
                          type="date"
                          format="yyyy-MM-dd"
                          style="width: 100%"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择首次上牌时间"
                          clearable
                          @update:formatted-value="formModel.psvaFirstRegisDate = $event"
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="发动机号" path="psvaEngineNumber" required>
                        <n-input
                          v-model:value="formModel.psvaEngineNumber"
                          placeholder="请输入发动机号"
                          clearable
                          maxlength="50"
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="车辆类别" path="psvaVehicleCate" required>
                        <n-space class="w-full" vertical align="stretch">
                          <n-select
                            v-model:value="formModel.psvaVehicleCate"
                            placeholder="请选择车辆类别"
                            :options="fieldOptions.psvaVehicleCate || []"
                            clearable
                          />
                          <n-text v-if="psvaVehicleCate" type="primary">
                            识别：{{ psvaVehicleCate }}
                          </n-text>
                        </n-space>
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="车牌号" path="psvaPlateNumber" required>
                        <n-input
                          v-model:value="formModel.psvaPlateNumber"
                          placeholder="请输入车牌号"
                          clearable
                          maxlength="8"
                        />
                      </n-form-item>
                    </n-grid-item>
                  </n-grid>
                </div>
              </n-flex>
            </n-card>

            <!-- 车辆登记证相关 -->
            <n-card class="my-4">
              <n-flex align="center">
                <div>
                  <SubTitle title="车辆登记证相关" />
                  <n-button
                    type="primary"
                    size="small"
                    :disabled="!formModel.psvaCarRegisCert"
                    @click="onPreview(jsonToImage(formModel.psvaCarRegisCert || '').map((item) => item.url) as string[])"
                  >
                    预览
                  </n-button>
                </div>
                <div class="flex-1 mb-[-25px]">
                  <n-grid :cols="GRID_COLS">
                    <n-grid-item>
                      <n-form-item label="排量/L" path="psvaDisplace" required>
                        <n-input
                          v-model:value="formModel.psvaDisplace"
                          placeholder="请输入排量"
                          clearable
                          maxlength="9"
                        />
                      </n-form-item>
                    </n-grid-item>

                    <n-grid-item>
                      <n-form-item label="过户次数" path="psvaTransferTimes" required>
                        <n-input
                          v-model:value="formModel.psvaTransferTimes"
                          placeholder="请输入过户次数"
                          clearable
                          maxlength="2"
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="车辆颜色" path="psvaCarColoe" required>
                        <n-select
                          v-model:value="formModel.psvaCarColoe"
                          placeholder="请选择车辆颜色"
                          :options="fieldOptions.psvaCarColoe || []"
                          clearable
                          :fallback-option="false"
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="燃料类型" path="psvaFuelType" required>
                        <n-select
                          v-model:value="formModel.psvaFuelType"
                          placeholder="请选择燃料类型"
                          :options="fieldOptions.psvaFuelType || []"
                          clearable
                          :fallback-option="false"
                        />
                      </n-form-item>
                    </n-grid-item>
                  </n-grid>
                </div>
              </n-flex>
            </n-card>

            <n-flex justify="center">
              <n-button
                :type="canSubmit ? 'primary' : 'default'"
                form-type="submit"
                :disabled="formDisabled"
                :loading="formLoading"
                @click="canSubmit ? handleSubmit() : handleSave()"
              >
                {{ canSubmit ? '提交' : '保存' }}
              </n-button>
            </n-flex>
          </n-form>

          <template v-if="productOptions.length">
            <n-divider dashed />
            <SubTitle
              title="选择产品"
              desc="前置要求：贷款金额、贷款期限、车辆售价、首次上牌时间、里程、车辆类别、公牌类型、燃料类型信息完整"
            />
            <n-descriptions label-placement="top" bordered :column="2">
              <n-descriptions-item label="产品列表">
                <n-select
                  v-model:value="selectedProduct"
                  placeholder="请选择产品"
                  :options="productOptions"
                  clearable
                  :disabled="formDisabled"
                />
              </n-descriptions-item>
              <n-descriptions-item label="操作">
                <n-button
                  size="small"
                  type="primary"
                  :disabled="formDisabled"
                  @click="onConfirmSelectProduct"
                >
                  确认
                </n-button>
              </n-descriptions-item>
            </n-descriptions>
            <SubTitle
              class="mt-4"
              title="车辆评估"
              desc="前置要求：车辆VIN码、变速箱类型、排量、燃料类型、车辆颜色、发动机号、车牌号、过户次数、抵押城市、首次上牌时间、里程、车辆售价信息完整"
            />
            <n-descriptions label-placement="top" bordered :column="4">
              <n-descriptions-item label="车辆评估价">
                <n-popconfirm
                  :show="showPopConfirm"
                  :negative-text="null"
                  @positive-click="showPopConfirm = false"
                >
                  <template #trigger>
                    {{ initialData.psvaCarEvalPrice || '-' }}
                  </template>
                  当前车辆评估价格低于贷款最低值，请重新确认车辆相关信息是否准确
                </n-popconfirm>
              </n-descriptions-item>
              <n-descriptions-item>
                <template #label>
                  <div class="flex items-center">
                    <n-text>预估最高可贷金额</n-text>
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      根据车辆评估价和客户评级等级预估最高可贷金额
                    </n-tooltip>
                  </div>
                </template>
                {{ initialData.psvaMaximumLoanAmount || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="剩余评估次数">
                {{ initialData.psvaRemainEvalTimes ?? '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="操作">
                <n-button
                  size="small"
                  type="primary"
                  v-cooldown
                  :disabled="String(initialData.psvaRemainEvalTimes) === '0' || formDisabled"
                  @click="onEvaluateQuery"
                >
                  评估查询
                </n-button>
              </n-descriptions-item>
            </n-descriptions>
          </template>
        </div>
      </n-collapse-item>
    </n-collapse>
  </n-card>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import UploadFile from '@/components/UploadFile/index.vue';
  import CitySelect from '@/components/CitySelect/v2.vue';
  import { QuestionCircleTwotone } from '@vicons/antd';

  import { ref, reactive, computed, onMounted, watch, inject } from 'vue';
  import { saveFieldData, submitForm, getProductEvaluation } from '@/api/dashboard/deyi';
  import type { Fields } from '@/components/DrawerOrder/types';
  import { GRID_COLS } from '@/components/DrawerOrder/config';
  import { useBaseData } from '../../composables/useBaseData';
  import { usePreviewImage } from '@/composables/usePreviewImage';
  import { vehicleLicenseOcr, recognizeVehicleLicenseOcr } from '@/components/DrawerOrder/ocr';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import { promisifyDialog } from '@/utils/nativeUtils';
  import { imageToJson, jsonToImage, validateCityCode } from '@/components/DrawerOrder/utils';
  import { useFormDisabled } from '@/components/DrawerOrder/composables/useFormDisabled';
  import { PublicType } from '@/components/DrawerOrder/enum';
  import { bufferedEmitter, EventNames } from '@/utils/eventBus';

  interface Props {
    innerOrderNo: string;
  }
  const props = defineProps<Props>();
  const expandedNames = inject('expandedNames');
  const toggleExpanded = inject('toggleExpanded') as (name: string) => void;
  const orderDrawerStore = useOrderDrawerStore();
  const { isCurrentStagePending, isCurrentStageCompleted } = useFormDisabled();

  const { previewImage: onPreview } = usePreviewImage();
  const psvaVehicleCate = ref(''); // 展示数据
  const showPopConfirm = ref(false);

  // ========== VehicleUpload 表单数据 ==========
  const formRef = ref<any>(null);
  const formLoading = ref(false);
  const formModel = reactive<Partial<Fields>>({
    // 文件上传
    psvaCarRegisCert: '',
    psvaCarDrivCert: '',
    // 贷款信息
    psvaLoanAmout: '',
    psvaLoanTime: '',
    psvaPrice: '',
    psvaMileage: '',
    psvaScBox: '',
    psvaCiey: '',
    // 公牌信息
    psvaPublicType: '',
    psvaPublicPhoneNum: '',
    psvaOrganCode: '',
    psvaComName: '',
    psvaComPerName: '',
    psvaComPerIdCard: '',
    psvaComRegiDate: undefined,
    // 行驶证相关
    psvaVinCode: '',
    psvaEngineNumber: '',
    psvaVehicleCate: '',
    psvaPlateNumber: '',
    // 车辆登记证相关
    psvaFirstRegisDate: undefined,
    psvaDisplace: '',
    psvaTransferTimes: '',
    psvaCarColoe: '',
    psvaFuelType: '',
    gimaIssuingAuthority: '',
    gimaCertificateNo: '',
  });
  const formDisabled = computed(() => isCurrentStageCompleted('2-1'));

  const onOcr = async (
    ocrKey: string,
    fileList: { url: string; id: string }[],
    addKeys: string[],
    api: any
  ) => {
    if (!fileList || !fileList.length) {
      return;
    }

    let data: any = {};

    const urls = fileList.map((item) => item.url);
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i];
      const res = await api({ url });
      Object.assign(data, res || {});
    }

    addKeys.forEach((key) => {
      if (data[key]) {
        if (key === 'psvaFirstRegisDate') {
          // 判断日期格式是否是 yyyy-MM-dd
          if (!isDate(data[key])) {
            window.$message?.error?.('首次上牌时间,日期格式不正确,请手动录入');
            console.warn('psvaFirstRegisDate 格式不正确');
            data[key] = '';
            return;
          }
        }
        // 车辆类型只回显
        if (key === 'psvaVehicleCate') {
          psvaVehicleCate.value = data[key];
          data[key] = '';
          return;
        }

        formModel[key] = data[key];
      }
    });

    saveFieldData({
      innerOrderNo: props.innerOrderNo,
      fieldData: {
        ...addKeys.reduce(
          (pre, cur) => {
            if (data[cur]) {
              pre[cur] = data[cur];
            }
            return pre;
          },
          {
            [ocrKey]: imageToJson(fileList),
          }
        ),
      },
    });
  };

  const {
    data: initialData,
    options: fieldOptions,
    getData,
    getOptions,
  } = useBaseData({
    innerOrderNo: props.innerOrderNo,
    keys: [
      ...Object.keys(formModel),
      'psvaSelectProduct',
      'psvaProductList',
      'psvaCarEvalPrice',
      'psvaRemainEvalTimes',
      'psvaMaximumLoanAmount',
      // 申请城市，设置抵押城市默认值
      'preAuditPhaseCity',
    ],
  });

  watch(
    () => initialData.psvaCarEvalPrice,
    (val) => {
      if (val && Number(val) < 25000) {
        showPopConfirm.value = true;
      }
    }
  );

  // 校验规则
  const psvaFormRules = {
    // psvaCarRegisCert: [{ required: true, message: '请上传车辆登记证书', trigger: 'change' }],
    psvaCarDrivCert: [{ required: true, message: '请上传行驶证', trigger: 'change' }],
    psvaLoanAmout: [
      { required: true, message: '请输入贷款金额', trigger: 'blur' },
      {
        pattern: /^\d+(\.\d{1,2})?$/,
        message: '只能输入正数，小数最多保留2位',
        trigger: 'blur',
      },
      {
        validator(_rule, value) {
          if (value && Number(value) < 15000) {
            return new Error('金额必须≥15000');
          }
          return true;
        },
        trigger: 'blur',
      },
    ],
    psvaLoanTime: [{ required: true, message: '请选择贷款期限', trigger: 'change' }],
    psvaPrice: [
      { required: true, message: '请输入车辆售价', trigger: 'blur' },
      {
        pattern: /^\d+(\.\d{1,2})?$/,
        message: '只能输入正数，小数最多保留2位',
        trigger: 'blur',
      },
    ],
    psvaMileage: [
      { required: true, message: '请输入里程', trigger: 'blur' },
      {
        pattern: /^\d+(\.\d{1,2})?$/,
        message: '只能输入正数，小数最多保留2位',
        trigger: 'blur',
      },
    ],
    psvaScBox: [{ required: true, message: '请选择变速箱类型', trigger: 'change' }],
    psvaCiey: [
      {
        required: true,
        validator(_rule, value) {
          return validateCityCode(value as string);
        },
        trigger: 'change',
      },
    ],
    psvaPublicType: [{ required: true, message: '请选择公牌类型', trigger: 'change' }],
    psvaPublicPhoneNum: [{ required: true, message: '请输入公牌公司电话', trigger: 'blur' }],
    psvaOrganCode: [{ required: true, message: '请输入组织机构代码', trigger: 'blur' }],
    psvaComName: [{ required: true, message: '请输入公牌公司名称', trigger: 'blur' }],
    psvaComPerName: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
    psvaComPerIdCard: [
      { required: true, message: '请输入身份证号', trigger: 'blur' },
      {
        pattern:
          /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: '请输入正确的身份证号',
        trigger: 'blur',
      },
    ],
    psvaComRegiDate: [{ required: true, message: '请选择企业成立时间', trigger: 'change' }],
    psvaDisplace: [{ required: true, message: '请输入排量', trigger: 'blur' }],
    psvaFuelType: [{ required: true, message: '请选择燃料类型', trigger: 'change' }],
    psvaVehicleCate: [{ required: true, message: '请选择车辆类别', trigger: 'change' }],
    psvaPlateNumber: [{ required: true, message: '请输入车牌号', trigger: 'blur' }],
    psvaCarColoe: [{ required: true, message: '请选择车辆颜色', trigger: 'change' }],
    psvaEngineNumber: [{ required: true, message: '请输入发动机号', trigger: 'blur' }],
    psvaFirstRegisDate: [{ required: true, message: '请选择首次上牌时间', trigger: 'change' }],
    psvaVinCode: [{ required: true, message: '请输入车辆VIN码', trigger: 'blur' }],
    psvaTransferTimes: [
      { required: true, message: '请输入过户次数', trigger: 'blur' },
      {
        pattern: /^(0|[1-9]\d*)$/,
        message: '请输入正确的数字',
        trigger: 'blur',
      },
    ],
  };

  // 必填字段列表
  const psvaRequiredFields = computed(() => {
    return Object.keys(psvaFormRules).filter((field) => {
      if (
        [
          'psvaPublicPhoneNum',
          'psvaOrganCode',
          'psvaComName',
          'psvaComPerName',
          'psvaComPerIdCard',
          'psvaComRegiDate',
        ].includes(field)
      ) {
        return String(formModel.psvaPublicType) === String(PublicType.Company);
      }

      return (
        Array.isArray(psvaFormRules[field]) &&
        psvaFormRules[field].some((rule) => rule.required || rule.validator)
      );
    });
  });

  // 判断是否可以提交
  const canSubmit = computed(() => {
    for (const field of psvaRequiredFields.value) {
      const val = formModel[field];
      if (!val) return false;
      if (Array.isArray(val) && val.length === 0) return false;
      if (typeof val === 'string' && val.trim() === '') return false;
    }

    if (isCurrentStagePending('2-1')) return false;

    return true;
  });

  // 当前校验规则（根据是否可以提交切换宽严）
  const currentRules = computed(() => {
    if (canSubmit.value) {
      return psvaFormRules;
    }
    const rules: any = {};
    Object.keys(psvaFormRules).forEach((key) => {
      rules[key] = (psvaFormRules[key] as any[]).filter((rule) => !rule.required);
    });
    return rules;
  });

  // 保存逻辑
  const handleSave = async () => {
    try {
      await formRef.value?.validate();
    } catch {
      window.$message?.error('填写内容格式不正确');
      return;
    }
    try {
      formLoading.value = true;
      const innerOrderNo = props.innerOrderNo;

      await saveFieldData({ innerOrderNo, fieldData: formModel });
      window.$message?.success('保存成功');
    } catch (e) {
      window.$message?.error('保存失败');
      console.error(e);
    } finally {
      formLoading.value = false;
    }
  };

  // 提交逻辑
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();
    } catch {
      console.error('表单验证失败:');
      return;
    }
    try {
      formLoading.value = true;
      const innerOrderNo = props.innerOrderNo;

      await saveFieldData({ innerOrderNo, fieldData: formModel });
      await submitForm({ innerOrderNo, mainNodeCode: 2, subNodeCode: 1 });

      orderDrawerStore.triggerRefresh();
      getData();
      window.$message?.success('提交成功');
    } catch (e) {
      window.$message?.error('提交失败');
      console.error(e);
    } finally {
      formLoading.value = false;
    }
  };

  // 改造后的产品选择：本地状态 + initialData 列表
  const selectedProduct = ref<string>('');
  const productOptions = computed<{ label: string; value: string }[]>(() => {
    try {
      return initialData.psvaProductList
        ? JSON.parse(initialData.psvaProductList).map((item: any) => ({
            label: item.name,
            value: '' + item.value,
          }))
        : [];
    } catch (e) {
      return [];
    }
  });

  const onConfirmSelectProduct = async () => {
    const psvaSelectProduct = selectedProduct.value;
    if (!psvaSelectProduct) {
      window.$message?.error('请选择产品');
      return;
    }

    const result = await promisifyDialog(window.$dialog.warning)({
      title: '选择产品',
      content: '当前产品确认后，不可修改产品信息，请确认',
      positiveText: '确定',
      negativeText: '取消',
      maskClosable: false,
    });

    if (result.source !== 'positive') {
      return;
    }

    await saveFieldData({
      innerOrderNo: props.innerOrderNo,
      fieldData: {
        psvaSelectProduct,
      },
    });

    orderDrawerStore.triggerRefresh();
    getData();
  };

  // 车辆评估：方法
  const onEvaluateQuery = async () => {
    const remain = Number(initialData.psvaRemainEvalTimes || 0);

    if (remain > 0) {
      const result = await promisifyDialog(window.$dialog.warning)({
        title: '车辆评估',
        content: `当前剩余评估次数${remain}次，请确认是否发起车辆评估`,
        positiveText: '确定',
        negativeText: '取消',
        maskClosable: false,
      });
      if (result.source !== 'positive') return;
    }

    await getProductEvaluation({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };

  // 是否是合法日期 yyyy-MM-dd
  const isDate = (date: string) => {
    if (!date) return false;

    const reg = /^\d{4}-\d{2}-\d{2}$/;
    return reg.test(date);
  };

  watch(
    initialData,
    (newVal) => {
      if (!newVal) {
        return;
      }
      Object.keys(formModel).forEach((key) => {
        formModel[key] = newVal[key];
      });

      // 抵押城市
      if (!formModel.psvaCiey && newVal.preAuditPhaseCity) {
        formModel.psvaCiey = newVal.preAuditPhaseCity;
      }

      // 同步已选产品
      selectedProduct.value = newVal.psvaSelectProduct || '';
    },
    { immediate: true, deep: true }
  );

  watch(
    () => formModel.psvaPublicType,
    (newVal) => {
      bufferedEmitter.emit(
        EventNames.DEYI_POPUP_IS_PUBLIC_NOTICE,
        String(newVal) === String(PublicType.Company)
      );
    },
    { immediate: true, deep: true }
  );

  onMounted(async () => {
    await getData();
    await getOptions();
  });
</script>

<style lang="less" scoped>
  .money-tips {
    position: absolute;
    left: -100%;
    top: 93%;
    color: #7b7b7b;
  }
</style>
