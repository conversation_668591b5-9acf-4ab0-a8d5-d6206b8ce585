<template>
  <n-space vertical>
    <n-card>
      <n-collapse arrow-placement="right" :expanded-names="expandedNames">
        <n-collapse-item
          :name="`${nodeCodeMaps.mortgage.mainNode}-${nodeCodeMaps.mortgage.subNode.gpsInsuranceMortgageApproval}`"
        >
          <template #header>
            <Title
              ref="nodeRef1"
              :mainNode="nodeCodeMaps.mortgage.mainNode"
              :subNode="nodeCodeMaps.mortgage.subNode.gpsInsuranceMortgageApproval"
              @click="
                toggleExpanded(
                  `${nodeCodeMaps.mortgage.mainNode}-${nodeCodeMaps.mortgage.subNode.gpsInsuranceMortgageApproval}`
                )
              "
            />
          </template>

          <div>
            <n-form
              ref="formRef"
              label-placement="left"
              size="medium"
              :model="formModel"
              :rules="formRules"
              label-width="120px"
              show-require-mark
              :disabled="formDisabled1"
            >
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="保险公司" path="gimaInsuranceCompany">
                    <n-input
                      v-model:value="formModel.gimaInsuranceCompany"
                      placeholder="请输入保险公司"
                      maxlength="50"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="商业险到期日期" path="gimaCommercialExpireDate">
                    <n-date-picker
                      :formatted-value="formModel.gimaCommercialExpireDate || undefined"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="yyyy-MM-dd"
                      clearable
                      @update:formatted-value="(formModel.gimaCommercialExpireDate = $event) as any"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="保险渠道" path="gimaInsuranceChannel">
                    <n-select
                      v-model:value="formModel.gimaInsuranceChannel"
                      placeholder="请选择保险渠道"
                      :options="insuranceChannelOptions"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="抵押日期" path="gimaMortgageDate">
                    <n-date-picker
                      :formatted-value="formModel.gimaMortgageDate || undefined"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="yyyy-MM-dd"
                      clearable
                      @update:formatted-value="(formModel.gimaMortgageDate = $event) as any"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="抵押材料" path="gimaMortgageMaterial">
                    <n-select
                      v-model:value="formModel.gimaMortgageMaterial"
                      placeholder="请选择抵押材料"
                      :options="mortgageMaterialsOptions"
                      clearable
                      @change="selectedAttachment = 'gimaCommercialPolicy'"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <n-card>
                <n-space align="start" :vertical="false">
                  <div>
                    <p>车辆登记相关</p>
                    <n-button
                      type="primary"
                      :disabled="!formData.psvaCarRegisCert"
                      @click="onPreview(jsonToImage(formData.psvaCarRegisCert).map((item) => item.url) as string[])"
                    >
                      预览
                    </n-button>
                  </div>
                  <n-form-item label="发证机关" path="gimaIssuingAuthority">
                    <n-input
                      v-model:value="formModel.gimaIssuingAuthority"
                      placeholder="请输入发证机关"
                      maxlength="50"
                    />
                  </n-form-item>
                  <n-form-item label="证书编号" path="gimaCertificateNo">
                    <n-input
                      v-model:value="formModel.gimaCertificateNo"
                      placeholder="请输入证书编号"
                      maxlength="20"
                    />
                  </n-form-item>
                </n-space>
              </n-card>
              <n-card class="mt-2">
                <n-form-item label="附件资料" path="appendix" :rules="formRules.appendix">
                  <CombineSelect
                    v-model:value="selectedAttachment"
                    v-model:appendix="formModel as any"
                    :disabled="formDisabled1"
                    :options="selectOptions"
                  />
                </n-form-item>
              </n-card>
              <n-flex justify="center" class="mt-2">
                <n-button
                  :type="canSubmit1 ? 'primary' : 'default'"
                  :loading="submitLoading1"
                  :disabled="formDisabled1"
                  @click="
                    canSubmit1
                      ? handleSubmit({ formRefNode: formRef, formModel, formIndex: 1 })
                      : onSave({ formRefNode: formRef, formModel, formIndex: 1 })
                  "
                >
                  {{ formDisabled1 ? '已提交' : canSubmit1 ? '确认提交' : '保存' }}
                </n-button>
              </n-flex>
            </n-form>
            <template
              v-if="
                !!formData.gimaApprovalQuery &&
                String(formData.gimaApprovalQuery) !== String(GpsInsuranceApproveStatus.Pending)
              "
            >
              <n-divider dashed />
              <SubTitle title="GPS、保险、抵押审批" />
              <n-grid :cols="2">
                <n-grid-item span="1">
                  <n-descriptions
                    label-style="width: 120px"
                    label-placement="top"
                    :column="2"
                    bordered
                  >
                    <n-descriptions-item label="审批结果查询">
                      <n-tag
                        v-if="
                          formData.gimaApprovalQuery &&
                          GpsInsuranceApproveStatusMap[formData.gimaApprovalQuery]
                        "
                        size="small"
                        :type="
                          String(formData.gimaApprovalQuery) ===
                          String(GpsInsuranceApproveStatus.Pass)
                            ? 'success'
                            : 'default'
                        "
                      >
                        {{ GpsInsuranceApproveStatusMap[formData.gimaApprovalQuery] }}
                      </n-tag>
                      <span v-else>-</span>
                    </n-descriptions-item>
                    <n-descriptions-item label="审批备注">
                      {{ formData.gimaApprovalRemark || '-' }}
                    </n-descriptions-item>
                    <n-descriptions-item label="操作">
                      <n-button
                        size="small"
                        :disabled="formStatusDisabled1"
                        :loading="searchLoading1"
                        type="primary"
                        v-cooldown
                        @click="handleSearch1"
                      >
                        状态查询
                      </n-button>
                    </n-descriptions-item>
                  </n-descriptions>
                </n-grid-item>
              </n-grid>
            </template>
          </div>
        </n-collapse-item>
      </n-collapse>
    </n-card>

    <n-card
      v-if="
        formData.gimaApprovalQuery &&
        String(formData.gimaApprovalQuery) !== String(GpsInsuranceApproveStatus.Pending) &&
        formData.gimaMortgageMaterial === '警邮回执'
      "
    >
      <!-- 警邮回执附件资料 -->
      <n-collapse arrow-placement="right" :expanded-names="expandedNames">
        <n-collapse-item
          :name="`${nodeCodeMaps.mortgage.mainNode}-${nodeCodeMaps.mortgage.subNode.policePostReceipt}`"
        >
          <template #header>
            <Title
              ref="nodeRef2"
              :mainNode="nodeCodeMaps.mortgage.mainNode"
              :subNode="nodeCodeMaps.mortgage.subNode.policePostReceipt"
              @click="
                toggleExpanded(
                  `${nodeCodeMaps.mortgage.mainNode}-${nodeCodeMaps.mortgage.subNode.policePostReceipt}`
                )
              "
            />
          </template>
          <n-card>
            <n-form
              ref="formRef2"
              label-placement="left"
              size="medium"
              :model="formModel2"
              :rules="formRules2"
              label-width="120px"
              show-require-mark
              :disabled="formDisabled2"
            >
              <n-form-item label="附件资料" path="appendix">
                <CombineSelect
                  v-model:value="selectedAttachment2"
                  v-model:appendix="formModel2"
                  :disabled="formDisabled2"
                  :options="selectOptions2"
                />
              </n-form-item>
            </n-form>
          </n-card>

          <n-flex justify="center" class="mt-2">
            <n-button
              :type="canSubmit2 ? 'primary' : 'default'"
              :loading="submitLoading2"
              :disabled="formDisabled2"
              @click="
                canSubmit2 && isCurrentStageCompleted('5-1')
                  ? handleSubmit({ formRefNode: formRef2, formModel: formModel2, formIndex: 2 })
                  : onSave({ formRefNode: formRef2, formModel: formModel2, formIndex: 2 })
              "
            >
              {{
                formDisabled2
                  ? '已提交'
                  : canSubmit2 && isCurrentStageCompleted('5-1')
                  ? '确认提交'
                  : '保存'
              }}
            </n-button>
          </n-flex>
          <n-divider dashed />
          <SubTitle
            title="警邮回执"
            desc="前置要求：抵押材料为警邮回执时可先放后抵，并后续补充抵押相关登记证信息"
          />
          <n-grid :cols="2">
            <n-grid-item span="1">
              <n-descriptions label-style="width: 120px" label-placement="top" :column="3" bordered>
                <n-descriptions-item label="审批结果">
                  {{
                    formData.pprApprovalResult
                      ? GpsInsuranceApproveStatusMap?.[formData.pprApprovalResult] ?? '-'
                      : '-'
                  }}
                </n-descriptions-item>
                <n-descriptions-item label="审批备注">
                  {{ formData.pprApprovalRemarks || '-' }}
                </n-descriptions-item>
                <n-descriptions-item label="操作">
                  <n-button
                    size="small"
                    :disabled="formStatusDisabled2"
                    :loading="searchLoading2"
                    type="primary"
                    v-cooldown
                    @click="handleSearch2"
                  >
                    状态查询
                  </n-button>
                </n-descriptions-item>
              </n-descriptions>
            </n-grid-item>
          </n-grid>
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import CombineSelect from '@/components/DrawerOrder/components/CombineSelect/index.vue';
  import { ref, reactive, computed, onMounted, watch, h, inject } from 'vue';
  import type { FormInst } from 'naive-ui';
  import { GRID_COLS } from '@/components/DrawerOrder/config';
  import { useFormDisabled } from '@/components/DrawerOrder/composables/useFormDisabled';

  import {
    getFieldConfig,
    getFieldData,
    saveFieldData,
    getMortgageAuditResult,
    getGPSPoliceReceipt,
    submitForm,
  } from '@/api/dashboard/deyi';
  import { usePreviewImage } from '@/composables/usePreviewImage';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import { NodeCodeMap } from '@/components/DrawerOrder/types';
  import {
    GpsInsuranceApproveStatusMap,
    GpsInsuranceApproveStatus,
  } from '@/components/DrawerOrder/enum';
  import { jsonToImage } from '@/components/DrawerOrder/utils';
  const { isCurrentStageCompleted } = useFormDisabled();

  const nodeCodeMaps = ref(NodeCodeMap);
  const expandedNames = inject('expandedNames');
  const toggleExpanded = inject('toggleExpanded') as (name: string) => void;

  const orderDrawerStore = useOrderDrawerStore();
  const formRef = ref<FormInst | null>(null);
  const formRef2 = ref<FormInst | null>(null);
  const nodeRef1 = ref({ status: '' });
  const nodeRef2 = ref({ status: '' });
  const nodeStatus1 = computed(() => nodeRef1.value?.status);
  const nodeStatus2 = computed(() => nodeRef2.value?.status);
  const defaultFormModel = {
    gimaInsuranceCompany: '', //保险公司
    gimaCommercialExpireDate: null, //商业险到期日期
    gimaInsuranceChannel: '', //保险渠道
    gimaMortgageDate: null, //抵押日期
    gimaMortgageMaterial: '', //抵押材料
    gimaIssuingAuthority: '', //发证机关
    gimaCertificateNo: '', //证书编号
    //附件资料
    gimaMortgageRegCert: '', //抵押信息登记证
    gimaCommercialPolicy: '', //商业保单
    gimaVehicleStatus: '', //机动车状态（12123截图)
    gimaPolicePostReceipt: '', //警邮回执
    gimaMortgageRegDjz: '', //警邮回执登记证
  };
  const defaultFormModel2 = {
    pprMortgageRegForm: '', //抵押信息登记证
  };
  const selectedAttachment = ref('gimaCommercialPolicy');
  const selectedAttachment2 = ref('pprMortgageRegForm');
  const props = defineProps({
    innerOrderNo: {
      type: String,
      required: true,
    },
  });
  const formData = ref({
    ...defaultFormModel,
    ...defaultFormModel2,
    psvaCarRegisCert: '',
    gimaApprovalQuery: '',
    pprApprovalResult: '',
    pprApprovalRemarks: '',
    ipAttachOrigRegForm: '', //原登记证
    gimaApprovalRemark: '',
  }); //数据库存的数据
  const formModel = reactive({ ...defaultFormModel });
  const formModel2 = reactive({ ...defaultFormModel2 });
  const insuranceChannelOptions = ref([]); //保险渠道
  const mortgageMaterialsOptions = ref([]); //抵押材料
  const selectOptions = computed(() => {
    const certificate = {
      value: 'gimaMortgageRegCert',
      label: `抵押信息登记证(2)`,
      finish: jsonToImage(formModel.gimaMortgageRegCert)?.length >= 2,
      required: true,
      min: 2,
      max: 9,
    };
    const police = [
      {
        value: 'gimaPolicePostReceipt',
        label: `警邮回执`,
        finish: jsonToImage(formModel.gimaPolicePostReceipt)?.length > 0,
        required: true,
      },
      {
        value: 'gimaMortgageRegDjz',
        label: `登记证(2)`,
        finish: jsonToImage(formModel.gimaMortgageRegDjz)?.length >= 2,
        required: true,
        min: 2,
        max: 2,
      },
    ];
    const list = [
      {
        value: 'gimaCommercialPolicy',
        label: `商业保单`,
        finish: jsonToImage(formModel.gimaCommercialPolicy)?.length > 0,
        required: true,
      },
      {
        value: 'gimaVehicleStatus',
        label: `机动车状态（12123截图） `,
        finish: jsonToImage(formModel.gimaVehicleStatus)?.length > 0,
        required: true,
      },
    ];
    formModel.gimaMortgageMaterial === '警邮回执'
      ? list.push(...police)
      : formModel.gimaMortgageMaterial === '登记证'
      ? list.unshift(certificate)
      : null;
    return list;
  });
  const selectOptions2 = computed(() => [
    {
      value: 'pprMortgageRegForm',
      label: `抵押信息登记证(2)`,
      finish: jsonToImage(formModel2.pprMortgageRegForm)?.length >= 2,
      required: true,
      min: 2,
      max: 9,
    },
  ]);
  const canSubmit1 = computed(() => {
    //node节点在进行中&&每项都有值时可提交
    if (!['ongoing', 'returned'].includes(nodeStatus1.value)) {
      return false;
    }

    const selectKeys = selectOptions.value.map((x) => x.value);
    for (const key of selectKeys) {
      if (!selectOptions.value.find((x) => x.value === key)?.finish) {
        return false;
      }
    }

    const filterKeys = [
      'gimaMortgageRegCert', //抵押信息登记证
      'gimaCommercialPolicy', //商业保单
      'gimaVehicleStatus', //机动车状态（12123截图)
      'gimaPolicePostReceipt', //警邮回执
      'gimaMortgageRegDjz', //警邮回执登记证
    ];
    for (const key of Object.keys(formModel).filter((x) => !filterKeys.includes(x))) {
      if (!formModel[key]) {
        return false;
      }
    }

    return true;
  });
  const canSubmit2 = computed(() => {
    //node节点在进行中&&每项都有值时可提交
    if (!['ongoing', 'returned'].includes(nodeStatus2.value)) {
      return false;
    }
    for (const key of Object.keys(formModel2)) {
      if (!formModel2[key]) {
        return false;
      }
    }

    for (const key of selectOptions2.value.map((x) => x.value)) {
      if (!selectOptions2.value.find((x) => x.value === key)?.finish) {
        return false;
      }
    }

    return true;
  });
  const formDisabled1 = computed(() => {
    return !!nodeStatus1.value && !['pending', 'ongoing', 'returned'].includes(nodeStatus1.value);
  });
  const formStatusDisabled1 = computed(() => {
    return (
      !!formData.value.gimaApprovalQuery &&
      [
        String(GpsInsuranceApproveStatus.Pass),
        String(GpsInsuranceApproveStatus.Refuse),
        String(GpsInsuranceApproveStatus.Back),
      ].includes(formData.value.gimaApprovalQuery)
    );
  });
  const formDisabled2 = computed(() => {
    return !!nodeStatus2.value && !['pending', 'ongoing', 'returned'].includes(nodeStatus2.value);
  });
  const formStatusDisabled2 = computed(() => {
    if (formData.value.gimaApprovalQuery !== String(GpsInsuranceApproveStatus.Pass)) {
      return true;
    }
    return [
      String(GpsInsuranceApproveStatus.Pass),
      String(GpsInsuranceApproveStatus.Refuse),
      String(GpsInsuranceApproveStatus.Back),
    ].includes(formData.value.pprApprovalResult);
  });
  const submitLoading1 = ref(false);
  const submitLoading2 = ref(false);

  //表单
  function canSaveForm(formModelVal) {
    let formModelKey = Object.keys(formModelVal);
    for (let i = 0; i < formModelKey.length; i++) {
      let val = formModelVal[formModelKey[i]];
      if (
        (Array.isArray(val) && val.length > 0) ||
        (typeof val === 'string' && val.trim() !== '') ||
        (typeof val !== 'string' && !Array.isArray(val) && val)
      ) {
        return true;
      }
    }
    return false;
  }
  const formRules = computed(() => {
    let {
      gimaMortgageRegCert,
      gimaPolicePostReceipt,
      gimaMortgageRegDjz,
      gimaCommercialPolicy,
      gimaVehicleStatus,
      ...rest
    } = formModel;
    let rules = {};
    Object.keys(rest).forEach((key) => {
      rules[key] = [
        {
          required: canSubmit1.value,
          message: `此项为必填项`,
          trigger: 'change,blur',
        },
      ];
    });
    return {
      ...rules,
      appendix: [
        {
          validator: (_rule, value) => {
            if (!canSubmit1.value) return true;
            for (const item of selectOptions.value) {
              if (!item.finish) {
                return new Error(`请上传${item.label}`);
              }
            }
            return true;
          },
          trigger: 'change',
        },
      ],
    };
  });

  const formRules2 = {
    appendix: [
      {
        validator: (_rule, value) => {
          if (!canSubmit2.value) return true;
          for (const item of selectOptions2.value) {
            if (!item.finish) {
              return new Error(`请完善${item.label}`);
            }
          }
          return true;
        },
        trigger: 'change',
      },
    ],
  };

  const searchLoading1 = ref(false);

  const searchLoading2 = ref(false);

  // 监听 gimaMortgageMaterial 的变化，同步到 store 并触发刷新
  watch(
    () => formModel.gimaMortgageMaterial,
    (newVal) => {
      if (newVal !== undefined) {
        orderDrawerStore.mortgageFields.gimaMortgageMaterial = newVal || undefined;
        // 只有在值确实变化时才触发刷新（避免初始化时重复刷新）
        orderDrawerStore.triggerRefresh();
      }
    }
  );

  onMounted(() => {
    getData();
    getOptions();
    // orderDrawerStore.triggerRefresh();
  });

  function getOptions() {
    getFieldConfig({ fieldName: ['gimaInsuranceChannel', 'gimaMortgageMaterial'] }).then((res) => {
      if (res.data) {
        res.data?.forEach((item) => {
          let options = item.fieldOptions ? JSON.parse(item.fieldOptions) : [];
          options = options.map((item) => ({
            label: item,
            value: item,
          }));
          if (item.fieldName === 'gimaInsuranceChannel') {
            insuranceChannelOptions.value = options;
          } else if (item.fieldName === 'gimaMortgageMaterial') {
            mortgageMaterialsOptions.value = options;
          }
        });
      }
    });
  }
  function getData() {
    let queryField: string[] = [...getField(formData.value)];
    function getField(model) {
      return Object.keys(model);
    }
    getFieldData({
      innerOrderNo: props.innerOrderNo,
      fieldName: queryField,
    }).then((res) => {
      if (res.data) {
        res.data?.forEach((item) => {
          formData.value[item.fieldName] = item.fieldValue;

          if (item.fieldName in formModel) {
            formModel[item.fieldName] = item.fieldValue;
          }
          if (item.fieldName in formModel2) {
            formModel2[item.fieldName] = item.fieldValue;
          }
          // 同步 gimaApprovalQuery 和 gimaMortgageMaterial 到 store
          if (item.fieldName === 'gimaApprovalQuery') {
            orderDrawerStore.mortgageFields.gimaApprovalQuery = item.fieldValue || undefined;
          } else if (item.fieldName === 'gimaMortgageMaterial') {
            orderDrawerStore.mortgageFields.gimaMortgageMaterial = item.fieldValue || undefined;
          }
        });
        /**
         * 根据原登记证初始化警邮登记证
         */
        if (!formModel.gimaMortgageRegDjz && formData.value.ipAttachOrigRegForm) {
          formModel.gimaMortgageRegDjz = formData.value.ipAttachOrigRegForm;
        }
      }
    });
  }
  const onSave = async ({ type = '', formRefNode, formModel, formIndex }) => {
    console.log(`formIndex`, formIndex);

    if (!canSaveForm(formModel)) {
      window.$message?.warning('请先填写表单');
      return;
    }

    let { errors } = await formRefNode?.validate();
    if (errors) {
      console.error('表单验证失败:', errors);
      throw new Error('表单验证失败');
    }

    let params = {
      ...formModel,
    };

    // 根据 formIndex 判断使用哪个 loading
    const currentSubmitLoading = formIndex === 1 ? submitLoading1 : submitLoading2;

    // 如果是提交操作，loading 由 handleSubmit 统一管理，这里不设置
    if (type !== 'submit') {
      currentSubmitLoading.value = true;
    }
    return saveFieldData({
      innerOrderNo: props.innerOrderNo,
      fieldData: params,
    })
      .then((res) => {
        if (res.code === 200) {
          formData.value = { ...formData.value, ...params };
          // 如果保存的是 gimaMortgageMaterial，同步到 store 并触发刷新
          if (formModel.gimaMortgageMaterial !== undefined) {
            orderDrawerStore.mortgageFields.gimaMortgageMaterial =
              formModel.gimaMortgageMaterial || undefined;
            orderDrawerStore.triggerRefresh();
          }
          return type !== 'submit' && window.$message?.success('保存成功');
        }
      })
      .finally(() => {
        // 如果是提交操作，loading 由 handleSubmit 统一管理，这里不清除
        if (type !== 'submit') {
          currentSubmitLoading.value = false;
        }
      });
  };
  const handleSubmit = async ({ formRefNode, formModel, formIndex }) => {
    // 根据 formIndex 判断使用哪个 loading、subNodeCode 和 handleSearch 函数
    const currentSubmitLoading = formIndex === 1 ? submitLoading1 : submitLoading2;
    const subNodeCode =
      formIndex === 1
        ? NodeCodeMap.mortgage.subNode.gpsInsuranceMortgageApproval
        : NodeCodeMap.mortgage.subNode.policePostReceipt;
    const handleSearch = formIndex === 1 ? handleSearch1 : handleSearch2;

    await onSave({ type: 'submit', formRefNode, formModel, formIndex });
    currentSubmitLoading.value = true;
    try {
      await submitForm({
        innerOrderNo: props.innerOrderNo,
        mainNodeCode: NodeCodeMap.mortgage.mainNode,
        subNodeCode,
      });
      window.$message?.success('提交成功');
      setTimeout(() => {
        handleSearch();
      }, 20);
    } finally {
      currentSubmitLoading.value = false;
    }
  };
  async function handleSearch1() {
    searchLoading1.value = true;
    await getMortgageAuditResult({ innerOrderNo: props.innerOrderNo });
    let { data } = await getFieldData({
      innerOrderNo: props.innerOrderNo,
      fieldName: ['gimaApprovalQuery'],
    });
    if (data && data.length > 0) {
      data.forEach((item) => {
        if (item.fieldName in formData.value) {
          formData.value[item.fieldName] = item.fieldValue;
        }
        // 同步 gimaApprovalQuery 到 store
        if (item.fieldName === 'gimaApprovalQuery') {
          orderDrawerStore.mortgageFields.gimaApprovalQuery = item.fieldValue || undefined;
        }
      });
    }
    orderDrawerStore.triggerRefresh();
    searchLoading1.value = false;
  }
  async function handleSearch2() {
    searchLoading2.value = true;
    await getGPSPoliceReceipt({ innerOrderNo: props.innerOrderNo });
    let { data } = await getFieldData({
      innerOrderNo: props.innerOrderNo,
      fieldName: ['pprApprovalResult', 'pprApprovalRemarks'],
    });
    if (data && data.length > 0) {
      data.forEach((item) => {
        if (item.fieldName in formData.value) {
          formData.value[item.fieldName] = item.fieldValue;
        }
      });
    }
    orderDrawerStore.triggerRefresh();
    searchLoading2.value = false;
  }
  // 预览
  const { previewImage: onPreview } = usePreviewImage();
</script>

<style lang="less" scoped></style>
