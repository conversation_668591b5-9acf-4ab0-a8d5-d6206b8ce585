<template>
  <div>
    <n-modal
      v-model:show="active"
      preset="card"
      v-bind="$attrs"
      :auto-focus="false"
      :mask-closable="false"
      :close-on-esc="false"
      class="w-[400px]"
    >
      <template #header>
        <p class="font-500">查询绑卡状态</p>
      </template>
      <n-space vertical>
        <n-form ref="formRef" :model="formModel" :rules="formRules">
          <n-form-item path="code" required class="relative">
            <n-input
              v-model:value="formModel.code"
              maxlength="6"
              placeholder="请输入验证码"
              @input="formModel.code = $event.replace(/\D/g, '')"
            />
            <n-button
              class="absolute right-2 top-1/2 -translate-y-1/2"
              size="small"
              type="primary"
              :disabled="sendCodeDisabled"
              :loading="sendCodeLoading"
              @click="handleSendCode"
            >
              {{ sendCodeText }}
            </n-button>
          </n-form-item>
        </n-form>
        <span>请联系客户获取动态验证码</span>
      </n-space>

      <template #action>
        <n-space justify="center">
          <n-button @click="onClose">关闭</n-button>
          <n-button type="primary" :loading="formLoading" @click="onConfirm">确认</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
  import { FormInst } from 'naive-ui';
  import { onMounted, ref, watch } from 'vue';
  import { getDeyiCardBindCode, submitDeyiCardBindCode } from '@/api/dashboard/deyi';
  const props = defineProps({
    innerOrderNo: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits<{
    (e: 'close'): void;
  }>();
  console.log(props.innerOrderNo, 'innerOrderNo');
  let timer: NodeJS.Timeout | null = null;
  const active = ref<boolean>(false);
  const sendCodeDisabled = ref<boolean>(false);
  const sendCodeLoading = ref<boolean>(false);
  const sendCodeText = ref<string>('发送验证码');
  const formLoading = ref<boolean>(false);

  const formRef = ref<FormInst | null>();
  const formModel = ref<{ code: string }>({
    code: '',
  });
  const formRules = {
    code: [
      {
        required: true,
        message: '请输入验证码',
        trigger: ['input', 'blur'],
      },
      {
        len: 6,
        message: '请输入6位验证码',
        trigger: ['input', 'blur'],
      },
    ],
  };

  watch(
    () => active.value,
    (val) => {
      if (!val) {
        setTimeout(() => {
          emit('close');
        }, 300);
      }
    }
  );

  onMounted(() => {
    active.value = true;
  });

  const onClose = () => {
    active.value = false;
  };

  const onConfirm = async () => {
    try {
      await formRef.value?.validate();
    } catch (error) {
      return;
    }

    if (formLoading.value) return;

    formLoading.value = true;

    try {
      await submitDeyiCardBindCode({
        innerOrderNo: props.innerOrderNo,
        code: formModel.value.code,
      });
      console.log('验证通过');
      active.value = false;
    } catch (error) {
      console.log('验证失败');
    } finally {
      formLoading.value = false;
    }
  };

  const handleSendCode = async () => {
    try {
      sendCodeDisabled.value = true;
      sendCodeLoading.value = true;

      // 请求发送验证码
      await getDeyiCardBindCode({ innerOrderNo: props.innerOrderNo });

      // 请求成功
      sendCodeLoading.value = false;
      let seconds = 60;
      sendCodeText.value = `${seconds}s`;
      timer && clearInterval(timer);
      timer = setInterval(() => {
        seconds--;
        sendCodeText.value = `${seconds}s`;
        if (seconds <= 0) {
          sendCodeDisabled.value = false;
          sendCodeText.value = '重新发送';
          timer && clearInterval(timer);
        }
      }, 1000);
    } catch (error) {
      sendCodeDisabled.value = false;
      sendCodeLoading.value = false;
      sendCodeText.value = '发送失败,请重试';
      timer && clearInterval(timer);
    }
  };
</script>

<style lang="less" scoped></style>
