<template>
  <div>
    <n-modal
      v-model:show="active"
      preset="card"
      v-bind="$attrs"
      :auto-focus="false"
      :mask-closable="false"
      :close-on-esc="false"
      class="w-[600px]"
    >
      <n-form
        ref="formRef"
        :model="formModal"
        :rules="formRules"
        label-placement="left"
        :label-width="100"
        validate-trigger="blur"
      >
        <n-form-item path="contractTypeId" label="签约类型">
          <n-select
            v-model:value="formModal.contractTypeId"
            :options="allSignType"
            label-field="contractType"
            value-field="id"
          />
        </n-form-item>
        <n-form-item path="contractTemplateId" label="签约模板">
          <n-select v-model:value="formModal.contractTemplateId" :options="signTemplateOptions" />
        </n-form-item>
        <n-form-item path="name" label="姓名">
          <n-input v-model:value="formModal.name" placeholder="请输入" :maxlength="30" show-count
        /></n-form-item>
        <n-form-item path="gender" label="性别">
          <n-select v-model:value="formModal.gender" :options="sexOptions" />
        </n-form-item>
        <n-form-item path="idCard" label="身份证">
          <n-input
            v-model:value="formModal.idCard"
            placeholder="请输入"
            :maxlength="18"
            show-count
          />
        </n-form-item>
        <n-form-item path="phone" label="联系电话">
          <n-input
            @input="(val) => (formModal.phone = val.replace(/\D/g, ''))"
            v-model:value="formModal.phone"
            placeholder="请输入"
            :maxlength="11"
            show-count
          />
        </n-form-item>
        <n-form-item path="address" label="联系地址">
          <CitySelect
            v-model="formModal.address"
            change-on-select
            :multiple="false"
            :value-field="'label'"
            :label-field="'label'"
            @update="handleCityUpdate"
            ref="citySelectRef"
          />
        </n-form-item>
        <n-form-item path="postalCode" label="邮编">
          <n-input v-model:value="formModal.postalCode" :disabled="postalCodeDisabled"
        /></n-form-item>
        <n-form-item path="productName" label="产品名称">
          <n-input
            v-model:value="formModal.productName"
            placeholder="请输入"
            :maxlength="30"
            show-count
        /></n-form-item>
        <n-form-item path="fundingEntity" label="资方主体">
          <n-input
            v-model:value="formModal.fundingEntity"
            placeholder="请输入"
            :maxlength="30"
            show-count
        /></n-form-item>
        <n-form-item path="contractAmount" label="签约金额">
          <n-input
            v-model:value="formModal.contractAmount"
            placeholder="请输入"
            @input="(val) => (formModal.contractAmount = val.replace(/\D/g, ''))"
          >
            <template #suffix> 元 </template>
          </n-input>
        </n-form-item>

        <n-space justify="center">
          <n-button @click="handleClose"> 取消 </n-button>
          <n-button type="primary" @click="handleSave"> 确定 </n-button>
        </n-space>
      </n-form>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
  import { sexOptions } from '@/enums/detailEnum';
  import { onMounted, reactive, ref, watch } from 'vue';
  import {
    getCityPostalCode,
    getContractRecordTypes,
    getSignTemplate,
    addRecordContract,
    renewRecordContract,
    type addContractRecordParams,
    contractRecordRepose,
  } from '@/api/detail';
  import { FormInst, FormRules } from 'naive-ui';
  import { validIdCard, validPhone } from '@/utils/formValidationRules';
  import { Fields } from '@/components/DrawerOrder/types';
  import { ListData } from '@/views/dashboard/workplace/orderManage/columns';
  type TFormModal = Omit<addContractRecordParams, 'clueId'> & { clueId: number | null };

  const props = defineProps<{
    rebuild: boolean;
    initialData: Partial<Fields>;
    rowData: ListData;
    gpsSignRecord: contractRecordRepose | null;
  }>();
  const emit = defineEmits(['close']);
  const citySelectRef = ref();
  const active = ref(false);
  const allSignType = ref([]);
  const signTemplateOptions = ref([]);
  const postalCodeDisabled = ref(false);

  watch(
    () => active.value,
    (val) => {
      if (!val) {
        setTimeout(() => {
          emit('close');
        }, 300);
      }
    }
  );

  const initialSignModel: TFormModal = {
    contractRecordId: null,
    contractTypeId: '',
    contractTemplateId: '',
    clueId: null,
    name: '',
    gender: null,
    idCard: '',
    phone: '',
    address: '',
    postalCode: '',
    productName: '',
    fundingEntity: '',
    contractAmount: '',
  };
  const formRef = ref<FormInst | null>(null);
  const formModal = reactive<TFormModal>({
    ...initialSignModel,
  });

  const formRules: FormRules = {
    contractTypeId: {
      required: true,
      message: '请选择签约类型',
      trigger: 'change',
      type: 'number',
    },
    name: { required: true, message: '请输入姓名', trigger: 'blur' },
    gender: { required: true, message: '请选择性别', trigger: 'change', type: 'number' },
    contractTemplateId: {
      required: true,
      message: '请选择签约模板',
      trigger: 'change',
      type: 'number',
    },
    idCard: [{ required: true, validator: validIdCard, trigger: 'blur' }],
    phone: [{ required: true, validator: validPhone, trigger: 'blur' }],
    address: [
      { required: true, message: '请选择联系地址', trigger: 'change' },
      {
        validator: (_rule, value: any) => {
          if (value) {
            const options = citySelectRef.value?.options ?? [];
            const parentIndex = options?.findIndex((item) => item.label === value);
            if (parentIndex !== -1 && options?.[parentIndex]?.children.length > 1) {
              return new Error('请选择市');
            }
          }
        },
        trigger: 'change',
      },
    ],
    postalCode: { required: true, message: '请输入邮编', trigger: 'blur' },
    productName: { required: true, message: '请输入产品名称', trigger: 'blur' },
    fundingEntity: { required: true, message: '请输入资方主体', trigger: 'blur' },
    contractAmount: {
      required: true,
      message: '请输入签约金额',
      trigger: 'blur',
      type: 'any',
    },
  };

  onMounted(() => {
    active.value = true;
    getSignType();
    getSignTemplateList();
    initialFormData();
  });

  const initialFormData = () => {
    const obj = props.gpsSignRecord?.extension || {};
    const keys = Object.keys(obj);
    for (const key of keys) {
      if (key in formModal && obj[key]) {
        formModal[key] = obj[key];
      }
    }
    formModal.clueId = props.rowData.clueId || null;
    formModal.name = props.initialData.preAuditName || '';
    formModal.idCard = props.initialData.preAuditIdCardNum || '';
    formModal.phone = props.initialData.preAuditPhoneNum || '';
  };

  const getSignType = async () => {
    const { data } = await getContractRecordTypes();
    if (data) {
      allSignType.value = data;
    }
  };

  const getSignTemplateList = async () => {
    let { data } = await getSignTemplate();
    signTemplateOptions.value = data?.map((item) => ({
      id: item.id,
      label: item.originTemplateFileName,
      value: item.id,
      ...item,
    }));
    return data;
  };

  const handleCityUpdate = async () => {
    if (formModal.address) {
      const { data } = await getCityPostalCode({ city: formModal.address });
      formModal.postalCode = data;
      postalCodeDisabled.value = !!formModal.postalCode;
    }
  };

  const getProvinceAndCity = () => {
    const city = formModal.address;
    const province = citySelectRef.value?.options?.find((item) =>
      item.children.find((cityItem) => cityItem.label === city && cityItem.value !== item.value)
    )?.label;
    return (province || '') + formModal.address;
  };

  const handleSave = async () => {
    try {
      await formRef.value?.validate();
      let api = props.rebuild ? renewRecordContract : addRecordContract;
      let address = getProvinceAndCity();
      await api({ ...formModal, address } as addContractRecordParams);
      window.$message.success(`操作成功`);

      handleClose();
    } catch (errors) {
      window.$message.error('请检查表单输入');
      return false;
    }
  };

  const handleClose = () => {
    active.value = false;
  };
</script>

<style lang="less" scoped></style>
