<template>
  <n-space vertical>
    <n-card>
      <!-- 面签阶段 -->
      <n-collapse arrow-placement="right" :expanded-names="expandedNames">
        <n-collapse-item name="3-1">
          <template #header>
            <Title :mainNode="3" :subNode="1" @click="toggleExpanded('3-1')" />
          </template>

          <div>
            <template v-if="!isCurrentStagePending('3-1')">
              <SubTitle title="面签阶段" />

              <!-- 改造：使用 n-descriptions 展示面签信息 -->
              <n-descriptions label-placement="top" bordered :column="3">
                <n-descriptions-item label="远程面签地址">
                  <n-space
                    v-if="
                      String(initialData.faceSignResult) !== String(FaceSignStatus.SignFail) &&
                      (!initialData.faceSignTime ||
                        (initialData.faceSignTime &&
                          dayjs(initialData.faceSignTime).isAfter(
                            dayjs(initialData.serverTime).subtract(1, 'day')
                          )))
                    "
                    align="center"
                    justify="center"
                  >
                    <n-text type="primary">
                      <!-- {{ initialData.faceSignRemoteAddress }} -->
                      <n-qr-code
                        v-if="initialData.faceSignRemoteAddress"
                        class="p-0"
                        id="faceSignRemoteAddress_qrcode"
                        :value="initialData.faceSignRemoteAddress"
                      />
                      <span v-else>-</span>
                    </n-text>
                    <n-button
                      v-if="initialData.faceSignRemoteAddress"
                      size="small"
                      type="primary"
                      @click="copyQrcode"
                    >
                      复制二维码
                    </n-button>
                  </n-space>
                  <n-space v-else align="center" justify="center">-</n-space>
                </n-descriptions-item>
                <n-descriptions-item label="签署结果">
                  <template v-if="initialData.faceSignResult">
                    <n-tag
                      size="small"
                      :type="
                        String(initialData.faceSignResult) === String(FaceSignStatus.Finish)
                          ? 'success'
                          : 'warning'
                      "
                    >
                      {{ FaceSignStatusMap[initialData.faceSignResult] }}
                    </n-tag>
                  </template>
                  <template v-else>-</template>
                </n-descriptions-item>
                <n-descriptions-item label="操作">
                  <n-button
                    size="small"
                    type="primary"
                    style="margin-right: 10px"
                    :disabled="
                      !(
                        isCurrentStageOngoing('3-1') &&
                        !initialData.faceSignRemoteAddress &&
                        !showReSignButton &&
                        String(initialData.faceSignResult) !== String(FaceSignStatus.Signing) &&
                        String(initialData.faceSignResult) !== String(FaceSignStatus.Processing)
                      )
                    "
                    v-cooldown
                    @click="onReCreateFaceSignUrl"
                  >
                    获取地址
                  </n-button>
                  <template v-if="showReSignButton">
                    <n-button size="small" type="primary" @click="onReCreateFaceSignUrl">
                      重新签署
                    </n-button>
                  </template>
                  <template v-else>
                    <n-button
                      size="small"
                      type="primary"
                      :disabled="
                        String(initialData.faceSignResult) === String(FaceSignStatus.Finish)
                      "
                      v-cooldown
                      @click="onQueryFaceSignStatus"
                    >
                      状态查询
                    </n-button>
                  </template>
                </n-descriptions-item>
              </n-descriptions>
            </template>

            <!-- 改造：审批信息使用 n-descriptions -->
            <n-descriptions
              v-if="String(initialData.faceSignResult) === String(FaceSignStatus.Finish)"
              label-placement="top"
              bordered
              :column="4"
              class="mt-4"
            >
              <n-descriptions-item label="签约机构">
                {{ initialData.deyiContractOrg || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="审批结果">
                <n-tag
                  size="small"
                  :type="
                    String(initialData.deyiApprovalResult) === String(FaceApproveStatus.Pass)
                      ? 'success'
                      : 'warning'
                  "
                >
                  {{
                    initialData.deyiApprovalResult
                      ? FaceApproveStatusMap[initialData.deyiApprovalResult]
                      : '-'
                  }}
                </n-tag>
              </n-descriptions-item>
              <n-descriptions-item label="审批备注">
                {{ initialData.deyiApprovalRemarks || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="操作">
                <n-button
                  size="small"
                  style="margin-right: 10px"
                  type="primary"
                  :disabled="
                    String(initialData.deyiApprovalResult) === String(FaceApproveStatus.Pass) ||
                    String(initialData.deyiApprovalResult) === String(FaceApproveStatus.Refuse)
                  "
                  v-cooldown
                  @click="onQueryDeyiSignApprovalStatus"
                >
                  状态查询
                </n-button>
                <n-button
                  size="small"
                  type="primary"
                  :disabled="
                    String(initialData.deyiApprovalResult) !==
                      String(FaceApproveStatus.WaitSubmit) &&
                    String(initialData.deyiApprovalResult) !== String(FaceApproveStatus.Back)
                  "
                  v-cooldown
                  @click="onQueryDeyiSign"
                >
                  重新签署
                </n-button>
              </n-descriptions-item>
            </n-descriptions>
          </div>
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import { onMounted, inject, computed } from 'vue';
  import { useBaseData } from './composables/useBaseData';
  import {
    getFaceSignStatus,
    getFaceSignUrl,
    getDeyiSignApprovalStatus,
    onQuerySign,
  } from '@/api/dashboard/deyi';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import dayjs from 'dayjs';
  import {
    FaceApproveStatus,
    FaceApproveStatusMap,
    FaceSignStatus,
    FaceSignStatusMap,
  } from '@/components/DrawerOrder/enum';
  import { useFormDisabled } from '@/components/DrawerOrder/composables/useFormDisabled';
  import { copyCanvasToClipboard } from '@/utils/copyCanvasToClipboard';

  interface Props {
    innerOrderNo: string;
  }
  const props = defineProps<Props>();
  const expandedNames = inject('expandedNames');
  const toggleExpanded = inject('toggleExpanded') as (name: string) => void;
  const orderDrawerStore = useOrderDrawerStore();
  const { isCurrentStagePending, isCurrentStageOngoing } = useFormDisabled();
  const showReSignButton = computed(() => {
    const faceSignResult = String(initialData.faceSignResult);
    if (faceSignResult === String(FaceSignStatus.SignFail)) {
      return true;
    }

    if (faceSignResult !== String(FaceSignStatus.Finish) && initialData.faceSignTime) {
      return dayjs(initialData.faceSignTime).isBefore(
        dayjs(initialData.serverTime).subtract(1, 'day')
      );
    }

    return false;
  });

  // 拉取数据
  const {
    data: initialData,
    getData,
    getOptions,
  } = useBaseData({
    innerOrderNo: props.innerOrderNo,
    keys: [
      'faceSignRemoteAddress',
      'faceSignResult',
      'faceSignTime',
      'deyiContractOrg',
      'deyiApprovalResult',
      'deyiApprovalRemarks',
    ],
  });
  const onReCreateFaceSignUrl = async () => {
    await getFaceSignUrl({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };

  const onQueryFaceSignStatus = async () => {
    await getFaceSignStatus({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };
  const onQueryDeyiSign = async () => {
    await onQuerySign({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };
  const onQueryDeyiSignApprovalStatus = async () => {
    await getDeyiSignApprovalStatus({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };
  //复制签约二维码
  async function copyQrcode() {
    const canvas = document.getElementById('faceSignRemoteAddress_qrcode')?.querySelector('canvas');
    canvas && (await copyCanvasToClipboard(canvas));
    window.$message.success('复制成功');
  }
  onMounted(() => {
    getData();
    getOptions();
  });
</script>

<style lang="less" scoped></style>
