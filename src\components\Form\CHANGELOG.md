# 表单组件更新日志

## [1.1.3] - 2024-12-19

### 🐛 Bug修复
- 🔧 **重置功能修复**：修复重置表单时 schema 默认值被清空的问题
- 📝 **逻辑优化**：改进初始化逻辑，确保默认值模型正确保存
- ✅ **测试完善**：新增重置功能测试示例

### 技术改进
- **initDefault 函数优化**：先保存默认值模型，再处理缓存恢复
- **resetFields 函数修复**：正确恢复到 schema 中定义的默认值
- **边界情况处理**：区分 undefined 和 null 值的处理

## [1.1.2] - 2024-12-19

### 新增功能
- ✨ **展开收起状态缓存**：表单的展开收起状态现在也会被自动缓存和恢复
- 🔄 **状态同步**：展开收起状态变化时自动保存，页面刷新后自动恢复

### 优化
- 🔧 **缓存逻辑增强**：同时缓存表单数据和UI状态，提供更完整的用户体验
- 📝 **文档更新**：更新相关文档说明展开收起状态缓存功能

## [1.1.1] - 2024-12-19

### 优化
- 🔧 **缓存时间优化**：默认缓存时间改为当天24点，每天都是新的开始，更符合日常使用习惯

## [2.0.0] - 2024-10-14 - 🎉 动态表单重大更新

### 🚀 新增核心组件
- ✨ **DynamicForm 组件**: 全新的基于配置的动态表单组件
- ✨ **useDynamicForm Hook**: 提供表单状态管理和操作方法

### 🎯 字段类型支持 (30+ 种)
- ✅ **基础输入**: input, textarea, password, number
- ✅ **选择类**: select, multiSelect, radio, checkbox, checkboxGroup
- ✅ **日期时间**: date, dateRange, time, timeRange, datetime, datetimeRange
- ✅ **特殊组件**: switch, upload, rate, slider, colorPicker
- ✅ **高级组件**: cascader, treeSelect, transfer, mention
- ✅ **动态组件**: dynamicInput, dynamicTags
- ✅ **布局组件**: divider
- ✅ **自定义组件**: 支持注册和使用自定义组件

### 🛡️ 增强的表单校验
- ✅ **内置校验规则**: 必填、长度、格式、范围等 20+ 种校验规则
- ✅ **异步校验**: 支持远程校验和防抖处理
- ✅ **条件校验**: 基于表单数据的条件校验
- ✅ **自定义校验**: 支持自定义校验函数
- ✅ **依赖校验**: 字段间的依赖校验
- ✅ **文件校验**: 文件大小、类型校验

### 🔗 强大的字段联动
- ✅ **显示隐藏联动**: 基于条件控制字段显示/隐藏
- ✅ **启用禁用联动**: 基于条件控制字段启用/禁用状态
- ✅ **选项联动**: 动态更新字段选项（如省市联动）
- ✅ **值联动**: 基于其他字段自动计算值
- ✅ **校验联动**: 动态添加或移除校验规则
- ✅ **多条件联动**: 支持 AND/OR 逻辑组合
- ✅ **循环依赖检测**: 自动检测并警告循环依赖

### 🎨 表单功能增强
- ✅ **响应式布局**: 基于栅格系统的响应式布局
- ✅ **调试模式**: 开发时的调试信息显示
- ✅ **加载状态**: 表单提交时的加载状态管理
- ✅ **只读模式**: 表单只读模式支持
- ✅ **动态字段**: 运行时添加/删除/更新字段
- ✅ **表单模板**: 快速应用预定义表单模板
- ✅ **批量操作**: 批量更新字段配置

### 📚 完善的文档和示例
- ✅ **完整文档**: README.md 包含详细的使用说明和 API 参考
- ✅ **快速开始**: QUICK_START.md 提供 5 分钟上手指南
- ✅ **示例代码**: 多个完整的使用示例和演示页面
- ✅ **最佳实践**: 性能优化和可维护性建议

### 🛠️ 技术特性
- ✅ **TypeScript**: 完整的类型定义和类型安全
- ✅ **Vue 3**: 基于 Vue 3 Composition API
- ✅ **Naive UI**: 完全兼容 Naive UI 组件库
- ✅ **模块化设计**: 各功能模块独立，便于维护和扩展
- ✅ **插件化架构**: 支持自定义组件和校验器
- ✅ **性能优化**: 合理的更新策略和懒加载

### 🔄 向后兼容
- ✅ **保持兼容**: 原有 BasicForm 组件保持不变
- ✅ **渐进升级**: 可以逐步迁移到新的动态表单组件
- ✅ **迁移指南**: 提供详细的迁移指南和示例

### 📦 新增文件
```
src/components/Form/
├── src/
│   ├── DynamicForm.vue           # 主组件
│   ├── types/dynamicForm.ts      # 动态表单类型定义
│   ├── utils/
│   │   ├── validators.ts         # 增强的校验工具
│   │   ├── componentMap.ts       # 组件映射管理


│   └── hooks/
│       └── useDynamicForm.ts    # 动态表单 Hook
├── examples/
│   └── BasicExample.vue         # 示例组件
├── test/
│   └── DynamicForm.test.ts      # 单元测试
├── README.md                    # 完整文档
├── QUICK_START.md              # 快速开始指南
└── CHANGELOG.md                # 更新日志
```

### 🎯 使用场景
- 🏢 **后台管理系统**: 各种表单页面快速开发
- 📝 **数据录入**: 复杂的数据录入表单
- ⚙️ **配置页面**: 系统配置和设置页面
- 👤 **用户注册**: 用户注册和信息收集
- 📊 **问卷调查**: 动态问卷和调查表单
- 🔄 **工作流**: 审批和工作流表单

### 🚀 快速开始
```vue
<template>
  <DynamicForm
    :config="formConfig"
    v-model="formData"
    @submit="handleSubmit"
  />
</template>

<script setup>
import { DynamicForm, FiledOptions, validators } from '@/components/Form';

const formConfig = {
  fields: [
    {
      field: 'name',
      label: '姓名',
      type: FiledOptions.INPUT,
      required: true,
      rules: [validators.required('请输入姓名')]
    },
    {
      field: 'email',
      label: '邮箱',
      type: FiledOptions.INPUT,
      rules: [validators.email()]
    }
  ]
};
</script>
```
- 📝 **文档更新**：更新相关文档说明新的缓存时间策略

## [1.1.0] - 2024-12-19

### 新增功能
- ✨ **表单缓存功能**：支持本地缓存表单数据，解决同一路由多次使用表单组件的数据保持问题
  - 自动保存表单数据到本地存储
  - 支持自动恢复缓存数据
  - 支持自定义缓存key，解决同一路由多个表单的冲突问题
  - 支持设置缓存过期时间
  - 只缓存有值的字段，避免存储空数据
  - 提供完整的缓存操作API

### 新增配置项
- `enableCache`: 是否启用缓存功能（默认：false）
- `cacheKey`: 自定义缓存key（默认：使用路由路径）
- `cacheTimeout`: 缓存过期时间，单位秒（默认：缓存到当天24点）
- `autoRestoreCache`: 是否自动恢复缓存（默认：true）

### 新增API方法
- `saveFormCache()`: 手动保存表单缓存
- `restoreFormCache()`: 手动恢复表单缓存
- `clearFormCache()`: 清除表单缓存
- `hasCacheData()`: 检查是否有缓存数据

### 技术实现
- 新增 `useFormCache` hook 处理缓存逻辑
- 集成到 `BasicForm` 组件中
- 使用项目现有的 Storage 工具类
- 支持深度监听表单数据变化
- 防抖机制避免频繁缓存操作

### 使用场景
1. **同一路由多个表单**：通过不同的 cacheKey 区分
2. **搜索表单**：保留用户搜索条件
3. **长表单填写**：避免意外刷新导致数据丢失
4. **表单草稿**：临时保存用户输入

### 示例代码
```vue
<template>
  <BasicForm
    :schemas="schemas"
    :enable-cache="true"
    cache-key="user-form"
    :cache-timeout="60 * 60 * 24"
    @submit="handleSubmit"
  />
</template>
```

### 文档和测试
- 新增缓存功能使用文档 `docs/FormCache.md`
- 新增使用示例 `examples/FormCacheExample.vue`
- 新增单元测试 `tests/FormCache.test.ts`

### 兼容性
- ✅ 向后兼容，默认不启用缓存功能
- ✅ 不影响现有表单组件的使用
- ✅ 可选择性启用缓存功能

---

## [1.0.0] - 之前版本

### 基础功能
- 基础表单组件实现
- 动态表单配置
- 表单验证
- 列显示/隐藏配置
- 响应式布局
- 多种表单控件支持
