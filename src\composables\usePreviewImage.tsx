import { useMountComponent } from '@/composables/useMountComponent';
import { defineComponent, PropType, ref, onMounted, nextTick, onBeforeUnmount, toRaw } from 'vue';
import { NImageGroup, NImage } from 'naive-ui';

const PreviewImage = defineComponent({
  name: 'PreviewImage',
  props: {
    images: {
      type: Array as PropType<string[]>,
      required: true,
    },
    startIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ['close'],
  setup(props, { emit }) {
    const imgRefs = ref<any[]>([]);
    let mo: MutationObserver | null = null;

    onMounted(async () => {
      await nextTick();
      // 触发对应索引图片的点击，打开预览分组
      const index = Math.min(Math.max(props.startIndex || 0, 0), props.images.length - 1);
      const el = imgRefs.value[index] as any;
      setTimeout(() => {
        try {
          el?.$el?.click?.();
          el?.el?.click?.();
          el?.click?.();
        } catch {}
      }, 0);

      // 观察预览容器的挂载/卸载，关闭后自动卸载组件
      try {
        mo = new MutationObserver(() => {
          const hasPreview = document.body.querySelector('.n-image-preview-container');
          if (!hasPreview) {
            emit('close');
          }
        });
        mo.observe(document.body, { childList: true, subtree: true });
      } catch {}
    });

    onBeforeUnmount(() => {
      mo?.disconnect?.();
      mo = null;
    });

    return () => (
      <NImageGroup>
        {/* 放入隐藏图片用于预览分组 */}
        {props.images.map((src, idx) => (
          <NImage
            class="hidden"
            src={src}
            key={src + idx}
            ref={(el: any) => (imgRefs.value[idx] = el)}
          />
        ))}
      </NImageGroup>
    );
  },
});

export function usePreviewImage() {
  const { mountPromisify } = useMountComponent();

  const previewImage = (images: string | string[], startIndex = 0) => {
    const list = typeof images === 'string' ? [images] : images;
    return mountPromisify({
      render: (ctx) => <PreviewImage images={list} startIndex={startIndex} onClose={ctx.unmount} />,
    });
  };
  return {
    previewImage,
  };
}
