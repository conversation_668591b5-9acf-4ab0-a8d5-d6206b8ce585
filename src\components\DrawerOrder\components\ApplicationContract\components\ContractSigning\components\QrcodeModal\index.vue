<template>
  <div>
    <n-modal
      v-model:show="active"
      preset="card"
      v-bind="$attrs"
      :auto-focus="false"
      :mask-closable="false"
      :close-on-esc="false"
      class="w-[400px]"
    >
      <template #header>
        <p class="font-500">签约二维码</p>
      </template>
      <n-space vertical align="center">
        <n-qr-code class="p-0" :value="value" />
        <p>({{ `通过签约地址生成，可扫码访问` }})</p>
      </n-space>

      <template #action>
        <n-space justify="center">
          <n-button @click="active = false">关闭</n-button>
          <n-button type="primary" @click="copyQrcode">复制二维码</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';

  const props = defineProps<{
    value: string;
  }>();

  const emit = defineEmits<{
    (e: 'close'): void;
  }>();

  const active = ref(false);

  watch(
    () => active.value,
    (val) => {
      if (!val) {
        setTimeout(() => {
          emit('close');
        }, 300);
      }
    }
  );

  onMounted(() => {
    active.value = true;
  });

  const copyQrcode = () => {
    navigator.clipboard.writeText(props.value).then(() => {
      window.$message?.success('二维码已复制到剪贴板');
      active.value = false;
    });
  };
</script>

<style lang="less" scoped></style>
