<template>
  <n-card>
    <!-- 德易签约 -->
    <n-collapse arrow-placement="right" :expanded-names="expandedNames">
      <n-collapse-item name="2-6">
        <template #header>
          <Title :mainNode="2" :subNode="6" @click="toggleExpanded('2-6')" />
        </template>

        <n-space v-if="!isCurrentStagePending('2-6')" vertical>
          <SubTitle title="德易签约" />

          <!-- 改造：德易签约（地址/结果/操作）使用 n-descriptions -->
          <n-descriptions label-placement="top" bordered :column="3" class="mb-4">
            <n-descriptions-item label="德易绑卡签约合同签署地址">
              <n-space
                v-if="
                  (String(initialData.deyiSignResult) === String(DeyiSignStatus.UnSign) &&
                    (!initialData.deyiSignTime ||
                      (initialData.deyiSignTime &&
                        dayjs(initialData.deyiSignTime).isAfter(
                          dayjs(initialData.serverTime).subtract(1, 'day')
                        )))) ||
                  String(initialData.deyiSignResult) === String(DeyiSignStatus.SignFinish)
                "
                align="center"
                justify="center"
              >
                <n-text type="primary">
                  <!-- {{ initialData.deyiSignAddress }} -->
                  <n-qr-code
                    class="p-0"
                    v-if="initialData.deyiSignAddress"
                    id="deyiSignAddress_qrcode"
                    :value="initialData.deyiSignAddress"
                  />
                </n-text>
                <n-button
                  v-if="initialData.deyiSignAddress"
                  size="small"
                  type="primary"
                  @click="copyQrcode"
                >
                  复制二维码
                </n-button>
              </n-space>
              <n-space v-else align="center" justify="center">-</n-space>
            </n-descriptions-item>
            <n-descriptions-item label="签署结果">
              <template v-if="initialData.deyiSignResult">
                <n-tag
                  size="small"
                  :type="
                    String(initialData.deyiSignResult) === String(DeyiSignStatus.SignFinish)
                      ? 'success'
                      : 'warning'
                  "
                  >{{ DeyiSignStatusMap[initialData.deyiSignResult] }}</n-tag
                >
              </template>
              <template v-else>-</template>
            </n-descriptions-item>
            <n-descriptions-item label="操作">
              <n-button
                style="margin-right: 10px"
                size="small"
                type="primary"
                :disabled="!isCurrentStageOngoing('2-6')"
                v-cooldown
                @click="onReCreateDeyiSignUrl"
              >
                获取地址
              </n-button>
              <template
                v-if="
                  initialData.deyiSignResult &&
                  String(initialData.deyiSignResult) === String(DeyiSignStatus.UnSign) &&
                  initialData.deyiSignTime &&
                  dayjs(initialData.deyiSignTime).isBefore(
                    dayjs(initialData.serverTime).subtract(1, 'day')
                  )
                "
              >
                <n-button size="small" type="primary" @click="onReCreateDeyiSignUrl">
                  重新发起
                </n-button>
              </template>
              <template v-else>
                <n-button
                  size="small"
                  type="primary"
                  :disabled="
                    !initialData.deyiSignResult ||
                    String(initialData.deyiSignResult) === String(DeyiSignStatus.SignFinish)
                  "
                  v-cooldown
                  @click="onQueryDeyiSignStatus"
                >
                  状态查询
                </n-button>
              </template>
            </n-descriptions-item>
          </n-descriptions>
        </n-space>
      </n-collapse-item>
    </n-collapse>
  </n-card>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import { onMounted, inject } from 'vue';
  import { useBaseData } from '../../composables/useBaseData';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import { DeyiSignStatus, DeyiSignStatusMap } from '@/components/DrawerOrder/enum';
  import { getDeyiSignStatus, getDeyiSignUrl } from '@/api/dashboard/deyi';
  import dayjs from 'dayjs';
  import { useFormDisabled } from '@/components/DrawerOrder/composables/useFormDisabled';
  import { copyCanvasToClipboard } from '@/utils/copyCanvasToClipboard';

  interface Props {
    innerOrderNo: string;
  }
  const { isCurrentStagePending, isCurrentStageOngoing } = useFormDisabled();
  const props = defineProps<Props>();
  const expandedNames = inject('expandedNames');
  const toggleExpanded = inject('toggleExpanded') as (name: string) => void;
  const orderDrawerStore = useOrderDrawerStore();

  // 拉取数据
  const {
    data: initialData,
    getData,
    getOptions,
  } = useBaseData({
    innerOrderNo: props.innerOrderNo,
    keys: ['deyiSignAddress', 'deyiSignResult', 'deyiSignTime'],
  });

  const onReCreateDeyiSignUrl = async () => {
    await getDeyiSignUrl({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };
  const onQueryDeyiSignStatus = async () => {
    await getDeyiSignStatus({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };
  //复制签约二维码
  async function copyQrcode() {
    const canvas = document.getElementById('deyiSignAddress_qrcode')?.querySelector('canvas');
    canvas && (await copyCanvasToClipboard(canvas));
    window.$message.success('复制成功');
  }
  onMounted(() => {
    getData();
    getOptions();
  });
</script>

<style lang="less" scoped></style>
