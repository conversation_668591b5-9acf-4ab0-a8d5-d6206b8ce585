<template>
  <div class="dynamic-form-demo">
    <n-card title="动态表单组件演示" class="mb-4">
      <p>这是一个功能完整的动态表单组件演示页面，展示了各种字段类型、校验规则、联动逻辑等功能。</p>
    </n-card>
    <!-- 联动表单示例 -->
    <n-card title="字段联动示例" class="mb-4">
      <DynamicForm
        ref="linkageFormRef"
        v-model:config="linkageFormConfig"
        v-model="linkageFormData"
      />
    </n-card>
  </div>
</template>

<script setup lang="tsx">
  import { ref, computed, reactive } from 'vue';
  import { NCard, useMessage } from 'naive-ui';
  import type { DataTableColumns } from 'naive-ui';
  import { DynamicForm, FiledOptions, validators } from '@/components/Form';
  import type { DynamicFormInstance } from '@/components/Form/src/types/dynamicForm';
  import BaseTableV2 from '@/components/BaseTableV2/index.vue';

  const message = useMessage();

  // 表单引用
  const linkageFormRef = ref<DynamicFormInstance>();

  // 表单数据
  const linkageFormData = ref<any>({
    userType: 'enterprise',
    username: 'admin1',
    personalName: '张三',
    companyName: 'ABC公司',
    hasContract: true,
    tableId: [3, 2],
    actionAge: [
      {
        id: 2,
      },
    ],
    contractFile: [
      { url: 'https://tfile.haoxincd.cn/test/20251017/file1760683152160.docx', id: 176 },
      { url: 'https://tfile.haoxincd.cn/test/20251017/file1760683156172.docx', id: 17606 },
    ],
  });
  const columns: DataTableColumns<any> = [
    {
      type: 'selection',
      width: 60,
    },
    { title: 'ID', key: 'id' },
    { title: 'Name', key: 'name' },
    {
      title: 'Age',
      key: 'age',
      render(row) {
        return <n-input v-model:value={row.age} />;
      },
    },
  ];
  const tableData = reactive([
    { id: 1, name: 'John', age: '1' },
    { id: 2, name: 'Jane', age: '2' },
    { id: 3, name: 'Jane3', age: '3' },
  ]);
  // 联动表单配置
  const linkageFormConfig = computed(() => ({
    labelWidth: 120,
    columns: 24,
    showSubmitButton: true,
    submitButtonText: '提交11',
    onSubmit: async (data) => {
      let checkedRow = tableData.filter((item) => data.tableId.includes(item.id));
      console.log(checkedRow, '-----');
      await new Promise((resolve) => setTimeout(resolve, 1000));
      message.success('提交成功！');
      //成功之后的处理
      linkageFormRef.value?.resetForm();
      return data;
    },
    fields: [
      {
        field: 'userType',
        label: '用户类型',
        type: FiledOptions.SELECT,
        required: true,
        span: 12,
        render: (props, ctx) => {
          return (
            <n-select
              value={props.value}
              options={[
                { label: '个人用户', value: 'personal' },
                { label: '企业用户', value: 'enterprise' },
              ]}
              onUpdateValue={(value) => {
                ctx.emit('update:value', value);
              }}
            />
          );
        },
        options: [
          { label: '个人用户', value: 'personal' },
          { label: '企业用户', value: 'enterprise' },
        ],
      },
      {
        field: 'username',
        label: '用户名',
        type: FiledOptions.INPUT,
        required: true,
        span: 12,
        rules: [
          validators.required('请输入用户名'),
          validators.remote(async (value) => {
            // 模拟异步校验
            await new Promise((resolve) => setTimeout(resolve, 500));
            return value !== 'admin'; // admin 用户名不可用
          }, '用户名已存在'),
        ],
      },
      {
        field: 'personalName',
        label: '个人姓名',
        type: FiledOptions.INPUT,
        required: true,
        span: 12,
        hidden: linkageFormData.value?.userType !== 'personal',
      },
      {
        field: 'companyName',
        label: '公司名称',
        type: FiledOptions.INPUT,
        required: true,
        span: 12,
        hidden: linkageFormData.value?.userType !== 'enterprise',
      },
      {
        field: 'hasContract',
        label: '是否签署合同',
        type: FiledOptions.SWITCH,
        span: 24,
      },
      {
        field: 'tableId',
        label: '选择关联',
        span: 24,
        required: true,
        type: FiledOptions.DATA_TABLE,
        render: (props, ctx) => {
          return (
            <BaseTableV2
              value={props.value}
              columns={columns}
              data={tableData}
              checked-row-keys={props.value}
              rowKey={(row) => row.id}
              onUpdate:checked-row-keys={(value) => {
                ctx.emit('update:value', value);
              }}
            />
          );
        },
        rules: [validators.arrayLength(1, 10000, '请勾选关联项')],
      },
      {
        field: 'contractFile',
        label: '合同文件',
        type: FiledOptions.UPLOAD,
        span: 24,
        required: true,
        hidden: linkageFormData.value.hasContract !== true,
        componentProps: {
          accept: '.pdf,.doc,.docx',
          maxSize: 15,
          maxCount: 2,
          multiple: true,
        },
        suffix: '文件大小不能超过1M',
        rules: [validators.arrayLength(1, 2, '请上传文件'), validators.fileSize(1, 'MB')],
      },
    ],
  }));
  // 表单操作方法
  const validateBasicForm = async () => {
    const isValid = await linkageFormRef.value?.validate();
    console.log('校验结果:', isValid, linkageFormData.value);
    message.info(isValid ? '表单校验通过' : '表单校验失败');
  };
</script>

<style scoped>
  .dynamic-form-demo {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .mb-4 {
    margin-bottom: 16px;
  }
</style>
