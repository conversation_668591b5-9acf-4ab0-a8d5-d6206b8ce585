import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import DynamicForm from '../src/DynamicForm.vue';
import { FieldType, validators } from '../index';
import type { DynamicFormConfig } from '../src/types/dynamicForm';

describe('DynamicForm', () => {
  let wrapper: any;
  let config: DynamicFormConfig;

  beforeEach(() => {
    config = {
      fields: [
        {
          field: 'name',
          label: '姓名',
          type: FieldType.INPUT,
          required: true,
          rules: [validators.required('姓名不能为空')],
        },
        {
          field: 'email',
          label: '邮箱',
          type: FieldType.INPUT,
          rules: [validators.email()],
        },
        {
          field: 'age',
          label: '年龄',
          type: FieldType.NUMBER,
          componentProps: {
            min: 1,
            max: 120,
          },
        },
      ],
    };
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  it('应该正确渲染表单字段', async () => {
    wrapper = mount(DynamicForm, {
      props: {
        config,
      },
    });

    await nextTick();

    // 检查是否渲染了正确数量的表单项
    const formItems = wrapper.findAll('.n-form-item');
    expect(formItems).toHaveLength(3);

    // 检查字段标签
    expect(wrapper.text()).toContain('姓名');
    expect(wrapper.text()).toContain('邮箱');
    expect(wrapper.text()).toContain('年龄');
  });

  it('应该正确处理表单数据绑定', async () => {
    const modelValue = { name: '张三', email: '<EMAIL>' };

    wrapper = mount(DynamicForm, {
      props: {
        config,
        modelValue,
      },
    });

    await nextTick();

    // 检查初始值是否正确设置
    const nameInput = wrapper.find('input[placeholder*="姓名"]');
    const emailInput = wrapper.find('input[placeholder*="邮箱"]');

    expect(nameInput.element.value).toBe('张三');
    expect(emailInput.element.value).toBe('<EMAIL>');
  });

  it('应该正确触发字段变化事件', async () => {
    wrapper = mount(DynamicForm, {
      props: {
        config,
      },
    });

    await nextTick();

    const nameInput = wrapper.find('input[placeholder*="姓名"]');
    await nameInput.setValue('新姓名');

    // 检查是否触发了 field-change 事件
    expect(wrapper.emitted('field-change')).toBeTruthy();
    expect(wrapper.emitted('update:modelValue')).toBeTruthy();
  });

  it('应该正确处理表单校验', async () => {
    wrapper = mount(DynamicForm, {
      props: {
        config,
      },
    });

    await nextTick();

    // 获取表单实例
    const formInstance = wrapper.vm;

    // 测试必填字段校验
    const isValid = await formInstance.validate();
    expect(isValid).toBe(false); // 姓名为必填，应该校验失败

    // 设置姓名值后再次校验
    await formInstance.setFieldValue('name', '张三');
    const isValidAfterSet = await formInstance.validate();
    expect(isValidAfterSet).toBe(true);
  });

  it('应该正确处理表单提交', async () => {
    const onSubmit = vi.fn();

    wrapper = mount(DynamicForm, {
      props: {
        config: {
          ...config,
          onSubmit,
        },
      },
    });

    await nextTick();

    // 设置表单数据
    const formInstance = wrapper.vm;
    await formInstance.setFormData({
      name: '张三',
      email: '<EMAIL>',
      age: 25,
    });

    // 提交表单
    await formInstance.submit();

    // 检查是否调用了 onSubmit 回调
    expect(onSubmit).toHaveBeenCalledWith({
      name: '张三',
      email: '<EMAIL>',
      age: 25,
    });
  });

  it('应该正确处理表单重置', async () => {
    wrapper = mount(DynamicForm, {
      props: {
        config,
        modelValue: { name: '张三', email: '<EMAIL>' },
      },
    });

    await nextTick();

    const formInstance = wrapper.vm;

    // 修改表单数据
    await formInstance.setFieldValue('name', '李四');

    // 重置表单
    await formInstance.resetForm();

    // 检查数据是否重置
    const formData = formInstance.getFormData();
    expect(formData.name).toBe('张三'); // 应该恢复到初始值
  });
});

// 表单配置测试
describe('Form Configuration', () => {
  it('应该正确创建表单配置', () => {
    const { FiledOptions, validators } = require('../index');

    const config = {
      labelWidth: 120,
      columns: 12,
      fields: [
        {
          field: 'name',
          label: '姓名',
          type: FiledOptions.INPUT,
          required: true,
          rules: [validators.required('请输入姓名')],
        },
        {
          field: 'email',
          label: '邮箱',
          type: FiledOptions.INPUT,
          rules: [validators.email()],
        },
        {
          field: 'age',
          label: '年龄',
          type: FiledOptions.NUMBER,
        },
      ],
    };

    expect(config.labelWidth).toBe(120);
    expect(config.columns).toBe(12);
    expect(config.fields).toHaveLength(3);
    expect(config.fields[0].field).toBe('name');
    expect(config.fields[0].required).toBe(true);
  });
});

// 校验器测试
describe('Validators', () => {
  it('应该正确校验必填字段', () => {
    const rule = validators.required('此字段为必填项');
    expect(rule.required).toBe(true);
    expect(rule.message).toBe('此字段为必填项');
  });

  it('应该正确校验邮箱格式', () => {
    const rule = validators.email();
    expect(rule.pattern).toBeDefined();
    expect(rule.message).toContain('邮箱');
  });

  it('应该正确校验手机号格式', () => {
    const rule = validators.phone();
    expect(rule.pattern).toBeDefined();
    expect(rule.message).toContain('手机号');
  });

  it('应该正确校验长度范围', () => {
    const rule = validators.length(2, 20);
    expect(rule.min).toBe(2);
    expect(rule.max).toBe(20);
  });

  it('应该正确校验数字范围', () => {
    const rule = validators.numberRange(1, 100);
    expect(rule.type).toBe('number');
    expect(rule.min).toBe(1);
    expect(rule.max).toBe(100);
  });
});
