import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
import { computed, toRef } from 'vue';
import { getSubNode } from '../utils';

export function useFormDisabled() {
  const orderDrawerStore = useOrderDrawerStore();
  const currentStage = toRef(orderDrawerStore, 'currentStage');
  const subStages = computed(() => currentStage.value?.subStages || []);

  // 当前节点是否处于未开始
  const isCurrentStagePending = (id: string): boolean => {
    return getSubNode(subStages.value, id)?.status === 'pending';
  };

  const isCurrentStageCompleted = (id: string) => {
    return getSubNode(subStages.value, id)?.status === 'completed';
  };
  // 判断阶段是否进行中
  const isCurrentStageOngoing = (id: string) => {
    return getSubNode(subStages.value, id)?.status === 'ongoing';
  };
  return {
    isCurrentStagePending,
    isCurrentStageCompleted,
    isCurrentStageOngoing,
  };
}
