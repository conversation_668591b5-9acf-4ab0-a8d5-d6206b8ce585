import { BasicColumn } from '@/components/Table';
import dayjs from 'dayjs';

export interface ListData {
  id: number; // 主键（自增）
  clueId: number; // 线索ID
  innerOrderNo: string; // 内部单号（业务唯一标识）
  realName: string; // 线索客户姓名
  orderCustomerName: string; // 订单客户姓名
  mobileNo: string; // 线索手机号
  mobileNoMd5: string; // 线索手机号MD5
  orderMobile: string; // 订单手机号
  orderMobileMd5: string; // 订单手机号MD5
  loanOrderId: string; // 贷款订单ID
  managementCodeStr: string; // 资方标识（转义）
  ownerName: string; // 归属人
  productName: string; // 产品名称（如「信用贷」「抵押贷」）
  loanAmount: number; // 实际放款金额
  term: number; // 期数
  statusStr: string; // 订单状态（转义）
  businessOrderNodeStr: string; // 订单节点（转义）
  loanNodeSubCodeStr: string; // 订单子节点（转义）
  nodeStatusStr: string; // 节点状态（转义）
  faceSignStatusStr: string; // 面签节点状态（转义）
  gpsLoadStatusStr: string; // gps安装状态（转义）
  hypoStatusStr: string; // 抵押节点状态（转义）
  createTime: string; // 创建时间（数据库默认当前时间）
  updateTime: string; // 最后跟进时间（数据库默认当前时间，更新时自动刷新）
  loanOverAt: string; // 订单结束时间
}

export const columns: BasicColumn<ListData>[] = [
  {
    title: '进件ID',
    key: 'id',
    align: 'center',
  },
  {
    title: '线索ID',
    key: 'clueId',
    align: 'center',
  },
  {
    title: '贷款订单ID',
    key: 'innerOrderNo',
    align: 'center',
  },
  {
    title: '渠道贷款订单ID',
    key: 'loanOrderId',
    align: 'center',
  },
  {
    title: '线索客户姓名',
    key: 'realName',
    align: 'center',
  },
  {
    title: '订单客户姓名',
    key: 'orderCustomerName',
    align: 'center',
  },
  {
    title: '线索手机号',
    key: 'mobileNo',
    align: 'center',
  },
  {
    title: '线索手机号MD5',
    key: 'mobileNoMd5',
    align: 'center',
  },
  {
    title: '订单手机号',
    key: 'orderMobile',
    align: 'center',
  },
  {
    title: '订单手机号MD5',
    key: 'orderMobileMd5',
    align: 'center',
  },
  {
    title: '归属人',
    key: 'ownerName',
    align: 'center',
  },
  {
    title: '进件资方',
    key: 'managementCodeStr',
    align: 'center',
  },
  {
    title: '产品信息',
    key: 'productName',
    align: 'center',
  },
  {
    title: '批复贷款金额',
    key: 'loanAmount',
    align: 'center',
  },
  {
    title: '实际贷款期限',
    key: 'term',
    align: 'center',
    render: (record: ListData) => {
      return <span>{record.term ? `${record.term}个月` : '-'}</span>;
    },
  },
  {
    title: '订单状态',
    key: 'statusStr',
    width: 100,
    align: 'center',
  },
  {
    title: '订单节点',
    key: 'businessOrderNodeStr',
    width: 100,
    align: 'center',
  },
  {
    title: '订单子节点',
    key: 'loanNodeSubCodeStr',
    width: 100,
    align: 'center',
  },
  {
    title: '节点状态',
    key: 'nodeStatusStr',
    width: 100,
    align: 'center',
  },
  {
    title: '面签节点状态',
    key: 'faceSignStatusStr',
    width: 180,
    align: 'center',
  },
  {
    title: 'GPS安装节点状态',
    key: 'gpsLoadStatusStr',
    width: 180,
    align: 'center',
  },
  {
    title: '抵押节点状态',
    key: 'hypoStatusStr',
    width: 180,
    align: 'center',
  },
  {
    title: '创建订单时间',
    key: 'createTime',
    width: 180,
    align: 'center',
    render: (record: ListData) => {
      return (
        <span>
          {record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
  {
    title: '最近跟进时间',
    key: 'updateTime',
    width: 180,
    align: 'center',
    render: (record: ListData) => {
      return (
        <span>
          {record.updateTime ? dayjs(record.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
  {
    title: '订单结束时间',
    key: 'loanOverAt',
    width: 180,
    align: 'center',
    render: (record: ListData) => {
      return (
        <span>
          {record.loanOverAt ? dayjs(record.loanOverAt).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
];
