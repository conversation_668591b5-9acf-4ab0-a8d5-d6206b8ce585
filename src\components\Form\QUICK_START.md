# 动态表单组件 - 快速开始

## 5分钟快速上手

### 1. 基础用法

```vue
<template>
  <DynamicForm
    :config="formConfig"
    v-model="formData"
    @submit="handleSubmit"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { DynamicForm, FieldType, validators } from '@/components/Form';

const formData = ref({});

const formConfig = {
  fields: [
    {
      field: 'name',
      label: '姓名',
      type: FieldType.INPUT,
      required: true,
      rules: [validators.required('请输入姓名')]
    },
    {
      field: 'email',
      label: '邮箱',
      type: FieldType.INPUT,
      rules: [validators.email()]
    },
    {
      field: 'age',
      label: '年龄',
      type: FieldType.NUMBER
    }
  ]
};

const handleSubmit = (data) => {
  console.log('表单数据:', data);
};
</script>
```

### 2. 手动配置表单

```vue
<script setup lang="ts">
import { validators, FiledOptions } from '@/components/Form';

const formConfig = {
  labelWidth: 120,
  fields: [
    {
      field: 'name',
      label: '姓名',
      type: FiledOptions.INPUT,
      required: true,
      rules: [validators.required('请输入姓名')]
    },
    {
      field: 'email',
      label: '邮箱',
      type: FiledOptions.INPUT,
      rules: [validators.email()]
    },
    {
      field: 'age',
      label: '年龄',
      type: FiledOptions.NUMBER
    }
  ]
};
</script>
```



### 4. 使用 Hook

```vue
<script setup lang="ts">
import { useDynamicForm, FieldType } from '@/components/Form';

const {
  config,
  formData,
  setFieldValue,
  validate
} = useDynamicForm();

// 设置值
setFieldValue('username', 'admin');

// 校验
const isValid = await validate();
</script>
```

## 常用字段类型

| 类型 | 用法 |
|------|------|
| 输入框 | `type: FieldType.INPUT` |
| 文本域 | `type: FieldType.TEXTAREA` |
| 数字输入 | `type: FieldType.NUMBER` |
| 选择器 | `type: FieldType.SELECT` |
| 单选框 | `type: FieldType.RADIO` |
| 复选框 | `type: FieldType.CHECKBOX` |
| 开关 | `type: FieldType.SWITCH` |
| 日期选择 | `type: FieldType.DATE` |
| 文件上传 | `type: FieldType.UPLOAD` |

## 常用校验规则

```javascript
// 必填
validators.required('此字段为必填项')

// 长度限制
validators.length(2, 20, '长度应在2-20个字符之间')

// 邮箱格式
validators.email('请输入正确的邮箱格式')

// 手机号格式
validators.phone('请输入正确的手机号')

// 数字范围
validators.numberRange(1, 100, '请输入1-100之间的数字')

// 自定义校验
validators.custom((value, formData) => {
  if (value !== formData.password) {
    return '两次密码输入不一致';
  }
  return null;
})
```

## 表单配置示例

```javascript
import { validators, FiledOptions } from '@/components/Form';

// 用户注册表单配置
const registerFormConfig = {
  labelWidth: 100,
  columns: 24,
  fields: [
    {
      field: 'username',
      label: '用户名',
      type: FiledOptions.INPUT,
      required: true,
      rules: [validators.required('请输入用户名'), validators.length(3, 20)]
    },
    {
      field: 'email',
      label: '邮箱',
      type: FiledOptions.INPUT,
      required: true,
      rules: [validators.required('请输入邮箱'), validators.email()]
    }
  ]
};
```

## 完整示例

```vue
<template>
  <div>
    <DynamicForm
      ref="formRef"
      :config="formConfig"
      v-model="formData"
      @submit="handleSubmit"
      @field-change="handleFieldChange"
    />
    
    <n-space class="mt-4">
      <n-button @click="validateForm">校验表单</n-button>
      <n-button @click="resetForm">重置表单</n-button>
      <n-button @click="fillSampleData">填充示例数据</n-button>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { NSpace, NButton, useMessage } from 'naive-ui';
import {
  DynamicForm,
  FiledOptions,
  validators
} from '@/components/Form';

const message = useMessage();
const formRef = ref();
const formData = ref({});

const formConfig = {
  labelWidth: 120,
  columns: 12,
  fields: [
    // 基础信息
    {
      field: 'name',
      label: '姓名',
      type: FiledOptions.INPUT,
      required: true,
      rules: [validators.required('请输入姓名'), validators.length(2, 20)]
    },
    {
      field: 'email',
      label: '邮箱',
      type: FiledOptions.INPUT,
      required: true,
      rules: [validators.required('请输入邮箱'), validators.email()]
    },
    {
      field: 'phone',
      label: '手机号',
      type: FiledOptions.INPUT,
      rules: [validators.phone()]
    },
    {
      field: 'age',
      label: '年龄',
      type: FiledOptions.NUMBER,
      rules: [validators.numberRange(1, 120)]
    },
    // 选择类字段
    {
      field: 'gender',
      label: '性别',
      type: FiledOptions.RADIO,
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ]
    },
    {
      field: 'city',
      label: '城市',
      type: FiledOptions.SELECT,
      options: [
        { label: '北京', value: 'beijing' },
        { label: '上海', value: 'shanghai' },
        { label: '广州', value: 'guangzhou' },
        { label: '深圳', value: 'shenzhen' }
      ]
    },
    {
      field: 'hobbies',
      label: '爱好',
      type: FiledOptions.CHECKBOX_GROUP,
      options: [
        { label: '读书', value: 'reading' },
        { label: '运动', value: 'sports' },
        { label: '音乐', value: 'music' },
        { label: '旅行', value: 'travel' }
      ]
    },
    // 日期时间
    {
      field: 'birthday',
      label: '生日',
      type: FiledOptions.DATE
    },
    // 开关和联动
    {
      field: 'hasAddress',
      label: '填写地址',
      type: FiledOptions.SWITCH
    },
    {
      field: 'address',
      label: '详细地址',
      type: FiledOptions.TEXTAREA,
      span: 24,
      hidden: true,
      rules: [
        validators.conditional(
          (formData) => formData.hasAddress,
          validators.required('请填写详细地址')
        )
      ]
    },
    // 文本域
    {
      field: 'bio',
      label: '个人简介',
      type: FiledOptions.TEXTAREA,
      rules: [validators.length(0, 500)]
    }
  ]
};

const handleSubmit = (data) => {
  message.success('表单提交成功！');
  console.log('表单数据:', data);
};

const handleFieldChange = (field, value, formData) => {
  console.log('字段变化:', { field, value, formData });
};

const validateForm = async () => {
  const isValid = await formRef.value?.validate();
  message.info(isValid ? '表单校验通过' : '表单校验失败');
};

const resetForm = () => {
  formRef.value?.resetForm();
  message.info('表单已重置');
};

const fillSampleData = () => {
  const sampleData = {
    name: '张三',
    email: '<EMAIL>',
    phone: '13800138000',
    age: 25,
    gender: 'male',
    city: 'beijing',
    hobbies: ['reading', 'sports'],
    birthday: new Date('1998-01-01').getTime(),
    hasAddress: true,
    address: '北京市朝阳区某某街道',
    bio: '这是一个示例用户的个人简介。'
  };
  formRef.value?.setFormData(sampleData);
  message.success('已填充示例数据');
};
</script>
```

## 下一步

- 查看 [完整文档](./README.md) 了解更多功能
- 查看 [示例页面](./examples/BasicExample.vue) 了解具体用法
- 查看 [API 参考](./README.md#api-参考) 了解所有可用选项

## 常见问题

**Q: 如何自定义组件？**
A: 使用 `ComponentMapManager.registerComponent()` 注册自定义组件。

**Q: 如何实现复杂的联动逻辑？**
A: 可以使用多个联动规则组合，或者在 `onFieldChange` 回调中实现自定义逻辑。

**Q: 如何处理异步数据？**
A: 可以在组件挂载后动态设置字段选项，或使用 `optionsApi` 配置异步获取选项。
