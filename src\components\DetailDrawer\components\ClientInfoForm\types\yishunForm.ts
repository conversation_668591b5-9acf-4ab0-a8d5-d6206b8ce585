export interface IUpdateCustomerModalFormModel {
  clueId: number | null;
  vehicleColor: string;
  interiorColor: string;
  licenseCity: string;
  manufactureDate: string | null;
  psvaTransferTimes: string;
  preAuditVehicleStatus: number | null;
  // carOfPersonStatus: number | null;
  // carThreeHundred: string | null;
  psvaPlateNumber: string;
  ipEduLevel: number | null;
  ipMaritalStatus: number | null;
  childrenCount: number | null;
  ipResDetail: string;
  propertyType: number | null;
  ipEmployerName: string;
  ipWorkDetail: string;
  companyPhone: string | null;
  ipEmployerType: number | null;
  ipNetMonthlyIncome: number | null;
  contactRelation: { relationshipName: string; name: string; mobile: string }[];
  // contactName1: string;
  // contactPhone1: string | null;
  // contactRelation1: number | null;
  // contactName2: string;
  // contactPhone2: string | null;
  // contactRelation2: number | null;
  // contactName3: string;
  // contactPhone3: string | null;
  // contactRelation3: number | null;
  preAuditIdCardNum: string | null;
  preAuditName: string | null;
  psvaCityCode: number | string | null;
  cityName: string | null;
  provinceName: string | null;
  provinceCode: number | string | null;
  ipSpouseName: string | null; //配偶姓名
  ipSpousePhone: string | null; //配偶手机号
  ipSpouseId: string | null; //配偶身份证号
  spouseCompanyName: string | null; //配偶工作单位
  spouseWorkAddress: string | null; //配偶工作单位地址
  spouseWorkUnitDetailAddress: string | null; //配偶工作单位详情地址
  ipIndustry: string | null; //从事行业种类
  profession: string | null; //职业
  preAuditIdCardReverse: string;
  preAuditIdCardFront: string;
  ipAttachVehicleLicense: string;
  ysVehicleLicenseBack: string;
  ipAttachDrivingLicense: string;
  driverLicenseBack: string;
  ysBankCardFront: string;
  ysBankCardBack: string;
  funderCardFrontLeft45: string | null;
  funderCardRearRight45: string | null;
  //人车合影
  funderCardVehiclePhoto: string | null;
  //车头全景
  ysCarFrontPanorama: string | null;
  psvaCarRegisCert: string | null;
  traffic12123Medias: string | null;
  compulsoryInsurance: string | null; //交强险
  gimaCommercialPolicy: string | null; //商业险
  // vehiclePhotos: string[];
  // contactListMedias: string[];
  // traffic12123Medias: string[];
  // bankStatements: string[];
  // bankAccountInfos: string[];
  // insurances: string[];
}

export const ipEduLevelOptions = [
  { label: '高中', value: 1 },
  { label: '大专', value: 2 },
  { label: '本科', value: 3 },
  { label: '研究生', value: 4 },
  { label: '博士', value: 5 },
];

export const ipMaritalStatusOptions = [
  { label: '未婚', value: 1 },
  { label: '已婚', value: 2 },
];

export const propertyTypeOptions = [
  { label: '商品房', value: 1 },
  { label: '自建房', value: 2 },
  { label: '别墅', value: 3 },
  { label: '写字楼', value: 4 },
  { label: '公寓', value: 5 },
];

export const ipEmployerTypeOptions = [
  { label: '企事业单位', value: 1 },
  { label: '民营企业', value: 2 },
  { label: '工商个体户', value: 3 },
  { label: '外资公司', value: 4 },
  { label: '其他', value: 5 },
];

export const ipNetMonthlyIncomeOptions = [
  { label: '5000以下', value: 1 },
  { label: '5000-10000', value: 2 },
  { label: '10000-15000', value: 3 },
  { label: '15000-20000', value: 4 },
  { label: '20000以上', value: 5 },
];
//直系亲属联系人 丈夫；妻子；儿子；女儿；父亲；母亲
export const contactRelationOptions = [
  { label: '丈夫', value: 1 },
  { label: '妻子', value: 2 },
  { label: '儿子', value: 3 },
  { label: '女儿', value: 4 },
  { label: '父亲', value: 5 },
  { label: '母亲', value: 6 },
];
//联系人 丈夫；妻子；前夫；前妻；未婚夫；未婚妻；男朋友；女朋友；父亲；母亲；哥哥；嫂子；弟弟；弟媳；姐姐；姐夫；妹妹；妹夫；表哥；表弟；表姐；表妹；同事；朋友；堂哥；堂弟；堂姐；堂妹；舅舅；舅妈；姑父；姑妈；姑姑；叔叔；伯父；伯母；儿子；女儿；侄儿；侄女；外甥；外甥女；公公；婆婆；岳父；岳母；亲家；儿媳；女婿；继父；继母；祖父；祖母；外祖父；外祖母；姨妈；姨父；婶婶；阿姨；师傅；师母；舅爷爷；舅奶奶；法人
export const contactRelationOptions1 = [
  { label: '丈夫', value: 1 },
  { label: '妻子', value: 2 },
  { label: '前夫', value: 3 },
  { label: '前妻', value: 4 },
  { label: '未婚夫', value: 5 },
  { label: '未婚妻', value: 6 },
  { label: '男朋友', value: 7 },
  { label: '女朋友', value: 8 },
  { label: '父亲', value: 9 },
  { label: '母亲', value: 10 },
  { label: '哥哥', value: 11 },
  { label: '嫂子', value: 12 },
  { label: '弟弟', value: 13 },
  { label: '弟媳', value: 14 },
  { label: '姐姐', value: 15 },
  { label: '姐夫', value: 16 },
  { label: '妹妹', value: 17 },
  { label: '妹夫', value: 18 },
  { label: '表哥', value: 19 },
  { label: '表弟', value: 20 },
  { label: '表姐', value: 21 },
  { label: '表妹', value: 22 },
  { label: '同事', value: 23 },
  { label: '朋友', value: 24 },
  { label: '堂哥', value: 25 },
  { label: '堂弟', value: 26 },
  { label: '堂姐', value: 27 },
  { label: '堂妹', value: 28 },
  { label: '舅舅', value: 29 },
  { label: '舅妈', value: 30 },
  { label: '姑父', value: 31 },
  { label: '姑妈', value: 32 },
  { label: '姑姑', value: 33 },
  { label: '叔叔', value: 34 },
  { label: '伯父', value: 35 },
  { label: '伯母', value: 36 },
  { label: '儿子', value: 37 },
  { label: '女儿', value: 38 },
  { label: '侄儿', value: 39 },
  { label: '侄女', value: 40 },
  { label: '外甥', value: 41 },
  { label: '外甥女', value: 42 },
  { label: '公公', value: 43 },
  { label: '婆婆', value: 44 },
  { label: '岳父', value: 45 },
  { label: '岳母', value: 46 },
  { label: '亲家', value: 47 },
  { label: '儿媳', value: 48 },
  { label: '女婿', value: 49 },
  { label: '继父', value: 50 },
  { label: '继母', value: 51 },
  { label: '祖父', value: 52 },
  { label: '祖母', value: 53 },
  { label: '外祖父', value: 54 },
  { label: '外祖母', value: 55 },
  { label: '姨妈', value: 56 },
  { label: '姨父', value: 57 },
  { label: '婶婶', value: 58 },
  { label: '阿姨', value: 59 },
  { label: '师傅', value: 60 },
  { label: '师母', value: 61 },
  { label: '舅爷爷', value: 62 },
  { label: '舅奶奶', value: 63 },
  { label: '法人', value: 64 },
];
export const psvaTransferTimesOptions = [
  { label: '0', value: '0' },
  { label: '1', value: '1' },
  { label: '2', value: '2' },
  { label: '3', value: '3' },
  { label: '4', value: '4' },
  { label: '5', value: '5' },
  { label: '6', value: '6' },
  { label: '7', value: '7' },
  { label: '8', value: '8' },
  { label: '9', value: '9' },
  { label: '10', value: '10' },
];

export const preAuditVehicleStatusOptions = [
  { label: '全款', value: 1 },
  { label: '按揭已结清', value: 2 },
  { label: '按揭未结清', value: 3 },
];

export const vehicleAuthStatusOptions = [
  { label: '一致', value: 1 },
  { label: '不一致', value: 0 },
];

export const threeElementAuthStatusOptions = [
  { label: '一致', value: 1 },
  { label: '不一致', value: 0 },
];
export const childrenCountOptions = [
  { label: '0', value: '0' },
  { label: '1', value: '1' },
  { label: '2', value: '2' },
  { label: '3', value: '3' },
  { label: '4', value: '4' },
  { label: '5', value: '5' },
];
//从事行业种类 医院；教育，金融/保险；科学研究,技术服务业和地址勘察业；信息传输、计算机服务、软件和技术服务；国际性组织；工商服务业；水利、环境与公共设施管理行业；电力、燃气及水的生产及工业业；制造业；建筑业；交通运输、仓储和邮政业；批发和零售业；房地产业；租赁及商业；文化，体育和娱乐业；采掘业；住宿和餐饮业；农业、林业、牧业、渔业；居民服务以及其他服务行业；临时工；退休；家庭妇女；学生；待业；进出口贸易业；旅游业；机关事业单位及部队；律师、会计师事务所等咨询；其他
export const ipIndustryOptions = [
  { label: '医院', value: 1 },
  { label: '教育', value: 2 },
  { label: '金融/保险', value: 3 },
  { label: '科学研究,技术服务业和地址勘察业', value: 4 },
  { label: '信息传输、计算机服务、软件和技术服务', value: 5 },
  { label: '国际性组织', value: 6 },
  { label: '工商服务业', value: 7 },
  { label: '水利、环境与公共设施管理行业', value: 8 },
  { label: '电力、燃气及水的生产及供应业', value: 9 },
  { label: '制造业', value: 10 },
  { label: '建筑业', value: 11 },
  { label: '交通运输、仓储和邮政业', value: 12 },
  { label: '批发和零售业', value: 13 },
  { label: '房地产业', value: 14 },
  { label: '租赁及商务服务业', value: 15 },
  { label: '文化，体育和娱乐业', value: 16 },
  { label: '采掘业', value: 17 },
  { label: '住宿和餐饮业', value: 18 },
  { label: '农业、林业、牧业、渔业', value: 19 },
  { label: '居民服务以及其他服务行业', value: 20 },
  { label: '临时工', value: 21 },
  { label: '退休', value: 22 },
  { label: '家庭妇女', value: 23 },
  { label: '学生', value: 24 },
  { label: '待业', value: 25 },
  { label: '进出口贸易业', value: 26 },
  { label: '旅游业', value: 27 },
  { label: '机关事业单位及部队', value: 28 },
  { label: '律师、会计师事务所等咨询', value: 29 },
  { label: '其他', value: 30 },
];
//职业：学生；外交人员；私营业主；不便分类的其他从业人员；退休人员；家庭主妇；无职业活动人员；中国共产党中央委员会和地方各级组织负责人；国际机关及其工作机构负责人；民主党派、社会团体及工作机构负责人；事业单位负责人；企业负责人；科学研究人员；工程技术（计算机）人员；农业技术人员；飞行和船舶技术人员；卫生专业技术人员；经济业务人员；金融业务人员；法律专业人员；教学人员；文学艺术工作人员；体育工作人员；新闻出版、文化工作人员；会计师；行政办公人员；安全保卫和消防工作人员；邮政和电信业务人员；事业单位员工；其他办事人员和有关人员；商业、服务业人员；农、林、牧、渔水利业生产人员；生产、运输设备操作人员及有关人员；军人
export const professionOptions = [
  { label: '学生', value: 1 },
  { label: '外交人员', value: 2 },
  { label: '私营业主', value: 3 },
  { label: '不便分类的其他从业人员', value: 4 },
  { label: '退休人员', value: 5 },
  { label: '家庭主妇', value: 6 },
  { label: '无职业活动人员', value: 7 },
  { label: '中国共产党中央委员会和地方各级组织负责人', value: 8 },
  { label: '国际机关及其工作机构负责人', value: 9 },
  { label: '民主党派、社会团体及工作机构负责人', value: 10 },
  { label: '事业单位负责人', value: 11 },
  { label: '企业负责人', value: 12 },
  { label: '科学研究人员', value: 13 },
  { label: '工程技术（计算机）人员', value: 14 },
  { label: '农业技术人员', value: 15 },
  { label: '飞行和船舶技术人员', value: 16 },
  { label: '卫生专业技术人员', value: 17 },
  { label: '经济业务人员', value: 18 },
  { label: '金融业务人员', value: 19 },
  { label: '法律专业人员', value: 20 },
  { label: '教学人员', value: 21 },
  { label: '文学艺术工作人员', value: 22 },
  { label: '体育工作人员', value: 23 },
  { label: '新闻出版、文化工作人员', value: 24 },
  { label: '会计师', value: 25 },
  { label: '行政办公人员', value: 26 },
  { label: '安全保卫和消防工作人员', value: 27 },
  { label: '邮政和电信业务人员', value: 28 },
  { label: '事业单位员工', value: 29 },
  { label: '其他办事人员和有关人员', value: 30 },
  { label: '商业、服务业人员', value: 31 },
  { label: '农、林、牧、渔水利业生产人员', value: 32 },
  { label: '生产、运输设备操作人员及有关人员', value: 33 },
  { label: '军人', value: 34 },
  { label: '其他', value: 35 },
];
