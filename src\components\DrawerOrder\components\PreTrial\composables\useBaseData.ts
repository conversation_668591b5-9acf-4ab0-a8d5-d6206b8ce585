import { reactive } from 'vue';
import { getFieldData } from '@/api/dashboard/deyi';
import { debounce } from 'lodash-es';
import { Fields } from '@/components/DrawerOrder/types';

const data = reactive({
  serverTime: '',
} as Partial<Fields>);

const getData = debounce(async (innerOrderNo: string) => {
  try {
    // 获取表单数据
    const { data: fieldData } = await getFieldData({
      innerOrderNo,
      fieldName: Object.keys(data),
    });

    if (Array.isArray(fieldData)) {
      fieldData.forEach((item: any) => {
        if (item.fieldName in data) {
          data[item.fieldName] = item.fieldValue;
        }
      });
    }
  } catch (e) {
    console.error('获取数据失败:', e);
  }
}, 500);

export function useBaseData(opts: { keys: string[]; innerOrderNo: string }) {
  opts.keys.forEach((key) => {
    data[key] = null;
  });

  return {
    data,
    getData: getData.bind(null, opts.innerOrderNo),
  };
}
