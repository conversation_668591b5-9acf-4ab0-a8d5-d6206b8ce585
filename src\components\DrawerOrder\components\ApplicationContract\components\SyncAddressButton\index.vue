<template>
  <n-button class="ml-2" type="primary" size="small" @click="onSync">同步身份证地址</n-button>
</template>

<script setup lang="ts">
  import { onMounted } from 'vue';
  import { useBaseData } from '../../composables/useBaseData';

  interface Props {
    innerOrderNo: string;
  }
  const props = defineProps<Props>();
  const emit = defineEmits<{
    (
      e: 'address',
      data: { province: string; city: string; district: string; detail: string }
    ): void;
  }>();

  const { data: initialData, getData } = useBaseData({
    innerOrderNo: props.innerOrderNo,
    keys: ['ipHukouProvince', 'ipHukouCity', 'ipHukouDistrict', 'ipHukouDetail'],
  });

  const onSync = () => {
    const { ipHukouProvince, ipHukouCity, ipHukouDistrict, ipHukouDetail } = initialData;
    const params = {
      province: ipHukouProvince || '',
      city: ipHukouCity || '',
      district: ipHukouDistrict || '',
      detail: ipHukouDetail || '',
    };
    console.log('同步地址信息', params);
    if (!params.province && !params.city && !params.district && !params.detail) {
      return;
    }

    emit('address', params);
  };

  onMounted(() => {
    getData();
  });
</script>
