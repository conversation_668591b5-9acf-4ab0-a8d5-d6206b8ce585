# 动态表单组件

一个基于 Vue 3 + Naive UI 的强大动态表单组件，支持丰富的字段类型、表单校验、字段联动等功能。

## 特性

- 🚀 **丰富的字段类型**: 支持 30+ 种表单控件类型
- 🔗 **强大的联动功能**: 支持字段间的显示隐藏、启用禁用、选项联动、值联动等
- ✅ **增强的校验功能**: 支持同步/异步校验、条件校验、自定义校验等
- 🛠 **配置化表单**: 通过配置对象快速构建复杂表单
- 📱 **响应式布局**: 支持栅格布局，自适应不同屏幕尺寸
- 🎨 **高度可定制**: 支持自定义组件、样式、校验规则等
- 💾 **表单缓存**: 支持表单数据缓存和恢复
- 🔧 **TypeScript**: 完整的 TypeScript 类型定义

## 安装

```bash
# 项目已内置，无需额外安装
```

## 基础用法

### 1. 简单表单

```vue
<template>
  <DynamicForm
    :config="formConfig"
    v-model="formData"
    @submit="handleSubmit"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { DynamicForm, FieldType, validators } from '@/components/Form';

const formData = ref({});

const formConfig = {
  fields: [
    {
      field: 'name',
      label: '姓名',
      type: FieldType.INPUT,
      required: true,
      rules: [validators.required('请输入姓名')]
    },
    {
      field: 'email',
      label: '邮箱',
      type: FieldType.INPUT,
      rules: [validators.email()]
    }
  ]
};

const handleSubmit = (data) => {
  console.log('表单数据:', data);
};
</script>
```

### 2. 手动配置表单

```vue
<script setup lang="ts">
import { validators, FiledOptions } from '@/components/Form';

const formConfig = {
  labelWidth: 120,
  columns: 12,
  fields: [
    {
      field: 'name',
      label: '姓名',
      type: FiledOptions.INPUT,
      required: true,
      rules: [validators.required('请输入姓名')]
    },
    {
      field: 'email',
      label: '邮箱',
      type: FiledOptions.INPUT,
      rules: [validators.email()]
    },
    {
      field: 'gender',
      label: '性别',
      type: FiledOptions.SELECT,
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ]
    }
  ]
};
</script>
```

### 3. 使用 Hook

```vue
<script setup lang="ts">
import { useDynamicForm, FieldType } from '@/components/Form';

const {
  config,
  formData,
  addField,
  setFieldValue,
  validate,
  submit
} = useDynamicForm();

// 添加字段
addField({
  field: 'username',
  label: '用户名',
  type: FieldType.INPUT,
  required: true
});

// 设置字段值
setFieldValue('username', 'admin');

// 校验表单
const isValid = await validate();

// 提交表单
await submit();
</script>
```

## 字段类型

支持以下字段类型：

| 类型 | 说明 | 组件 |
|------|------|------|
| `input` | 输入框 | NInput |
| `textarea` | 文本域 | NInput |
| `password` | 密码框 | NInput |
| `number` | 数字输入框 | NInputNumber |
| `select` | 选择器 | NSelect |
| `radio` | 单选框 | NRadioGroup |
| `checkbox` | 复选框 | NCheckbox |
| `checkboxGroup` | 复选框组 | NCheckboxGroup |
| `switch` | 开关 | NSwitch |
| `date` | 日期选择器 | NDatePicker |
| `dateRange` | 日期范围选择器 | NDatePicker |
| `time` | 时间选择器 | NTimePicker |
| `timeRange` | 时间范围选择器 | NTimePicker |
| `datetime` | 日期时间选择器 | NDatePicker |
| `datetimeRange` | 日期时间范围选择器 | NDatePicker |
| `upload` | 文件上传 | NUpload |
| `rate` | 评分 | NRate |
| `slider` | 滑块 | NSlider |
| `colorPicker` | 颜色选择器 | NColorPicker |
| `cascader` | 级联选择器 | NCascader |
| `treeSelect` | 树形选择器 | NTreeSelect |
| `transfer` | 穿梭框 | NTransfer |
| `mention` | 提及 | NMention |
| `dynamicInput` | 动态输入 | NDynamicInput |
| `dynamicTags` | 动态标签 | NDynamicTags |
| `divider` | 分割线 | NDivider |
| `custom` | 自定义组件 | 自定义 |

## 表单校验

### 内置校验规则

```typescript
import { validators } from '@/components/Form';

// 必填校验
validators.required('此字段为必填项')

// 长度校验
validators.length(2, 20, '长度应在2-20个字符之间')

// 正则校验
validators.pattern(/^[a-zA-Z]+$/, '只能输入字母')

// 手机号校验
validators.phone()

// 邮箱校验
validators.email()

// 身份证校验
validators.idCard()

// URL校验
validators.url()

// 数字范围校验
validators.numberRange(1, 100)

// 确认密码校验
validators.confirmPassword('password')

// 远程校验
validators.remote(async (value) => {
  const response = await checkUsername(value);
  return response.available;
}, '用户名已存在')

// 文件大小校验
validators.fileSize(5, 'MB')

// 文件类型校验
validators.fileType(['image/jpeg', 'image/png'])

// 自定义校验
validators.custom((value, formData) => {
  if (value < formData.minValue) {
    return '值不能小于最小值';
  }
  return null;
})
```

### 条件校验

```typescript
// 只有当某个条件满足时才进行校验
{
  field: 'confirmPassword',
  label: '确认密码',
  type: FieldType.PASSWORD,
  rules: [
    validators.conditional(
      (formData) => !!formData.password, // 只有密码不为空时才校验确认密码
      validators.confirmPassword('password')
    )
  ]
}
```

### 异步校验

```typescript
{
  field: 'username',
  label: '用户名',
  type: FieldType.INPUT,
  rules: [
    validators.async(async (rule, value, callback) => {
      try {
        const response = await checkUsernameAvailable(value);
        if (!response.available) {
          callback(new Error('用户名已存在'));
        } else {
          callback();
        }
      } catch (error) {
        callback(new Error('校验失败'));
      }
    })
  ]
}
```



## 表单配置示例

手动创建常用表单配置：

```typescript
import { validators, FiledOptions } from '@/components/Form';

// 用户注册表单配置
const registerFormConfig = {
  labelWidth: 100,
  columns: 24,
  fields: [
    {
      field: 'username',
      label: '用户名',
      type: FiledOptions.INPUT,
      required: true,
      rules: [validators.required('请输入用户名'), validators.length(3, 20)]
    },
    {
      field: 'password',
      label: '密码',
      type: FiledOptions.PASSWORD,
      required: true,
      rules: [validators.required('请输入密码'), validators.length(6, 20)]
    },
    {
      field: 'email',
      label: '邮箱',
      type: FiledOptions.INPUT,
      required: true,
      rules: [validators.required('请输入邮箱'), validators.email()]
    }
  ]
};
```

## API 参考

### DynamicForm Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| config | DynamicFormConfig | - | 表单配置 |
| modelValue | Record<string, any> | {} | 表单数据 |

### DynamicForm Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: Record<string, any>) | 表单数据更新 |
| submit | (data: Record<string, any>) | 表单提交 |
| reset | () | 表单重置 |
| cancel | () | 表单取消 |
| field-change | (field: string, value: any, formData: Record<string, any>) | 字段值变化 |
| validate | (field: string, valid: boolean, message?: string) | 字段校验 |

### DynamicForm Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getFormData | () | Record<string, any> | 获取表单数据 |
| setFormData | (data: Record<string, any>) | void | 设置表单数据 |
| getFieldValue | (field: string) | any | 获取字段值 |
| setFieldValue | (field: string, value: any) | void | 设置字段值 |
| validate | (fields?: string[]) | Promise<boolean> | 校验表单 |
| validateField | (field: string) | Promise<boolean> | 校验字段 |
| clearValidate | (fields?: string[]) | void | 清除校验 |
| resetForm | () | void | 重置表单 |
| clearForm | () | void | 清空表单 |


## 最佳实践

### 1. 表单性能优化

- 对于大型表单，使用 `v-show` 而不是 `v-if` 来控制字段显示
- 合理使用字段联动，避免过度复杂的联动逻辑
- 对于异步校验，使用防抖来减少请求频率

### 2. 表单可维护性

- 使用表单构建器来创建复杂表单
- 将表单配置抽取为独立的文件
- 使用 TypeScript 来确保类型安全

### 3. 用户体验

- 提供清晰的错误提示信息
- 使用合适的字段类型和校验规则
- 为复杂表单提供保存草稿功能

## 常见问题

### Q: 如何自定义组件？

A: 使用 `ComponentMapManager` 注册自定义组件：

```typescript
import { ComponentMapManager } from '@/components/Form';
import MyCustomComponent from './MyCustomComponent.vue';

ComponentMapManager.registerComponent('MyCustom', MyCustomComponent);
```

### Q: 如何实现复杂的联动逻辑？

A: 可以使用多个联动规则组合，或者在 `onFieldChange` 回调中实现自定义逻辑。

### Q: 如何处理表单数据的序列化？

A: 表单数据是普通的 JavaScript 对象，可以直接使用 `JSON.stringify` 序列化。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础字段类型和校验
- 支持字段联动功能
- 提供表单构建器
