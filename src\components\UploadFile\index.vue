<template>
  <div class="flex gap-[8px] flex-wrap">
    <!--展示渲染-->
    <template v-if="customShowPreview">
      <div
        v-for="item in fileList"
        :key="item.id"
        class="upload-file-item rounded-[var(--n-border-radius)] overflow-hidden flex items-center justify-center border-dashed border-[1px] border-[#eee] relative"
        :style="{
          width: width,
          height: height,
        }"
      >
        <!--展示渲染-->
        <div class="w-full h-full bg-[#fafafc] cursor-pointer">
          <!--图片-->
          <n-image
            v-if="getFileTypeByUrl(item.url) === 'image'"
            class="w-full h-full"
            :width="width"
            :height="height"
            :src="item.url"
            object-fit="contain"
          />
          <!--视频-->
          <video
            v-else-if="getFileTypeByUrl(item.url) === 'video'"
            :src="item.url"
            class="w-full h-full"
          ></video>
          <!--其他类型-->
          <div v-else class="w-full h-full p-[5px] flex justify-center items-center">
            {{ getFileName(item.url) }}
          </div>
        </div>
        <!--操作渲染-->
        <div
          class="operate absolute bottom-[-5px] h-[30%] w-full bg-[#00000099] z-[99] duration-[0.3s] opacity-0 flex justify-center items-center px-[5px] text-[#FFF] gap-[8px]"
        >
          <!--图片不需要预览-->
          <n-icon
            v-if="getFileTypeByUrl(item.url) !== 'image'"
            size="16"
            class="cursor-pointer"
            @click="handlePreview(item.url)"
          >
            <EyeOutlined />
          </n-icon>
          <n-icon v-if="!disabled" size="16" class="cursor-pointer" @click="handleDelete(item.id)">
            <DeleteOutlined />
          </n-icon>
        </div>
      </div>
    </template>

    <!--上传-->
    <n-upload
      v-if="fileList.length < maxCount || !customShowPreview"
      v-bind="$attrs"
      :action="action"
      :accept="accept"
      :show-file-list="!customShowPreview"
      :style="{
        width: width,
        height: height,
      }"
      :multiple="multiple"
      :headers="{ Authorization: token }"
      :on-before-upload="beforeUpload"
      :on-finish="handleFinish"
      :on-error="handleError"
      :disabled="!canUpload"
      :max="maxCount"
    >
      <slot name="uploadButton" v-bind="{ canUpload, uploading }">
        <div
          class="rounded-[var(--n-border-radius)] overflow-hidden flex items-center justify-center bg-[var(--n-dragger-color)] border-dashed border-[1px] border-[#eee] transition-all duration-200"
          :class="{
            'cursor-pointer hover:border-blue-500': canUpload,
            'cursor-not-allowed opacity-50': !canUpload,
          }"
          :style="{
            width: width,
            height: height,
          }"
        >
          <n-button text style="font-size: 24px" :disabled="!canUpload">
            <n-icon>
              <Add v-if="!uploading" />
              <n-spin v-else size="small" />
            </n-icon>
          </n-button>
          <div v-if="uploading" class="absolute bottom-1 text-xs text-gray-500"> 上传中... </div>
        </div>
      </slot>
    </n-upload>
  </div>
</template>

<script lang="ts" setup>
  import { Add } from '@vicons/ionicons5';
  import { DeleteOutlined, EyeOutlined } from '@vicons/antd';
  import { getFileTypeByUrl, getFileName } from '@/utils/getFileTypeByUrl';
  import { openFullscreen, watchElementFullscreen } from '@/utils/fullScreen';
  import { useUser } from '@/store/modules/user';
  import { onMounted, computed, ref, watch } from 'vue';
  interface UploadFileList {
    url: string;
    id: string | number;
  }

  const props = defineProps({
    action: {
      type: String,
      default: `${import.meta.env.VITE_GLOB_API_URL}/cms/upload/file`,
    },
    width: {
      type: String,
      default: '96px',
    },
    height: {
      type: String,
      default: '96px',
    },
    // 最大上传数量
    maxCount: {
      type: Number,
      default: 1,
    },
    // 上传文件类型
    accept: {
      type: String,
      default: '',
    },
    // 文件上传大小 默认5mb
    maxSize: {
      type: Number,
      default: 5,
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false,
    },
    //是否展示自定义受控组件
    customShowPreview: {
      type: Boolean,
      default: true,
    },
    //是否是单文件上传
    single: {
      type: Boolean,
      default: false,
    },
    // 强制数据类型数组
    forceArray: {
      type: Boolean,
      default: false,
    },
    /**
     * 禁用删除按钮
     */
    disabled: {
      type: Boolean,
      default: false,
    },
  });
  const token = computed(() => useUser().getToken);

  // 内部文件列表，始终为数组格式
  const internalFileList = ref<UploadFileList[]>([]);

  // 上传状态管理
  const uploading = ref(false);
  const uploadingCount = ref(0);
  // 定义model，支持字符串和数组
  const modelValue = defineModel<UploadFileList[] | string | undefined | null>('fileList', {
    default: () => [],
  });
  defineExpose({
    handlePreview,
  });
  const emit = defineEmits(['update:fileList']);

  // 计算属性：用于模板显示的文件列表
  const fileList = computed(() => internalFileList.value);

  // 计算是否可以上传
  const canUpload = computed(() => {
    return !uploading.value && internalFileList.value.length < props.maxCount && !props.disabled;
  });

  // 初始化文件列表
  function initFileList() {
    if (modelValue.value) {
      if (typeof modelValue.value === 'string') {
        internalFileList.value = modelValue.value
          ? [{ url: modelValue.value, id: Date.now() }]
          : [];
      } else {
        internalFileList.value = modelValue.value || [];
      }
    }
  }

  // 更新绑定值
  function updateModelValue() {
    const newValue =
      !props.forceArray && (props.maxCount === 1 || props.single)
        ? internalFileList.value.length > 0
          ? internalFileList.value[0].url
          : ''
        : internalFileList.value;
    modelValue.value = newValue;
  }

  onMounted(() => {
    initFileList();
  });

  watch(
    () => modelValue.value,
    (newVal) => {
      if (typeof newVal === 'string') {
        internalFileList.value = newVal ? [{ url: newVal, id: Date.now() }] : [];
      } else {
        internalFileList.value = newVal || [];
      }
    }
  );
  // 上传之前
  function beforeUpload(files: any) {
    console.log('点击上传');

    let { file } = files;
    const isLtMax = file.file.size / 1024 / 1024 <= props.maxSize;
    if (!isLtMax) {
      window.$message?.error(`文件大小不能超过 ${props.maxSize}MB`);
      return false;
    }

    // 检查是否已达到最大数量
    if (internalFileList.value.length + uploadingCount.value >= props.maxCount) {
      window.$message?.warning(`最多只能上传 ${props.maxCount} 个文件`);
      return false;
    }

    // 开始上传
    uploading.value = true;
    uploadingCount.value++;

    return true;
  }
  // 删除
  function handleDelete(id: string | number) {
    internalFileList.value = internalFileList.value.filter((item) => item.id !== id);
    updateModelValue();
  }
  // 预览
  function handlePreview(url: string) {
    // 视频创建video标签预览
    if (getFileTypeByUrl(url) === 'video') {
      const video = document.createElement('video');
      video.src = url;
      video.controls = true;
      video.autoplay = true;
      video.style.objectFit = 'cover';
      document.body.appendChild(video);
      openFullscreen(video);
      watchElementFullscreen(video, () => {
        // 卸载video元素
        document.body.removeChild(video);
      });
    } else if (getFileTypeByUrl(url) === 'pdf' || getFileTypeByUrl(url) === 'audio') {
      // pdf、audio元素打开新标签预览
      window.open(url);
    } else {
      // 其他文件通过下载
      const a = document.createElement('a');
      a.href = url;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  }

  function handleFinish({ event }) {
    try {
      const res = eval('(' + event.target.response + ')');
      const { code } = res;
      const message = res.msg || res.message || '上传失败';

      if (code === 200) {
        internalFileList.value.push({
          url: res.data,
          id: Date.now(),
        });
        updateModelValue();
        window.$message?.success('上传成功');
      } else {
        window.$message?.error(message);
      }
    } catch (error) {
      console.error('上传响应解析失败:', error);
      window.$message?.error('上传失败，请重试');
    } finally {
      // 上传完成，重置状态
      uploadingCount.value--;
      if (uploadingCount.value <= 0) {
        uploading.value = false;
        uploadingCount.value = 0;
      }
    }
  }

  // 上传错误处理
  function handleError(error: any) {
    console.error('上传失败:', error);
    window.$message?.error('上传失败，请重试');

    // 上传失败，重置状态
    uploadingCount.value--;
    if (uploadingCount.value <= 0) {
      uploading.value = false;
      uploadingCount.value = 0;
    }
  }
</script>

<style lang="less" scoped>
  .upload-file-item {
    .operate {
      border-radius: 3px 3px 0 0;
    }
    &:hover {
      .operate {
        opacity: 1 !important;
        bottom: 0 !important;
      }
    }
  }
</style>
