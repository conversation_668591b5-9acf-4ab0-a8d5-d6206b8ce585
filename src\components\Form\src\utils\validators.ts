import type { FormItemRule } from 'naive-ui';
import type { EnhancedFormItemRule, AsyncValidator } from '../types/dynamicForm';
import { isObject } from '@/utils/is';

/**
 * 增强的表单校验工具类
 */
export class FormValidators {
  /**
   * 创建必填校验规则
   */
  static required(message?: string): EnhancedFormItemRule {
    return {
      required: true,
      message: message || '此字段为必填项',
      trigger: ['blur', 'change'],
    };
  }

  /**
   * 创建长度校验规则
   */
  static length(min?: number, max?: number, message?: string): EnhancedFormItemRule {
    const rule: EnhancedFormItemRule = {
      trigger: ['blur', 'change'],
    };

    if (min !== undefined && max !== undefined) {
      rule.min = min;
      rule.max = max;
      rule.message = message || `长度应在 ${min} 到 ${max} 个字符之间`;
    } else if (min !== undefined) {
      rule.min = min;
      rule.message = message || `长度不能少于 ${min} 个字符`;
    } else if (max !== undefined) {
      rule.max = max;
      rule.message = message || `长度不能超过 ${max} 个字符`;
    }

    return rule;
  }

  /**
   * 创建正则表达式校验规则
   */
  static pattern(pattern: RegExp, message: string): EnhancedFormItemRule {
    return {
      pattern,
      message,
      trigger: ['blur', 'change'],
    };
  }

  /**
   * 手机号校验
   */
  static phone(message?: string): EnhancedFormItemRule {
    const phonePattern =
      /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[189]))\d{8}$/;
    return this.pattern(phonePattern, message || '请输入正确的手机号码');
  }

  /**
   * 邮箱校验
   */
  static email(message?: string): EnhancedFormItemRule {
    const emailPattern = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
    return this.pattern(emailPattern, message || '请输入正确的邮箱地址');
  }

  /**
   * 身份证号校验
   */
  static idCard(message?: string): EnhancedFormItemRule {
    const idCardPattern =
      /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/;
    return this.pattern(idCardPattern, message || '请输入正确的身份证号码');
  }

  /**
   * URL校验
   */
  static url(message?: string): EnhancedFormItemRule {
    const urlPattern = /^https?:\/\/[\w.-]+(?:\.[\w.-]+)+[/#?]?.*$/;
    return this.pattern(urlPattern, message || '请输入正确的URL地址');
  }

  /**
   * 数字范围校验
   */
  static numberRange(min?: number, max?: number, message?: string): EnhancedFormItemRule {
    return {
      type: 'number',
      min,
      max,
      message: message || `请输入 ${min || '最小'} 到 ${max || '最大'} 之间的数字`,
      trigger: ['blur', 'change'],
    };
  }

  /**
   * 自定义异步校验
   */
  static async(validator: AsyncValidator, message?: string): EnhancedFormItemRule {
    return {
      validator,
      message,
      trigger: ['blur', 'change'],
    };
  }

  /**
   * 条件校验：只有满足条件时才进行校验
   */
  static conditional(
    condition: (formData: Record<string, any>) => boolean,
    rule: EnhancedFormItemRule
  ): EnhancedFormItemRule {
    return {
      ...rule,
      condition,
    };
  }

  /**
   * 依赖字段校验：当依赖字段变化时重新校验
   */
  static dependent(dependencies: string[], rule: EnhancedFormItemRule): EnhancedFormItemRule {
    return {
      ...rule,
      dependencies,
    };
  }

  /**
   * 确认密码校验
   */
  static confirmPassword(passwordField: string, message?: string): EnhancedFormItemRule {
    return {
      validator: (rule, value, callback, source) => {
        if (!value) {
          callback();
          return;
        }
        if (value !== source[passwordField]) {
          callback(new Error(message || '两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      dependencies: [passwordField],
      trigger: ['blur', 'change'],
    };
  }

  /**
   * 远程校验（如用户名唯一性）
   */
  static remote(api: (value: any) => Promise<boolean>, message?: string): EnhancedFormItemRule {
    return {
      validator: async (rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }
        try {
          const isValid = await api(value);
          if (!isValid) {
            callback(new Error(message || '校验失败'));
          }
        } catch (error) {
          callback(new Error('校验服务异常'));
        }
      },
      trigger: ['blur'],
    };
  }

  /**
   * 数组长度校验
   */
  static arrayLength(min?: number, max?: number, message?: string): EnhancedFormItemRule {
    return {
      required: true,
      type: 'array',
      min,
      max,
      message: message || `请选择 ${min || 0} 到 ${max || '无限'} 项`,
      trigger: ['change'],
    };
  }

  /**
   * 文件大小校验
   */
  static fileSize(maxSize: number, unit: 'B' | 'KB' | 'MB' | 'GB' = 'MB'): EnhancedFormItemRule {
    const units = { B: 1, KB: 1024, MB: 1024 * 1024, GB: 1024 * 1024 * 1024 };
    const maxBytes = maxSize * units[unit];

    return {
      validator: async (rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }
        const files = Array.isArray(value) ? value : [value];
        for (const file of files) {
          const fileUrl = isObject(file) ? file.url : file;
          const response = await fetch(fileUrl, { method: 'HEAD' });
          if (response.ok) {
            const contentLength = response.headers.get('Content-Length');
            if (contentLength && parseInt(contentLength) > maxBytes) {
              callback(new Error(`文件大小不能超过 ${maxSize}${unit}`));
              return;
            }
          }
        }
        callback();
      },
      trigger: ['change'],
    };
  }

  /**
   * 文件类型校验
   */
  static fileType(allowedTypes: string[], message?: string): EnhancedFormItemRule {
    return {
      validator: (rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }

        const files = Array.isArray(value) ? value : [value];
        for (const file of files) {
          const fileType = file.type || '';
          const isAllowed = allowedTypes.some((type) => {
            if (type.includes('/')) {
              return fileType === type;
            } else {
              return fileType.startsWith(type + '/');
            }
          });

          if (!isAllowed) {
            callback(new Error(message || `只允许上传 ${allowedTypes.join(', ')} 类型的文件`));
            return;
          }
        }
        callback();
      },
      trigger: ['change'],
    };
  }

  /**
   * 组合多个校验规则
   */
  static combine(...rules: EnhancedFormItemRule[]): EnhancedFormItemRule[] {
    return rules;
  }

  /**
   * 创建自定义校验规则
   */
  static custom(
    validator: (
      value: any,
      formData: Record<string, any>
    ) => string | null | Promise<string | null>,
    trigger: string[] = ['blur', 'change']
  ): EnhancedFormItemRule {
    return {
      validator: async (rule, value, callback, source) => {
        try {
          const result = await validator(value, source || {});
          if (result) {
            callback(new Error(result));
          } else {
            callback();
          }
        } catch (error) {
          callback(new Error('校验过程中发生错误'));
        }
      },
      trigger,
    };
  }
}

/**
 * 校验工具函数
 */
export const validators = FormValidators;

/**
 * 常用校验规则快捷方式
 */
export const rules = {
  required: FormValidators.required,
  phone: FormValidators.phone,
  email: FormValidators.email,
  idCard: FormValidators.idCard,
  url: FormValidators.url,
  length: FormValidators.length,
  numberRange: FormValidators.numberRange,
  confirmPassword: FormValidators.confirmPassword,
  remote: FormValidators.remote,
  fileSize: FormValidators.fileSize,
  fileType: FormValidators.fileType,
  custom: FormValidators.custom,
};
