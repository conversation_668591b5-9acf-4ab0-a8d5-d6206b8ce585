<template>
  <BaseModal
    v-model:show="showModal"
    title="获取车辆评估价"
    :width="500"
    positive-text="获取估值"
    :on-confirm="handleStatusSubmit"
  >
    <n-form
      ref="formModelRef"
      :model="formModel"
      :rules="statusRules"
      label-placement="left"
      :label-width="100"
      label-align="right"
    >
      <n-form-item label="行驶证" path="licensePhoto" required>
        <UploadFile
          :file-list="formModel.licensePhoto"
          accept=".jpg,.jpeg,.png,.bmp"
          @update:file-list="
            (val) => {
              formModel.licensePhoto = val;
              onOcr('licensePhoto', val, ['vehicleRegisInfo'], vehicleLicenseOcr);
            }
          "
        />
      </n-form-item>
      <n-space justify="space-between">
        <n-form-item label="车辆VIN码" path="vin" required>
          <n-input v-model:value="formModel.vin" />
        </n-form-item>
        <n-form-item label="首次上牌时间" path="regDate" required>
          <n-date-picker
            :formatted-value="
                            isDate(formModel.regDate as string)
                              ? formModel.regDate
                              : undefined
                          "
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="请选择首次上牌时间"
            clearable
            @update:formatted-value="formModel.regDate = $event"
          /> </n-form-item
      ></n-space>
      <n-form-item label="车牌号" path="carNo" required>
        <n-input v-model:value="formModel.carNo" />
      </n-form-item>
    </n-form>
  </BaseModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import type { FormInst, FormRules } from 'naive-ui';
  import { NForm, NFormItem } from 'naive-ui';
  import { BaseModal } from '@/components/Modal';
  import { getVehicleAppraisalPrice } from '@/api/detail';
  import { vehicleLicenseOcr } from '@/components/DrawerOrder/ocr';
  import UploadFile from '@/components/UploadFile/index.vue';

  import { watch } from 'vue';

  const showModal = defineModel<boolean>('show', { default: false });
  const props = defineProps({
    clueId: {
      type: [Number, String],
      required: true,
    },
    localDetail: {
      type: Object,
      default: () => ({}),
    },
  });
  const emit = defineEmits(['submit-success']);
  const defaultForm = {
    licensePhoto: '',
    vin: '',
    regDate: undefined,
    carNo: '',
    carType: '',
    engineNumber: '',
  };

  const formModelRef = ref<FormInst | null>(null);
  const formModel = ref<{
    licensePhoto: string;
    vin: string;
    regDate: string | undefined;
    carNo: string;
    carType: string;
    engineNumber: string;
  }>({
    ...defaultForm,
  });

  const statusRules: FormRules = {
    vin: {
      required: true,
      message: '请输入车辆VIN码',
      trigger: ['blur', 'change'],
    },
    regDate: {
      required: true,
      message: '请选择首次上牌时间',
      trigger: ['blur', 'change'],
    },
    carNo: {
      required: true,
      message: '请输入车牌号',
      trigger: ['blur', 'change'],
    },
  };

  watch(
    () => showModal.value,
    (show) => {
      if (!show) {
        resetForm();
      }
    }
  );

  const resetForm = () => {
    formModel.value = { ...defaultForm };
    formModelRef.value?.restoreValidation();
  };
  // 是否是合法日期 yyyy-MM-dd
  const isDate = (date: string) => {
    if (!date) return false;

    const reg = /^\d{4}-\d{2}-\d{2}$/;
    return reg.test(date);
  };
  const onOcr = async (ocrKey: string, url: string, addKeys: string[], api: any) => {
    if (!url) {
      return;
    }
    let data: any = {};
    const res = await api({ url });
    Object.assign(data, res || {});
    console.log(formModel.value, 'addKeys');
    let {
      vinCode: vin,
      licensePlateNumber: carNo,
      registrationDate: regDate,
      engineNumber,
      vehicleType: carType,
    } = data.vehicleRegisInfo;
    if (!isDate(regDate)) {
      window.$message?.error?.('首次上牌时间,日期格式不正确,请手动录入');
      regDate = '';
    }
    formModel.value = {
      ...formModel.value,
      vin,
      carNo,
      regDate,
      engineNumber,
      carType,
    };
  };
  async function handleStatusSubmit() {
    try {
      await formModelRef.value?.validate();
      await getVehicleAppraisalPrice({
        // clueId: props.clueId,
        mobile: props.localDetail.clueInfoVo?.mobileNo,
        ...formModel.value,
      });
      window.$message.success('获取车辆评估价成功');
      emit('submit-success');
      showModal.value = false;
    } catch (error) {
      window.$message.error('获取车辆评估价失败');
      return false;
    }
  }
</script>
