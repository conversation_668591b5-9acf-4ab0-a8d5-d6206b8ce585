import { App } from 'vue';

import { permission, callPermission } from '@/directives/permission';
import copy from '@/directives/copy';
import debounce from '@/directives/debounce';
import throttle from '@/directives/throttle';
import draggable from '@/directives/draggable';
import loading from '@/directives/loading';
import wheelScroll from '@/directives/wheelScroll';
import cooldown from '@/directives/cooldown';

/**
 * 注册全局自定义指令
 * @param app
 */
export function setupDirectives(app: App) {
  // 权限控制指令（演示）
  app.directive('permission', permission);
  app.directive('callPermission', callPermission);
  // 复制指令
  app.directive('copy', copy);
  // 防抖指令
  app.directive('debounce', debounce);
  // 节流指令
  app.directive('throttle', throttle);
  // 拖拽指令
  app.directive('draggable', draggable);
  // 加载指令
  app.directive('loading', loading);
  // 滚轮横向滚动指令
  app.directive('wheel-scroll', wheelScroll);
  // 点击冷却指令（默认5s）
  app.directive('cooldown', cooldown);
}
