<template>
  <div>
    <n-modal
      v-model:show="active"
      preset="card"
      v-bind="$attrs"
      :auto-focus="false"
      :mask-closable="false"
      :close-on-esc="false"
      class="w-[400px]"
    >
      <template #header>
        <p class="font-500">身份证信息</p>
      </template>
      <n-space vertical align="center">
        <n-form
          ref="formRef1"
          label-placement="left"
          size="medium"
          :model="formModel1"
          :rules="currentRules1"
          label-width="120px"
        >
          <!-- 身份证上传区域 -->
          <n-grid :cols="GRID_COLS">
            <n-grid-item>
              <n-form-item label="身份证国徽面" path="preAuditIdCardReverse" required>
                <UploadFile
                  :file-list="jsonToImage(formModel1.preAuditIdCardReverse || '')"
                  accept=".jpg,.jpeg,.png,.bmp"
                  :max-size="10"
                  :force-array="true"
                  @update:file-list="(val) => (formModel1.preAuditIdCardReverse = imageToJson(val))"
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="身份证人像面" path="preAuditIdCardFront" required>
                <UploadFile
                  :file-list="jsonToImage(formModel1.preAuditIdCardFront || '')"
                  accept=".jpg,.jpeg,.png,.bmp"
                  :max-size="10"
                  :force-array="true"
                  @update:file-list="(val) => (formModel1.preAuditIdCardFront = imageToJson(val))"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </n-form>
      </n-space>

      <template #action>
        <n-space justify="center">
          <n-button @click="active = false"> 取消 </n-button>
          <n-button type="primary" :loading="btnLoading" @click="onConfirm"> 确认 </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
  import UploadFile from '@/components/UploadFile/index.vue';

  import { GRID_COLS } from '@/components/DrawerOrder/config';
  import { Fields } from '@/components/DrawerOrder/types';
  import { onMounted, ref, watch, reactive } from 'vue';
  import { saveFieldData, reSubmitIdCard } from '@/api/dashboard/deyi';
  import { jsonToImage, imageToJson } from '@/components/DrawerOrder/utils';

  const props = defineProps<{
    innerOrderNo: string;
  }>();
  const emit = defineEmits<{
    (e: 'close', v: 'ok' | 'cancel'): void;
  }>();

  const active = ref(false);
  const btnType = ref<'ok' | 'cancel'>('cancel');
  const btnLoading = ref(false);

  const formRef1 = ref();
  const formModel1 = reactive<Partial<Fields>>({
    preAuditIdCardReverse: '', // 身份证国徽面
    preAuditIdCardFront: '', // 身份证人像面
  });
  const currentRules1 = {
    preAuditIdCardReverse: [
      {
        required: true,
        message: '请上传身份证国徽面',
        trigger: ['input', 'blur'],
      },
    ],
    preAuditIdCardFront: [
      {
        required: true,
        message: '请上传身份证人像面',
        trigger: ['input', 'blur'],
      },
    ],
  };

  watch(
    () => active.value,
    (val) => {
      if (!val) {
        setTimeout(() => {
          emit('close', btnType.value);
        }, 300);
      }
    }
  );

  onMounted(() => {
    active.value = true;
  });

  const onConfirm = () => {
    formRef1.value?.validate(async (errors) => {
      if (!errors) {
        try {
          btnLoading.value = true;
          await saveFieldData({
            innerOrderNo: props.innerOrderNo,
            fieldData: {
              ...formModel1,
            },
          });
          await reSubmitIdCard({
            innerOrderNo: props.innerOrderNo,
          });

          active.value = false;
          btnType.value = 'ok';
        } finally {
          btnLoading.value = false;
        }
      }
    });
  };
</script>

<style lang="less" scoped></style>
