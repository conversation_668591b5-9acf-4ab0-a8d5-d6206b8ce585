{
  // Disable the default formatter, use eslint instead
  "prettier.enable": true,
  "editor.formatOnSave": false,
  "eslint.enable": true,
  "eslint.format.enable": false,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "json5",
    "jsonc",
    "yaml",
    "toml",
    "xml",
    "gql",
    "graphql",
    "astro",
    "svelte",
    "css",
    "less",
    "scss",
    "pcss",
    "postcss"
  ],
  "cSpell.words": [
    "antd",
    "cooldown",
    "daterange",
    "esno",
    "funder",
    "oppo",
    "persistedstate",
    "psva",
    "unref",
    "vicons"
  ]
}
