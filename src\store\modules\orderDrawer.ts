import { defineStore } from 'pinia';
import { store } from '@/store';
import type { Stage, StageNodeData } from '@/components/DrawerOrder/types';
import { transformStageData } from '@/components/DrawerOrder/types';
import { getOrderNode, getFieldData, moveNode2PreAudit } from '@/api/dashboard/deyi';

export interface BasicInfo {
  innerOrderNo: string;
  preAuditName: string;
  preAuditPhaseCity: string;
  preAuditPhoneNum: string;
  psvaLoanAmout: string;
  psvaLoanTime: string;
  [key: string]: any; // 允许动态字段
}

export interface OrderDrawerState {
  currentStage: Stage | null;
  refreshSignal: number;
  stagesList: StageNodeData[];
  basicInfo: BasicInfo;
  isSubmitLoading: boolean;
  mortgageFields: {
    gimaApprovalQuery?: string;
    gimaMortgageMaterial?: string;
  };
}

export const useOrderDrawerStore = defineStore({
  id: 'app-order-drawer',
  state: (): OrderDrawerState => ({
    currentStage: null,
    refreshSignal: 0,
    stagesList: [],
    basicInfo: {
      innerOrderNo: '',
      preAuditName: '',
      preAuditPhaseCity: '',
      preAuditPhoneNum: '',
      psvaLoanAmout: '',
      psvaLoanTime: '',
    },
    isSubmitLoading: false,
    mortgageFields: {},
  }),
  getters: {
    getCurrentStage(): Stage | null {
      return this.currentStage;
    },
  },
  actions: {
    setCurrentStage(stage: Stage | null) {
      this.currentStage = stage;
    },
    // 触发刷新（组件监听后执行 initOrderData）
    triggerRefresh() {
      this.refreshSignal++;
    },
    /**
     * 获取基本信息数据（通用方法，传什么字段名就返回什么字段的数据）
     * @param innerOrderNo 订单号
     * @param fieldNames 字段名数组，如果不传则使用默认字段
     * @returns 返回包含指定字段的数据对象，调用端可自行赋值
     */
    async getBasicInfo(
      innerOrderNo: string,
      fieldNames?: string[]
    ): Promise<BasicInfo | undefined> {
      if (!innerOrderNo) {
        return;
      }

      try {
        // 如果没有传入字段名，使用默认字段
        const defaultFieldNames = [
          'preAuditName',
          'preAuditPhaseCity',
          'preAuditPhoneNum',
          'psvaLoanAmout',
          'psvaLoanTime',
        ];
        const finalFieldNames = fieldNames || defaultFieldNames;

        const res = await getFieldData({ innerOrderNo, fieldName: finalFieldNames });

        if (res.data && Array.isArray(res.data)) {
          // 将数组转换为对象
          const fieldData: Record<string, string> = {};
          res.data.forEach((item: any) => {
            if (item.fieldName && item.fieldValue !== undefined) {
              fieldData[item.fieldName] = item.fieldValue;
            }
          });

          // 通用构建返回对象，包含 innerOrderNo 和所有传入的字段
          const result: BasicInfo = {
            innerOrderNo,
            preAuditName: '',
            preAuditPhaseCity: '',
            preAuditPhoneNum: '',
            psvaLoanAmout: '',
            psvaLoanTime: '',
          };

          // 将字段数据添加到结果对象中
          finalFieldNames.forEach((fieldName) => {
            result[fieldName] = fieldData[fieldName] || '';
          });

          return result;
        }
      } catch (error) {
        console.error('获取基本信息失败:', error);
      }
    },
    /**
     * 初始化订单数据
     */
    async initOrderData(innerOrderNo: string) {
      if (!innerOrderNo) {
        return;
      }

      try {
        this.isSubmitLoading = true;
        // 只加载节点，不主动跳转预审阶段，不改 currentStage
        await moveNode2PreAudit({ innerOrderNo });
        const res = await getOrderNode({ innerOrderNo });
        this.stagesList =
          res.data && res.data.length
            ? res.data
            : [
                {
                  mainNodeCode: 1,
                  subNodeCode: 1,
                  subNodeStatus: 2,
                },
              ];

        // 获取抵押阶段相关字段数据，用于判断警邮回执节点是否展示
        const mortgageFieldRes = await getFieldData({
          innerOrderNo,
          fieldName: ['gimaApprovalQuery', 'gimaMortgageMaterial'],
        });
        if (mortgageFieldRes.data && Array.isArray(mortgageFieldRes.data)) {
          mortgageFieldRes.data.forEach((item: any) => {
            if (item.fieldName === 'gimaApprovalQuery') {
              this.mortgageFields.gimaApprovalQuery = item.fieldValue || undefined;
            } else if (item.fieldName === 'gimaMortgageMaterial') {
              this.mortgageFields.gimaMortgageMaterial = item.fieldValue || undefined;
            }
          });
        }

        const stages = transformStageData(this.stagesList, this.mortgageFields);
        // 当前阶段 id 若在新数据中还存在，则用新数据更新（确保 subStages 等能正确更新），否则选第一项
        if (stages.length > 0) {
          // 优先 ongoing 阶段
          const ongoingStage = stages.find((s) => s.status === 'ongoing');
          // 如果没有 ongoing，则找第一个 pending
          const pendingStage = stages.find((s) => s.status === 'pending');
          // 找出退回
          const returnedStage = stages.find((s) => s.status === 'returned');
          // 找出失败
          const failedStage = stages.find((s) => s.status === 'failed');
          // 如果所有阶段都完成了（没有 ongoing 和 pending），则使用最后一个阶段
          const lastStage = stages[stages.length - 1];
          const targetStage =
            returnedStage || failedStage || ongoingStage || pendingStage || lastStage;
          if (this.currentStage && typeof this.currentStage.id !== 'undefined') {
            // 查找相同 id 的 stage，如果找到则用新数据更新（确保 subStages 等能正确更新）
            const found = stages.find((s) => s.id === this.currentStage!.id);
            if (found) {
              // 用新数据更新 currentStage，确保 subStages 等能正确更新
              this.currentStage = found;
            } else {
              // 如果找不到相同 id，则使用目标阶段
              this.currentStage = targetStage;
            }
          } else {
            this.currentStage = targetStage;
          }
        } else {
          this.currentStage = null;
        }
        // 获取基本信息数据
        const basicInfoData = await this.getBasicInfo(innerOrderNo);
        if (basicInfoData) {
          this.basicInfo = basicInfoData;
        }
      } catch (error) {
        console.error('初始化订单数据失败:', error);
      } finally {
        this.isSubmitLoading = false;
      }
    },
  },
});

// 组件外使用
export function useOrderDrawer() {
  return useOrderDrawerStore(store);
}
