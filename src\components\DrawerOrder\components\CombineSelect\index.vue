<template>
  <n-space vertical>
    <n-space>
      <n-select
        v-bind="$attrs"
        v-model:value="model"
        :options="options"
        :render-label="renderOption"
        :show-checkmark="false"
        ref="selectRef"
        class="w-[280px]"
        placeholder="请选择"
        :disabled="false"
      />
      <template v-for="item in options" :key="item.value">
        <UploadFile
          v-if="model === item.value && item.value"
          :file-list="getFileList(item.value! as string)"
          @update:file-list="(list) => updateAppendix(item.value! as string, list)"
          accept=".jpg,.jpeg,.png,.pdf,.bmp,.webp"
          :max-size="10"
          :max-count="item.max || 1"
          :force-array="true"
          :disabled="disabled"
        />
      </template>
    </n-space>
    <CurrentSelectTips v-if="notFinishOptions && notFinishOptions.length > 0">
      {{ notFinishOptions.join('，') }}
    </CurrentSelectTips>
  </n-space>
</template>
<script setup lang="ts">
  import CurrentSelectTips from '@/components/DrawerOrder/components/CurrentSelectTips/index.vue';
  import UploadFile from '@/components/UploadFile/index.vue';
  import { h, ref, computed, type PropType } from 'vue';
  import { NSelect, type SelectOption } from 'naive-ui';
  import { UploadFileList } from '@/components/DrawerOrder/types';

  const model = defineModel<string | undefined>('value');

  interface SelectOptions extends SelectOption {
    finish?: boolean;
    required?: boolean;
    min?: number;
    max?: number;
  }
  const props = defineProps({
    options: {
      type: Array as PropType<SelectOptions[]>,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const appendix = defineModel<Record<string, string>>('appendix', {
    required: true,
  });

  function getFileList(key: string): UploadFileList[] {
    const val = (appendix.value || {})[key];
    if (!val) return [];

    if (typeof val === 'string') {
      const s = val.trim();
      if (s === '') return [];
      let urls: any[] = [];
      try {
        urls = JSON.parse(s);
      } catch (_e1) {
        return [];
      }
      return Array.isArray(urls) ? urls.filter(Boolean).map((u: any) => ({ url: u, id: u })) : [];
    }

    return [];
  }

  function updateAppendix(key: string, list: UploadFileList[]) {
    const val = list.map((item) => item.url).filter(Boolean);
    appendix.value[key] = val.length > 0 ? JSON.stringify(val) : '';
  }
  function renderOption(option: SelectOptions) {
    const renderLabel = () => {
      if (!option.label) return option.label;
      if (!option.required) {
        return `${option.label}${option.finish ? '✔️' : ''}`;
      } else {
        return h('div', {}, [
          h('span', {}, { default: () => option.label }),
          h('span', { class: 'text-[#f00]' }, '*'),
          h('span', { class: 'finish-icon' }, option.finish ? '✔️' : ''),
        ]);
      }
    };
    return h('div', null, renderLabel());
  }
  const selectRef = ref<InstanceType<typeof NSelect> | null>(null);
  const notFinishOptions = computed(() =>
    props.options.filter((item) => !item.finish && item.required).map((item) => item.label)
  );
  const focus = () => {
    selectRef.value?.focus();
  };
  const focusInput = () => {
    selectRef.value?.focusInput();
  };
  const blur = () => {
    selectRef.value?.blur();
  };
  const blurInput = () => {
    selectRef.value?.blurInput();
  };
  defineExpose({
    selectRef,
    focus,
    focusInput,
    blur,
    blurInput,
  });
</script>
<style lang="less" scoped></style>
