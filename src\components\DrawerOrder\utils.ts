/**
 * 通过子节点id获取子节点信息
 * @param {Array} stagesList - 节点列表
 * @param {string} subNodeId - 子节点id
 * @returns {Object} - 子节点信息
 */

import { SubStage } from './types';

export const getSubNode = (stagesList: SubStage[], subNodeId: string) =>
  stagesList.find((item: SubStage) => item.id === subNodeId);

export const jsonToImage = (json: string | undefined) => {
  if (!json) return [];

  try {
    const data = JSON.parse(json);
    if (!Array.isArray(data)) return [];
    return data.map((item) => ({
      url: item,
      id: item,
    }));
  } catch (e) {
    // 如果解析失败，可能是单个 URL 字符串，尝试将其转换为数组格式
    // 或者返回空数组
    if (typeof json === 'string' && json.trim().startsWith('http')) {
      // 如果是 URL 字符串，转换为数组格式
      return [{ url: json, id: json }];
    }
    return [];
  }
};
export const imageToJson = (data: { url: string; id: string }[]) => {
  if (!Array.isArray(data)) return '';
  return data.length > 0 ? JSON.stringify(data.map((item) => item.url)) : '';
};

/**
 * 校验城市代码
 */
export const validateCityCode = (value: string) => {
  // 允许的层级：
  // - 普通省份：必须选到市级（code % 10000 !== 0 且 code % 100 === 0）
  // - 直辖市（11/12/31/50）：允许省级码（如 110000、310000）或市级码（110100、310100）
  const valueStr = String(value || '').trim();
  const code = Number(valueStr);
  if (!valueStr || !Number.isFinite(code) || valueStr.length !== 6) {
    return new Error('请选择正确的城市');
  }
  const prov = Math.floor(code / 10000); // 前两位
  const isMunicipality = [81, 82].includes(prov); // 港澳 // 11, 12, 31, 50,

  if (isMunicipality) {
    return true;
  }

  // 非直辖市必须为市级码
  if (code % 10000 === 0) {
    return new Error('请选择到市，不可选择省级');
  }

  return true;
};
