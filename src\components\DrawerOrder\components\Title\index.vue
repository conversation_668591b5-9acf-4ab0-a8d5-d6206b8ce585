<template>
  <div class="title">
    <span class="mr-2 text-lg font-bold">{{ title }}</span>
    <n-tag v-if="status === 'completed'" type="success" size="small" class="cursor-pointer">
      已完成
    </n-tag>
    <n-tag v-else-if="status === 'ongoing'" type="primary" size="small" class="cursor-pointer">
      进行中
    </n-tag>
    <n-tag v-else-if="status === 'returned'" type="warning" size="small" class="cursor-pointer">
      退回
    </n-tag>
    <n-tag v-else-if="status === 'failed'" type="error" size="small" class="cursor-pointer">
      失败
    </n-tag>
    <n-tag v-else-if="status === 'pending'" size="small" class="cursor-pointer"> 未开始 </n-tag>
  </div>
</template>

<script setup lang="ts">
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import { computed, toRef } from 'vue';

  const props = defineProps<{
    mainNode: number;
    subNode: number;
  }>();

  const orderDrawerStore = useOrderDrawerStore();
  const currentStage = toRef(orderDrawerStore, 'currentStage');

  const subStages = computed(() => {
    const sub = currentStage.value?.subStages?.find(
      (item) => item.id === `${props.mainNode}-${props.subNode}`
    );
    return sub;
  });

  const status = computed(() => subStages.value?.status);
  const title = computed(() => subStages.value?.title);
  defineExpose({
    status,
  });
</script>

<style lang="less" scoped>
  .title {
    display: flex;
    align-items: center;
    width: 100%;
    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background-color: #2d8cf0;
      margin-right: 10px;
    }
  }
</style>
