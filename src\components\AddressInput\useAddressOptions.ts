import { getProvinceCityArea } from '@/api/dashboard/workplace';
import { getProvinceAndCityApi } from '@/api/global';
import type { CascaderOption } from 'naive-ui';

import { cachePromise } from '@/utils';
import { ref } from 'vue';

export interface Options {
  label: string;
  value: string;
  children?: Options[];
}

export const fetchData = cachePromise(
  async () => {
    try {
      const { data } = await getProvinceCityArea();

      let result: Options[] = [];
      if (Array.isArray(data)) {
        result = data.map((province: any) => ({
          label: province.provinceName,
          value: String(province.provinceCode),
          children: province.cityDtoList?.map((city: any) => ({
            label: city.fullName,
            value: String(city.id),
            children: city.districtDtoList?.map((district: any) => ({
              label: district.fullName,
              value: String(district.id),
            })),
          })),
        })) as Options[];
      }

      return result as Options[];
    } catch (error) {
      return [];
    }
  },
  { ttl: 1000 * 10 }
);
export const fetchDataV2 = cachePromise(
  async function (args: any) {
    try {
      const { data } = await getProvinceCityArea();

      let result: CascaderOption[] = [];
      if (Array.isArray(data)) {
        result = data.map((province: any) => ({
          label: province.provinceName,
          value: String(province.provinceCode),
          children: province.cityDtoList?.map((city: any) => ({
            label: city.fullName,
            value: String(city.id),
            ...(args?.needDistrict
              ? {
                  children: city.districtDtoList?.map((district: any) => ({
                    label: district.fullName,
                    value: String(district.id),
                  })),
                }
              : {}),
          })),
        })) as CascaderOption[];
      }

      return result as CascaderOption[];
    } catch (error) {
      return [];
    }
  },
  { ttl: 1000 * 10 }
);
export function useAddressOptions() {
  const loading = ref(false);
  const options = ref([] as Options[]);

  const getOptions = async () => {
    if (options.value.length === 0) {
      loading.value = true;
      options.value = await fetchData();
      loading.value = false;
    }
    return options.value;
  };

  const getNameByCode = async (code: string): Promise<string> => {
    const opts = await getOptions();

    // 递归查询
    const handler = (list: Options[], code: string) => {
      for (const item of list) {
        if (String(item.value) === code) {
          return item;
        }
        if (item.children && item.children.length) {
          const it = handler(item.children, code);
          if (it) {
            return it;
          }
        }
      }
    };
    const it = handler(opts, code);
    return it ? it.label : '';
  };

  // 重构版：严格顺序匹配 省 -> 市 -> 区县；若市/区县未命中则留空；最后精确移除命中的片段得到详细地址
  const parseAddress = async (
    raw: string
  ): Promise<{ province: string; city: string; district: string; detail: string }> => {
    const opts = await getOptions();

    // 预处理
    const normalize = (s: string) =>
      (s || '')
        .replace(/[\s\t\r\n]+/g, '')
        .replace(/[，。、“”‘’·・,\.]/g, '')
        .trim();
    const text = normalize(raw);

    const stripSuffix = (name: string) =>
      (name || '').replace(/(特别行政区|自治区|自治州|地区|盟|省|市|区|县|旗)$/g, '');

    type Best = { label: string; start: number; end: number } | null;

    const findBest = (t: string, cands: { label: string; alt?: string }[]): Best => {
      let best: Best = null;
      for (const it of cands) {
        for (const nm of [it.label, it.alt].filter(Boolean) as string[]) {
          const i = t.indexOf(nm);
          if (i === -1) continue;
          const cur = { label: nm, start: i, end: i + nm.length };
          if (
            !best ||
            cur.start < (best as any).start ||
            (cur.start === (best as any).start &&
              cur.end - cur.start > (best as any).end - (best as any).start)
          ) {
            best = cur;
          }
        }
      }
      return best;
    };

    // 1) 省
    const provinces = opts.map((p) => ({
      label: p.label,
      alt: stripSuffix(p.label),
      code: String(p.value),
      node: p,
    }));
    const pBest = findBest(text, provinces);
    const provinceCode = pBest
      ? provinces.find((p) => p.label === pBest.label || p.alt === pBest.label)?.code || ''
      : '';
    const provinceNode = provinceCode
      ? opts.find((p) => String(p.value) === provinceCode) || null
      : null;

    // 2) 市（只在命中省的前提下进行，未命中则留空）
    let cityBest: Best = null;
    let cityCode = '';
    let cityNode: Options | null = null;
    if (provinceNode) {
      const cityCands = (provinceNode.children || []).map((c) => ({
        label: c.label,
        alt: stripSuffix(c.label),
        code: String(c.value),
        node: c,
      }));
      cityBest = findBest(text, cityCands);
      cityCode = cityBest
        ? cityCands.find((c) => c.label === cityBest!.label || c.alt === cityBest!.label)?.code ||
          ''
        : '';
      cityNode = cityCode
        ? (provinceNode.children || []).find((c) => String(c.value) === cityCode) || null
        : null;
    }

    // 3) 区/县（仅在命中市的前提下进行，未命中则留空）
    let districtBest: Best = null;
    let districtCode = '';
    if (cityNode) {
      const districtCands = (cityNode.children || []).map((d) => ({
        label: d.label,
        alt: stripSuffix(d.label),
        code: String(d.value),
      }));
      districtBest = findBest(text, districtCands);
      districtCode = districtBest
        ? districtCands.find(
            (d) => d.label === districtBest!.label || d.alt === districtBest!.label
          )?.code || ''
        : '';
    }

    // 4) 详细地址：只移除已命中的片段（省/市/区县），按文本中出现的位置精确裁剪，避免误删方位字
    let ranges: { start: number; end: number }[] = [];
    if (pBest) ranges.push({ start: pBest.start, end: pBest.end });
    if (cityBest) ranges.push({ start: cityBest.start, end: cityBest.end });
    if (districtBest) ranges.push({ start: districtBest.start, end: districtBest.end });
    ranges = ranges.sort((a, b) => b.start - a.start);
    let detail = text;
    for (const r of ranges) detail = detail.slice(0, r.start) + detail.slice(r.end);
    detail = detail.replace(/^[-—~·、,，。\s]+/, '').trim();

    return {
      province: provinceCode,
      city: cityCode,
      district: districtCode,
      detail,
    };
  };

  return {
    loading,
    options,
    getOptions,
    getNameByCode,
    parseAddress,
  };
}
