<template>
  <n-cascader
    v-bind="$attrs"
    v-model:value="model"
    :options="options"
    :loading="loading"
    clearable
    ref="cascader"
    placeholder="请选择地区"
    @update:value="handleUpdateValue"
  />
</template>

<script setup lang="ts">
  import { ref, onMounted, useTemplateRef } from 'vue';
  import { NCascader } from 'naive-ui';
  // import { getProvinceAndCityApi } from '@/api/global';
  import type { CascaderOption } from 'naive-ui';
  import { fetchDataV2 } from '@/components/AddressInput/useAddressOptions';

  type City = {
    fullName: string;
    id: string;
  };
  type Province = {
    provinceCode: string;
    provinceName: string;
    provinceShortName: string;
    cityDtoList: City[];
  };

  const model = defineModel<string[] | string | number[] | number | null>();
  const options = ref<CascaderOption[]>([]);
  const cascaderRef = useTemplateRef<InstanceType<typeof NCascader>>('cascader');
  defineExpose({
    findPathByValue,
    options,
  });
  const loading = ref(false);

  const emits = defineEmits(['update']);
  // 处理接口数据
  function transformToCascaderOptions(list: Province[]): CascaderOption[] {
    return list.map((province) => ({
      label: province.provinceName,
      value: province.provinceCode,
      provinceShortName: province.provinceShortName,
      children:
        province.cityDtoList?.map((city) => ({
          label: city.fullName,
          value: Number(city.id),
        })) || [],
    }));
  }
  async function getProvinceAndCity() {
    try {
      loading.value = true;

      // const {data} = await getProvinceAndCityApi();

      // options.value = transformToCascaderOptions(data);
      options.value = await fetchDataV2({ needDistrict: false });
    } catch (err) {
      console.log(err);
    } finally {
      loading.value = false;
    }
  }
  function findPathByValue(
    options: CascaderOption[],
    value: string | number
  ): CascaderOption[] | null {
    for (const option of options) {
      if (option.value === value) {
        return [option];
      }
      if (option.children?.length) {
        const childPath = findPathByValue(option.children, value);
        if (childPath) {
          return [option, ...childPath];
        }
      }
    }
    return null;
  }
  function handleUpdateValue(value: string | number | (string | number)[] | null) {
    const val = Array.isArray(value) ? value[value.length - 1] : value;
    const selectedOptions = findPathByValue(options.value, val!);
    //处理直辖市传市区信息
    if (
      selectedOptions &&
      selectedOptions?.length === 1 &&
      selectedOptions?.[0].value === selectedOptions?.[0]?.children?.[0].value
    ) {
      selectedOptions?.[0]?.children?.[0] && selectedOptions.push(selectedOptions[0].children[0]);
    }
    emits('update', selectedOptions);
  }
  onMounted(() => {
    getProvinceAndCity();
  });
</script>
