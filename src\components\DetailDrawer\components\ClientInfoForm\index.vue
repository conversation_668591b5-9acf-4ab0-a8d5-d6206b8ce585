<script setup lang="tsx">
  // import { DynamicForm } from '@/components/Form';
  // import { useYiShunForm } from '@/components/DetailDrawer/components/ClientInfoForm/composables/useYiShunForm';
  // import { useDeyiForm } from '@/components/DetailDrawer/components/ClientInfoForm/composables/useDeyiForm';
  import YishunForm from './components/YishunForm.vue';
  import DeyiForm from './components/DeyiForm.vue';
  import { computed, ref } from 'vue';
  import { customerMaterialSupplement } from '@/api/detail';

  const props = defineProps<{
    showForm: 'yishun' | 'deyi';
    clueId: number|string;
  }>();
  const yishunRef = ref();
  const deyiRef = ref();
  const formData = computed(() => {
    if (props.showForm === 'yishun') {
      return yishunRef?.value?.linkageFormData;
    } else if (props.showForm === 'deyi') {
      return deyiRef.value?.linkageFormData;
    } else {
      return {};
    }
  });
  //   defineExpose({
  //     formData,
  //   });
  //资方类型：1, "自有CRM"), EASY(3, "易顺CRM"),DY(4, "德易"),LIAN_ZHONG(5, "联众")
  function handleSave() {
    const typeOptions={
      'yishun':3,
      'deyi':4,
    }
    if(!typeOptions[props.showForm]){
      return;
    }
    console.log('formData.value', formData.value);
    console.log('props.clueId',{
      clueId: props.clueId,
      matchType: typeOptions[props.showForm],
      fieldData: formData.value,
    });
    // customerMaterialSupplement({
    //   clueId: props.clueId,
    //   matchType: typeOptions[props.showForm],
    //   fieldData: formData.value,
    // });
  }
</script>
<template>
  <div>
    <div class="client-info-form">
      <YishunForm ref="yishunRef" v-if="showForm === 'yishun'" />
      <DeyiForm ref="deyiRef" v-if="showForm === 'deyi'" />
    </div>
    <div class="flex items-center w-full justify-center p-[5px] button-container">
      <n-button type="primary" class="mr-5" @click="handleSave">保存</n-button>
      <n-button type="primary">生成留资二维码</n-button>
    </div>
  </div>

  <!-- <DynamicForm
      ref="linkageFormRef"
      v-model:config="linkageFormConfig as any"
      v-model="linkageFormData"
    /> -->
</template>
<style lang="less" scoped>
  //   @import '@/styles/custom/scrollbar.less';
  .client-info-form {
    max-height: 58vh;
    overflow-y: auto;
    // .custom-scrollbar();
  }
  .button-container {
    border-top: 1px solid #eee;
  }
</style>
