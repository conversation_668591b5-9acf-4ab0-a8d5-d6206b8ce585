<template>
  <n-card>
    <!-- 德易进件 -->
    <n-collapse arrow-placement="right" :expanded-names="expandedNames">
      <n-collapse-item name="2-2">
        <template #header>
          <Title :mainNode="2" :subNode="2" @click="toggleExpanded('2-2')" />
        </template>

        <div>
          <n-form
            ref="formRef"
            label-placement="left"
            size="medium"
            :model="formModel"
            :rules="currentRules"
            :disabled="formDisabled"
            :loading="formLoading"
            label-width="160px"
            @submit.prevent
          >
            <!-- 申请人居住信息 -->
            <n-grid :cols="GRID_COLS">
              <n-grid-item span="24">
                <n-form-item label="申请人居住地址" path="ipResDetail" required>
                  <AddressInput v-model="residentialAddressModel" :disabled="formDisabled" />
                  <SyncAddressButton
                    :innerOrderNo="innerOrderNo"
                    @address="residentialAddressModel = $event"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人有无驾照" path="ipHasDrivingLicense" required>
                  <n-select
                    v-model:value="formModel.ipHasDrivingLicense"
                    placeholder="请选择"
                    :options="fieldOptions.ipHasDrivingLicense || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人居住状况" path="ipResStatus" required>
                  <n-select
                    v-model:value="formModel.ipResStatus"
                    placeholder="请选择"
                    :options="fieldOptions.ipResStatus || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <!-- <n-grid-item>
                <n-form-item label="申请人本地居住年限" path="ipLocalResYears" required>
                  <n-select
                    v-model:value="formModel.ipLocalResYears"
                    placeholder="请选择"
                    :options="fieldOptions.ipLocalResYears || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item> -->
              <!-- <n-grid-item>
                <n-form-item label="申请人本地居住证明" path="ipLocalResProof" required>
                  <n-select
                    v-model:value="formModel.ipLocalResProof"
                    placeholder="请选择"
                    :options="fieldOptions.ipLocalResProof || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item> -->
              <n-grid-item>
                <n-form-item label="申请人学历情况" path="ipEduLevel" required>
                  <n-select
                    v-model:value="formModel.ipEduLevel"
                    placeholder="请选择"
                    :options="fieldOptions.ipEduLevel || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人就业状况" path="ipEmpStatus" required>
                  <n-select
                    v-model:value="formModel.ipEmpStatus"
                    placeholder="请选择"
                    :options="fieldOptions.ipEmpStatus || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
            </n-grid>

            <!-- 申请人就业信息 -->
            <n-grid :cols="GRID_COLS">
              <n-grid-item>
                <n-form-item label="申请人所属行业" path="ipIndustry" required>
                  <n-select
                    v-model:value="formModel.ipIndustry"
                    placeholder="请选择"
                    :options="fieldOptions.ipIndustry || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人单位名称" path="ipEmployerName" required>
                  <n-input
                    v-model:value="formModel.ipEmployerName"
                    placeholder="请输入单位名称"
                    clearable
                    maxlength="50"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人单位性质" path="ipEmployerType" required>
                  <n-select
                    v-model:value="formModel.ipEmployerType"
                    placeholder="请选择"
                    :options="fieldOptions.ipEmployerType || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item span="24">
                <n-form-item label="申请人单位地址" path="ipWorkDetail" required>
                  <AddressInput v-model="companyAddressModel" :disabled="formDisabled" />
                  <SyncAddressButton
                    :innerOrderNo="innerOrderNo"
                    @address="companyAddressModel = $event"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人工作职务" path="ipJobPosition" required>
                  <n-select
                    v-model:value="formModel.ipJobPosition"
                    placeholder="请选择"
                    :options="fieldOptions.ipJobPosition || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人工作职称" path="ipJobTitle" required>
                  <n-select
                    v-model:value="formModel.ipJobTitle"
                    placeholder="请选择"
                    :options="fieldOptions.ipJobTitle || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人税后月收入" path="ipNetMonthlyIncome" required>
                  <n-input
                    v-model:value="formModel.ipNetMonthlyIncome"
                    placeholder="请输入月收入"
                    clearable
                    maxlength="9"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人首次工作日期" path="ipFirstWorkDate" required>
                  <n-date-picker
                    v-model:formatted-value="formModel.ipFirstWorkDate"
                    type="date"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    placeholder="yyyy-MM-dd"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人现单位工作年限" path="ipCurrentJobYears" required>
                  <n-input-number
                    :value="
                      formModel.ipCurrentJobYears ? Number(formModel.ipCurrentJobYears) : null
                    "
                    placeholder="工作年限默认一年"
                    clearable
                    :min="1"
                    :max="99"
                    @update:value="formModel.ipCurrentJobYears = $event ? $event.toString() : ''"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人现工作固话/手机" path="ipWorkPhone" required>
                  <n-input
                    v-model:value="formModel.ipWorkPhone"
                    placeholder="请输入联系电话"
                    clearable
                    maxlength="20"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请用途" path="ipLoanPurpose" required>
                  <n-select
                    v-model:value="formModel.ipLoanPurpose"
                    placeholder="请输入申请用途"
                    clearable
                    :options="fieldOptions.ipLoanPurpose || []"
                  />
                </n-form-item>
              </n-grid-item>
            </n-grid>

            <!-- 公牌公司地址 -->
            <n-grid v-if="isPublicNotice" :cols="GRID_COLS">
              <n-grid-item span="24">
                <n-form-item label="公牌公司地址" path="ipCompDetail">
                  <AddressInput v-model="publicPlateCompanyAddressModel" :disabled="formDisabled" />
                  <n-tooltip trigger="hover">
                    <template #trigger>
                      <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                        <QuestionCircleTwotone />
                      </n-icon>
                    </template>
                    请输入详细地址
                  </n-tooltip>
                  <SyncAddressButton
                    :innerOrderNo="innerOrderNo"
                    @address="publicPlateCompanyAddressModel = $event"
                  />
                </n-form-item>
              </n-grid-item>
            </n-grid>

            <!-- 婚姻状况 & 配偶信息 -->
            <n-grid :cols="GRID_COLS">
              <n-grid-item>
                <n-form-item label="婚姻状况" path="ipMaritalStatus" required>
                  <n-select
                    v-model:value="formModel.ipMaritalStatus"
                    placeholder="请选择"
                    :options="fieldOptions.ipMaritalStatus || []"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <template v-if="formModel.ipMaritalStatus === '已婚'">
                <n-grid-item>
                  <n-form-item label="配偶姓名" path="ipSpouseName" required>
                    <n-input
                      v-model:value="formModel.ipSpouseName"
                      placeholder="请输入配偶姓名"
                      clearable
                      maxlength="20"
                    />
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      请输入配偶真实姓名
                    </n-tooltip>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="配偶身份证号" path="ipSpouseId" required>
                    <n-input
                      v-model:value="formModel.ipSpouseId"
                      placeholder="请输入配偶身份证号"
                      maxlength="18"
                      clearable
                    />
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      请输入配偶身份证号码
                    </n-tooltip>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="配偶手机号" path="ipSpousePhone" required>
                    <n-input
                      v-model:value="formModel.ipSpousePhone"
                      placeholder="请输入配偶手机号"
                      clearable
                      maxlength="11"
                    />
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      请输入配偶手机号码
                    </n-tooltip>
                  </n-form-item>
                </n-grid-item>
              </template>
            </n-grid>

            <!-- 紧急联系人 -->
            <n-card class="my-4">
              <n-space vertical>
                <n-space
                  v-for="(item, index) in emergencyContacts"
                  :key="`emergencyContacts${index}`"
                  class="w-full"
                  justify="space-between"
                  :wrap="false"
                >
                  <n-form-item
                    :label="`紧急联系人${index + 1}与申请人关系`"
                    :path="`emergencyContacts[${index}].relationshipName`"
                    required
                    class="w-[300px]"
                  >
                    <n-select
                      v-model:value="item.relationshipName"
                      placeholder="请选择"
                      :options="getRelationshipOptions(index)"
                      clearable
                    />
                    <n-tooltip v-if="index < 2" trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      <span>{{ getContactTipText(index) }}</span>
                    </n-tooltip>
                  </n-form-item>
                  <n-form-item
                    :label="`紧急联系人${index + 1}姓名`"
                    :path="`emergencyContacts[${index}].name`"
                    required
                  >
                    <n-input
                      v-model:value="item.name"
                      placeholder="请输入姓名"
                      clearable
                      maxlength="20"
                    />
                  </n-form-item>
                  <n-form-item
                    :label="`紧急联系人${index + 1}手机号`"
                    :path="`emergencyContacts[${index}].mobile`"
                    required
                  >
                    <n-input
                      v-model:value="item.mobile"
                      placeholder="请输入手机号"
                      clearable
                      maxlength="11"
                    />
                  </n-form-item>
                </n-space>
                <n-space justify="end">
                  <n-icon
                    class="cursor-pointer"
                    color="#1890ff"
                    @click="handleAddEmergencyContacts"
                  >
                    <PlusOutlined />
                  </n-icon>
                  <n-icon
                    class="cursor-pointer"
                    color="#1890ff"
                    @click="handleDeleteEmergencyContacts"
                  >
                    <MinusOutlined />
                  </n-icon>
                </n-space>
              </n-space>
            </n-card>

            <!-- 身份证信息 -->
            <n-card class="my-4">
              <n-flex align="center">
                <div>
                  <SubTitle title="身份证相关" />
                  <n-button type="primary" size="small" @click="onPreview">预览</n-button>
                </div>
                <div class="flex-1 mb-[-25px]">
                  <n-grid :cols="GRID_COLS">
                    <n-grid-item>
                      <n-form-item label="申请人身份证起始日期" path="ipIdStartDate" required>
                        <n-date-picker
                          v-model:formatted-value="formModel.ipIdStartDate"
                          type="date"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          placeholder="yyyy-MM-dd"
                          clearable
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="申请人身份证截止日期" path="ipIdExpireDate" required>
                        <n-date-picker
                          v-model:formatted-value="formModel.ipIdExpireDate"
                          type="date"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          placeholder="yyyy-MM-dd"
                          clearable
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="申请人民族" path="ipEthnicity">
                        <n-select
                          v-model:value="formModel.ipEthnicity"
                          placeholder="请选择"
                          :options="fieldOptions.ipEthnicity || []"
                          clearable
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item span="24">
                      <n-form-item label="申请人户籍地址" path="ipHukouDetail" required>
                        <AddressInput v-model="householdAddressModel" :disabled="formDisabled" />
                        <SyncAddressButton
                          :innerOrderNo="innerOrderNo"
                          @address="householdAddressModel = $event"
                        />
                      </n-form-item>
                    </n-grid-item>
                  </n-grid>
                </div>
              </n-flex>
            </n-card>

            <!-- 附件资料 -->
            <n-card class="my-4">
              <n-grid :cols="GRID_COLS">
                <n-grid-item span="24">
                  <n-form-item label="附件资料" path="ipAttachVehicleLicense" required>
                    <CombineSelect
                      v-model:value="selectedAttachment"
                      v-model:appendix="formModel"
                      :options="selectedAttachmentOptions"
                      :disabled="formDisabled"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-card>

            <n-flex justify="center">
              <n-button
                :type="canSubmit ? 'primary' : 'default'"
                :disabled="formDisabled"
                :loading="formLoading"
                @click="canSubmit ? handleSubmit() : handleSave()"
              >
                {{ canSubmit ? '提交' : '保存' }}
              </n-button>
            </n-flex>
          </n-form>

          <template v-if="initialData.ipContractOrg">
            <n-divider dashed />

            <SubTitle title="德易进件" desc="前置要求: 完善文件资料内容上传" />

            <n-descriptions label-placement="top" bordered :column="6">
              <n-descriptions-item label="签约机构">
                {{ initialData.ipContractOrg || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="审批备注">
                {{ initialData.ipApprovalRemarks || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="批复金额">
                {{ initialData.ipApprovedAmount || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="批复月供">
                {{ initialData.ipApprovedMonthFee || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="审批结果">
                <n-tag
                  v-if="initialData.ipApprovalResult"
                  size="small"
                  :type="
                    String(initialData.ipApprovalResult) === String(OrgSignStatus.Pass)
                      ? 'success'
                      : 'warning'
                  "
                >
                  {{ OrgSignStatusMap[initialData.ipApprovalResult] }}
                </n-tag>
                <template v-else> - </template>
              </n-descriptions-item>
              <n-descriptions-item label="操作">
                <n-button
                  size="small"
                  type="primary"
                  :disabled="String(initialData.ipApprovalResult) === String(OrgSignStatus.Pass)"
                  v-cooldown
                  @click="queryInnerOrderStatus"
                >
                  状态查询
                </n-button>
              </n-descriptions-item>
            </n-descriptions>
          </template>
        </div>
      </n-collapse-item>
    </n-collapse>
  </n-card>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import AddressInput from '@/components/AddressInput/index.vue';
  import CombineSelect from '@/components/DrawerOrder/components/CombineSelect/index.vue';
  import { QuestionCircleTwotone, PlusOutlined, MinusOutlined } from '@vicons/antd';
  import SyncAddressButton from '@/components/DrawerOrder/components/ApplicationContract/components/SyncAddressButton/index.vue';

  import { ref, reactive, computed, onMounted, watch, inject } from 'vue';
  import type { FormInst } from 'naive-ui';
  import { GRID_COLS } from '@/components/DrawerOrder/config';
  import { useBaseData } from '../../composables/useBaseData';
  import { saveFieldData, submitForm, getInnerOrderStatus } from '@/api/dashboard/deyi';
  import type { Fields } from '@/components/DrawerOrder/types';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import {
    OrgSignStatus,
    OrgSignStatusMap,
    RelationShip,
    RelationShipOptions,
  } from '@/components/DrawerOrder/enum';
  import { usePreviewImage } from '@/composables/usePreviewImage';
  import { useFormDisabled } from '@/components/DrawerOrder/composables/useFormDisabled';
  import { jsonToImage } from '@/components/DrawerOrder/utils';
  import dayjs from 'dayjs';
  import { bufferedEmitter, EventNames } from '@/utils/eventBus';
  import { onUnmounted } from 'vue';

  interface Props {
    innerOrderNo: string;
  }
  const props = defineProps<Props>();
  const expandedNames = inject('expandedNames');
  const toggleExpanded = inject('toggleExpanded') as (name: string) => void;
  const orderDrawerStore = useOrderDrawerStore();
  const { previewImage } = usePreviewImage();
  const { isCurrentStagePending, isCurrentStageCompleted } = useFormDisabled();
  const isPublicNotice = ref<boolean>(false);
  const formDisabled = computed(
    () =>
      isCurrentStageCompleted('2-2') &&
      String(initialData.ipApprovalResult) !== String(OrgSignStatus.Back)
  );

  const formRef = ref<FormInst | null>(null);
  const formLoading = ref(false);
  const formModel = reactive<Partial<Fields>>({
    // 居住地址
    ipResProvince: '',
    ipResCity: '',
    ipResDistrict: '',
    ipResDetail: '',
    // 居住/驾照等
    ipHasDrivingLicense: '',
    ipResStatus: '',
    ipEduLevel: '',
    ipEmpStatus: '',
    // 就业信息
    ipIndustry: '',
    ipEmployerName: '',
    ipEmployerType: '',
    ipWorkProvince: '',
    ipWorkCity: '',
    ipWorkDistrict: '',
    ipWorkDetail: '',
    ipJobPosition: '',
    ipJobTitle: '',
    ipNetMonthlyIncome: '',
    ipFirstWorkDate: undefined,
    ipCurrentJobYears: undefined,
    ipWorkPhone: '',
    ipLoanPurpose: '',
    // 公牌公司地址
    ipCompProvince: '',
    ipCompCity: '',
    ipCompDistrict: '',
    ipCompDetail: '',
    // 婚姻/配偶
    ipMaritalStatus: '',
    ipSpouseName: '',
    ipSpouseId: '',
    ipSpousePhone: '',
    // 身份证
    ipIdStartDate: undefined,
    ipIdExpireDate: undefined,
    ipEthnicity: '',
    ipHukouProvince: '',
    ipHukouCity: '',
    ipHukouDistrict: '',
    ipHukouDetail: '',
    // 附件（落库为各自字段，组件使用中间模型）
    ipAttachResStability: '',
    ipAttachVehicleLicense: '',
    ipAttachDrivingLicense: '',
    ipAttachDashboard: '',
    ipAttachOdometer: '',
    ipAttachOrigRegForm: '',
    ipBusinessLicense: '',
    // 表格展示字段
    ipContractOrg: '',
    ipApprovalRemarks: '',
    ipApprovedAmount: '',
    ipApprovedMonthFee: '',
    ipApprovalResult: '',
    // 紧急联系人最终串
    ipEmergencyContact: '',
    // 单独查询身份证图片信息
    preAuditIdCardReverse: '',
    preAuditIdCardFront: '',
  });

  // 通过 useBaseData 获取后端数据与选项
  const {
    data: initialData,
    options: fieldOptions,
    getData,
    getOptions,
  } = useBaseData({
    innerOrderNo: props.innerOrderNo,
    keys: [...Object.keys(formModel), 'preAuditIdCardNum'],
  });

  // v-model 适配 AddressInput（对象 <-> 扁平 Fields）
  const residentialAddressModel = computed({
    get: () => ({
      province: formModel.ipResProvince || '',
      city: formModel.ipResCity || '',
      district: formModel.ipResDistrict || '',
      detail: formModel.ipResDetail || '',
    }),
    set: (val: any) => {
      formModel.ipResProvince = val?.province ? String(val.province) : '';
      formModel.ipResCity = val?.city || '';
      formModel.ipResDistrict = val?.district || '';
      formModel.ipResDetail = val?.detail || '';
    },
  });

  const companyAddressModel = computed({
    get: () => ({
      province: formModel.ipWorkProvince || '',
      city: formModel.ipWorkCity || '',
      district: formModel.ipWorkDistrict || '',
      detail: formModel.ipWorkDetail || '',
    }),
    set: (val: any) => {
      formModel.ipWorkProvince = val?.province || '';
      formModel.ipWorkCity = val?.city || '';
      formModel.ipWorkDistrict = val?.district || '';
      formModel.ipWorkDetail = val?.detail || '';
    },
  });

  const publicPlateCompanyAddressModel = computed({
    get: () => ({
      province: formModel.ipCompProvince || '',
      city: formModel.ipCompCity || '',
      district: formModel.ipCompDistrict || '',
      detail: formModel.ipCompDetail || '',
    }),
    set: (val: any) => {
      formModel.ipCompProvince = val?.province || '';
      formModel.ipCompCity = val?.city || '';
      formModel.ipCompDistrict = val?.district || '';
      formModel.ipCompDetail = val?.detail || '';
    },
  });

  const householdAddressModel = computed({
    get: () => ({
      province: formModel.ipHukouProvince || '',
      city: formModel.ipHukouCity || '',
      district: formModel.ipHukouDistrict || '',
      detail: formModel.ipHukouDetail || '',
    }),
    set: (val: any) => {
      formModel.ipHukouProvince = val?.province || '';
      formModel.ipHukouCity = val?.city || '';
      formModel.ipHukouDistrict = val?.district || '';
      formModel.ipHukouDetail = val?.detail || '';
    },
  });

  // 紧急联系人（本地编辑 -> 提交前合并到 ipEmergencyContact）
  const emergencyContacts = ref<{ relationshipName: string; name: string; mobile: string }[]>([
    { relationshipName: '', name: '', mobile: '' },
    { relationshipName: '', name: '', mobile: '' },
  ]);
  watch(
    () => emergencyContacts.value,
    (val) => {
      const list = val.filter((x) => x.relationshipName && x.name && x.mobile);
      if (list.length > 0) {
        formModel.ipEmergencyContact = JSON.stringify(list);
      } else {
        formModel.ipEmergencyContact = '';
      }
    },
    { deep: true }
  );

  // 附件中间模型
  const selectedAttachment = ref('');
  const selectedAttachmentOptions = computed<any[]>(() => {
    const base = [
      // {
      //   label: '居住稳定性凭证(1)',
      //   value: 'ipAttachResStability',
      //   finish: jsonToImage(formModel.ipAttachResStability).length >= 1,
      //   required: false,
      //   min: 1,
      //   max: 9,
      // },
      {
        label: '车辆行驶证',
        value: 'ipAttachVehicleLicense',
        finish: jsonToImage(formModel.ipAttachVehicleLicense)?.length >= 1,
        required: true,
        min: 1,
        max: 9,
      },
      {
        label: '驾驶证(1)',
        value: 'ipAttachDrivingLicense',
        finish: jsonToImage(formModel.ipAttachDrivingLicense)?.length >= 1,
        required: true,
        min: 1,
        max: 9,
      },
      // {
      //   label: '中控台',
      //   value: 'ipAttachDashboard',
      //   finish: jsonToImage(formModel.ipAttachDashboard)?.length >= 1,
      //   required: true,
      //   min: 1,
      //   max: 9,
      // },
      {
        label: '里程表',
        value: 'ipAttachOdometer',
        finish: jsonToImage(formModel.ipAttachOdometer)?.length >= 1,
        required: true,
        min: 1,
        max: 9,
      },
      {
        label: '原登记证(2)',
        value: 'ipAttachOrigRegForm',
        finish: jsonToImage(formModel.ipAttachOrigRegForm)?.length >= 2,
        required: true,
        min: 2,
        max: 9,
      },
    ];

    // 如果有公牌公司地址，需要添加公牌公司地址的附件
    if (isPublicNotice.value) {
      base.push({
        label: '营业执照',
        value: 'ipBusinessLicense',
        finish: jsonToImage(formModel.ipBusinessLicense)?.length >= 1,
        required: true,
        min: 1,
        max: 9,
      });
    }

    return [...base];
  });

  // 恢复初始数据
  watch(
    () => initialData,
    (val) => {
      if (!val) return;
      Object.keys(formModel).forEach((k) => {
        // @ts-ignore
        const value = val[k];
        // 确保日期字段是字符串格式或 undefined
        if (k === 'ipFirstWorkDate' || k === 'ipIdStartDate' || k === 'ipIdExpireDate') {
          if (value && typeof value === 'string') {
            formModel[k] = value;
          } else {
            formModel[k] = undefined;
          }
        } else {
          formModel[k] = value ?? formModel[k];
        }
      });

      // 首次工作时间 默认第23岁日期 未满23岁取18岁日期，未满18则空
      if (!formModel.ipFirstWorkDate) {
        try {
          const serverTime = initialData.serverTime || dayjs().format('YYYY-MM-DD');
          const preAuditIdCardNum = initialData.preAuditIdCardNum;
          const birthday = preAuditIdCardNum?.slice?.(6, 14) || ''; // 获取身份证号码上的生日日期

          if (birthday && birthday.length === 8) {
            // 解析身份证日期格式 YYYYMMDD
            const birthdayDate = dayjs(birthday, 'YYYYMMDD');
            if (!birthdayDate.isValid()) return;

            const age23Date = birthdayDate.add(23, 'year');
            const age18Date = birthdayDate.add(18, 'year');
            const currentDate = dayjs(serverTime);

            if (age18Date.isAfter(currentDate)) {
              // 未满18岁，不设置
              return;
            } else if (age23Date.isAfter(currentDate)) {
              // 大于等于18岁但小于23岁，设置为18岁生日
              formModel.ipFirstWorkDate = age18Date.format('YYYY-MM-DD');
            } else {
              // 大于等于23岁，设置为23岁生日
              formModel.ipFirstWorkDate = age23Date.format('YYYY-MM-DD');
            }
          }
        } catch (error) {
          console.error('计算首次工作日期失败:', error);
        }
      }

      if (!formModel.ipCurrentJobYears) {
        formModel.ipCurrentJobYears = '1';
      }

      // 恢复紧急联系人
      if (typeof formModel.ipEmergencyContact === 'string' && formModel.ipEmergencyContact) {
        try {
          const arr = JSON.parse(formModel.ipEmergencyContact) as any[];
          if (Array.isArray(arr)) {
            emergencyContacts.value = arr.map((x) => ({
              relationshipName: x.relationshipName || '',
              name: x.name || '',
              mobile: x.mobile || '',
            }));
          }
        } catch {}
      }
    },
    { immediate: true, deep: true }
  );

  // 校验规则
  const baseFormRules = {
    // 居住地址
    ipResProvince: [{ required: true, message: '请选择居住地址省份', trigger: 'change' }],
    ipResCity: [{ required: true, message: '请选择居住地址城市', trigger: 'change' }],
    ipResDistrict: [{ required: true, message: '请选择居住地址区县', trigger: 'change' }],
    ipResDetail: [
      {
        required: true,
        validator(_r, v) {
          if (!v || String(v).trim() === '') return new Error('请填写居住详细地址');
          return true;
        },
        trigger: 'change',
      },
    ],
    ipEduLevel: [{ required: true, message: '请填写学历情况', trigger: 'blur' }],
    ipEmpStatus: [{ required: true, message: '请选择就业状况', trigger: 'change' }],
    ipIndustry: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
    ipEmployerName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
    ipEmployerType: [{ required: true, message: '请选择单位性质', trigger: 'change' }],
    // 单位地址
    ipWorkProvince: [{ required: true, message: '请选择单位地址省份', trigger: 'change' }],
    ipWorkCity: [{ required: true, message: '请选择单位地址城市', trigger: 'change' }],
    ipWorkDistrict: [{ required: true, message: '请选择单位地址区县', trigger: 'change' }],
    ipWorkDetail: [
      {
        required: true,
        validator(_r, v) {
          if (!v || String(v).trim() === '') return new Error('请填写单位详细地址');
          return true;
        },
        trigger: 'change',
      },
    ],
    ipJobPosition: [{ required: true, message: '请选择工作职务', trigger: 'change' }],
    ipJobTitle: [{ required: true, message: '请选择工作职称', trigger: 'change' }],
    ipNetMonthlyIncome: [{ required: true, message: '请输入月收入', trigger: 'blur' }],
    ipFirstWorkDate: [{ required: true, message: '请选择首次工作日期', trigger: 'change' }],
    ipCurrentJobYears: [{ required: true, message: '请输入工作年限', trigger: 'blur' }],
    ipWorkPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
    ipLoanPurpose: [{ required: true, message: '请输入申请用途', trigger: 'blur' }],
    ipMaritalStatus: [{ required: true, message: '请选择婚姻状况', trigger: 'change' }],
    ipSpouseName: [
      {
        required: true,
        validator(_r, v) {
          if (formModel.ipMaritalStatus !== '已婚') return true;
          if (!v || String(v).trim() === '') return new Error('请输入配偶姓名');
          return true;
        },
        trigger: 'blur',
      },
    ],
    ipSpouseId: [
      {
        required: true,
        validator(_r, v) {
          if (formModel.ipMaritalStatus !== '已婚') return true;
          if (!v || String(v).trim() === '') return new Error('请输入配偶身份证号');
          return true;
        },
        trigger: 'blur',
      },
    ],
    ipSpousePhone: [
      {
        required: true,
        validator(_r, v) {
          if (formModel.ipMaritalStatus !== '已婚') return true;
          if (!v || String(v).trim() === '') return new Error('请输入配偶手机号');
          return true;
        },
        trigger: 'blur',
      },
    ],
    ipIdStartDate: [{ required: true, message: '请选择身份证起始日期', trigger: 'change' }],
    ipIdExpireDate: [{ required: true, message: '请选择身份证截止日期', trigger: 'change' }],
    // 户籍地址
    ipHukouProvince: [{ required: true, message: '请选择户籍地址省份', trigger: 'change' }],
    ipHukouCity: [{ required: true, message: '请选择户籍地址城市', trigger: 'change' }],
    ipHukouDistrict: [{ required: true, message: '请选择户籍地址区县', trigger: 'change' }],
    ipHukouDetail: [
      {
        required: true,
        validator(_r, v) {
          if (!v || String(v).trim() === '') return new Error('请填写户籍详细地址');
          return true;
        },
        trigger: 'change',
      },
    ],
    ipAttachVehicleLicense: [
      { required: true, message: '请上传附件', trigger: 'change' },
      {
        required: true,
        validator(_r, _v) {
          const keys = selectedAttachmentOptions.value
            .filter((x) => x.required)
            .map((x) => x.value);

          for (const key of keys) {
            if (!formModel[key]) {
              const name = selectedAttachmentOptions.value.find((x) => x.value === key)?.label;
              return new Error(`请上传附件,${name}`);
            }
          }

          return true;
        },
        trigger: 'change',
      },
    ],
  } as Record<string, any[]>;

  const requiredFields = Object.keys(baseFormRules).filter(
    (f) =>
      (baseFormRules[f] || []).some((r) => r.required || r.validator) &&
      f !== 'ipAttachVehicleLicense'
  );

  const spouseRequiredFields = ['ipSpouseName', 'ipSpouseId', 'ipSpousePhone'];

  const isEmptyVal = (val: any) => {
    if (val === undefined || val === null) return true;
    if (typeof val === 'string') return val.trim() === '';
    if (Array.isArray(val)) return val.length === 0;
    if (typeof val === 'object') return Object.keys(val).length === 0;
    return false;
  };

  const canSubmit = computed(() => {
    const skipFields = formModel.ipMaritalStatus === '已婚' ? [] : spouseRequiredFields;
    for (const key of requiredFields) {
      if (skipFields.includes(key)) continue;
      const v = formModel[key];
      if (isEmptyVal(v)) return false;
    }

    for (const key of selectedAttachmentOptions.value
      .filter((x) => x.required)
      .map((x) => x.value)) {
      if (!selectedAttachmentOptions.value.find((x) => x.value === key)?.finish) {
        return false;
      }
    }

    const ok =
      emergencyContacts.value.length > 0 &&
      emergencyContacts.value.every((c) => c.name && c.mobile && c.relationshipName);
    if (!ok) return false;

    if (isCurrentStagePending('2-2')) return false;

    return true;
  });

  const currentRules = computed(() => {
    if (canSubmit.value) return baseFormRules;
    const rules: any = {};
    Object.keys(baseFormRules).forEach((k) => {
      rules[k] = (baseFormRules[k] as any[]).filter((r) => !r.required);
    });
    return rules;
  });

  const handleSave = async () => {
    try {
      await formRef.value?.validate();
    } catch {
      window.$message?.error('填写内容格式不正确');
      return;
    }
    try {
      formLoading.value = true;
      const innerOrderNo = props.innerOrderNo;

      await saveFieldData({ innerOrderNo, fieldData: formModel });
      window.$message?.success('保存成功');
    } catch (e) {
      window.$message?.error('保存失败');
      console.error(e);
    } finally {
      formLoading.value = false;
    }
  };

  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();
    } catch {
      return;
    }
    try {
      formLoading.value = true;
      const innerOrderNo = props.innerOrderNo;

      await saveFieldData({ innerOrderNo, fieldData: formModel });
      await submitForm({ innerOrderNo, mainNodeCode: 2, subNodeCode: 2 });

      orderDrawerStore.triggerRefresh();
      getData();
      window.$message?.success('提交成功');
    } catch (e) {
      window.$message?.error('提交失败');
      console.error(e);
    } finally {
      formLoading.value = false;
    }
  };

  const handleAddEmergencyContacts = () => {
    if (emergencyContacts.value.length >= 4) {
      window.$message?.error('最多添加4条紧急联系人');
      return;
    }
    emergencyContacts.value.push({ name: '', mobile: '', relationshipName: '' });
  };

  const handleDeleteEmergencyContacts = (index?: number) => {
    if (emergencyContacts.value.length <= 2) {
      window.$message?.error('至少保留两条紧急联系人');
      return;
    }
    if (typeof index !== 'number') index = emergencyContacts.value.length - 1;
    emergencyContacts.value.splice(index, 1);
  };

  // 关系选项：紧急联系人1禁用"朋友""兄弟/姐妹"，联系人2禁用"朋友"
  const getRelationshipOptions = (index: number) => {
    const disabledValues: string[] = [];
    if (index === 0) {
      disabledValues.push(RelationShip.Friend, RelationShip.Brother);
    } else if (index === 1) {
      disabledValues.push(RelationShip.Friend);
    }
    return (RelationShipOptions || []).map((opt) => ({
      ...opt,
      disabled: disabledValues.includes(opt.value),
    }));
  };

  // 获取联系人提示文本
  const getContactTipText = (index: number): string => {
    const tipMap: Record<number, string> = {
      0: '联系人1需直系亲属',
      1: '联系人2不可选朋友',
    };
    return tipMap[index] || '';
  };

  const onPreview = () => {
    if (!formModel.preAuditIdCardFront || !formModel.preAuditIdCardReverse) {
      window.$message?.error('未找到身份证照片');
      return;
    }

    previewImage([
      ...(formModel.preAuditIdCardFront ? JSON.parse(formModel.preAuditIdCardFront) : []),
      ...(formModel.preAuditIdCardReverse ? JSON.parse(formModel.preAuditIdCardReverse) : []),
    ] as string[]);
  };

  const queryInnerOrderStatus = async () => {
    await getInnerOrderStatus({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };

  onMounted(() => {
    getData();
    getOptions();

    bufferedEmitter.on(EventNames.DEYI_POPUP_IS_PUBLIC_NOTICE, (isPublic: boolean) => {
      isPublicNotice.value = isPublic;
    });
  });

  onUnmounted(() => {
    bufferedEmitter.off(EventNames.DEYI_POPUP_IS_PUBLIC_NOTICE);
  });
</script>

<style lang="less" scoped></style>
