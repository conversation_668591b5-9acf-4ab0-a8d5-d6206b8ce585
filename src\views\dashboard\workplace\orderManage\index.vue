<template>
  <n-card :bordered="false">
    <BasicForm
      :enable-cache="true"
      @register="register"
      @submit="reloadTable"
      @reset="resetTableForm()"
    >
      <template #blongUserId="{ model, field }">
        <NSelect
          v-model:value="model[field]"
          label-field="username"
          value-field="id"
          multiple
          :options="userList"
          placeholder="请选择归属人"
          filterable
        />
      </template>
      <template #actionButton>
        <n-button
          v-permission="{ action: 'export' }"
          :loading="exportLoading"
          @click="handleExport"
        >
          导出
        </n-button>
      </template>
    </BasicForm>
  </n-card>
  <n-card :bordered="false" class="mt-3">
    <BasicTable
      :columns="tableColumns"
      :request="loadDataTable"
      :row-key="(row:ListData) => row.id"
      :actionColumn="actionColumn"
      :scroll-x="0"
      ref="action"
      :striped="true"
    />
  </n-card>
</template>

<script setup lang="tsx">
  import { columns, ListData } from './columns';
  import { BasicTable } from '@/components/Table';
  import { BasicForm, FormSchema, useForm } from '@/components/Form';
  import { useDateOptions } from '@/composables/useDateOptions';
  import { reactive, useTemplateRef, ref, onMounted, computed, onActivated, nextTick } from 'vue';
  import { useRoute } from 'vue-router';
  import { createMonthRangeDisabledFn } from '@/utils/datePickerDisabled';
  import { useDrawerOrder } from '@/composables/useDrawerOrder';
  import { UserInfoType } from '@/store/modules/user';
  import { useMessage } from 'naive-ui';

  import {
    getOrderListApi,
    exportOrderListApi,
    getOrderNodeApi,
    getSubOrderNodeApi,
    getOrderDataCountApi,
    cancelOrderApi,
  } from '@/api/dashboard/orderMange';
  import { useUser } from '@/store/modules/user';
  import { getUserListApi } from '@/api/global';
  defineOptions({
    name: 'DashboardOrderManage',
  });
  const exportLoading = ref(false);
  const actionRef = useTemplateRef<InstanceType<typeof BasicTable>>('action');
  const { open: openDrawerOrder } = useDrawerOrder();
  const userStore = useUser();
  const route = useRoute();
  const message = useMessage();

  // 动态选项的 ref
  const termOptions = ref<Array<{ label: string; value: any }>>([
    { label: '12', value: '12' },
    { label: '18', value: '18' },
    { label: '24', value: '24' },
    { label: '30', value: '30' },
    { label: '36', value: '36' },
    { label: '48', value: '48' },
    { label: '60', value: '60' },
  ]);
  const statusOptions = ref<Array<{ label: string; value: any }>>([
    { label: '跟进中', value: '1' },
    { label: '订单取消', value: '2' },
    { label: '订单完结', value: '3' },
  ]);
  const businessOrderNodeOptions = ref<Array<{ label: string; value: any }>>([]);
  const loanNodeSubCodeOptions = ref<Array<{ label: string; value: any }>>([]);
  const nodeStatusOptions = ref<Array<{ label: string; value: any }>>([]);
  // 归属人list
  const userList = ref<any[]>([]);

  // 存储订单子节点的完整数据，用于获取节点状态选项
  const loanNodeSubCodeData = ref<
    Array<{ code: string; children?: Array<{ desc: string; code: string }> }>
  >([]);

  // 获取订单节点数据
  const fetchOrderNodeOptions = async (managementCode) => {
    try {
      const { data } = await getOrderNodeApi(managementCode, {});
      businessOrderNodeOptions.value = data.map((item) => ({
        label: item.desc,
        value: item.code,
      }));
    } catch (error) {
      console.error('获取订单节点数据失败:', error);
      businessOrderNodeOptions.value = [];
    }
  };

  // 获取订单子节点数据
  const fetchSubOrderNodeOptions = async (orderNode) => {
    try {
      const { data } = await getSubOrderNodeApi(orderNode, {});
      // 存储选项数据（不包含children）
      loanNodeSubCodeOptions.value = data.map((item) => ({
        label: item.desc,
        value: item.code,
      }));
      // 存储完整数据（包含children）
      loanNodeSubCodeData.value = data;
    } catch (error) {
      console.error('获取订单子节点数据失败:', error);
      loanNodeSubCodeOptions.value = [];
      loanNodeSubCodeData.value = [];
    }
  };

  // 根据选中的订单子节点更新节点状态选项
  const updateNodeStatusOptions = (loanNodeSubCode) => {
    if (!loanNodeSubCode) {
      nodeStatusOptions.value = [];
      return;
    }

    // 从存储的完整数据中查找对应的节点状态选项
    const selectedSubNode = loanNodeSubCodeData.value.find((item) => item.code === loanNodeSubCode);

    if (selectedSubNode && selectedSubNode.children) {
      nodeStatusOptions.value = selectedSubNode.children.map((child) => ({
        label: child.desc,
        value: child.code,
      }));
    } else {
      nodeStatusOptions.value = [];
    }
  };
  const schemas = computed<FormSchema[]>(() => {
    return [
      {
        field: 'innerOrderNo',
        component: 'NInput',
        label: '贷款订单ID',
        componentProps: {
          placeholder: '请输入贷款订单ID',
        },
      },
      {
        field: 'loanOrderId',
        component: 'NInput',
        label: '渠道贷款订单ID',
        componentProps: {
          placeholder: '请输入渠道贷款订单ID',
        },
      },
      {
        field: 'realName',
        component: 'NInput',
        label: '客户姓名',
        componentProps: {
          placeholder: '请输入客户姓名',
        },
      },
      {
        field: 'mobileNo',
        component: 'NInput',
        label: '线索手机号',
        componentProps: {
          placeholder: '请输入线索手机号',
          showButton: false,
          maxlength: 11,
          onInput: () => {
            const { mobileNo } = getFieldsValue();
            const formattedValue = mobileNo.replace(/\D/g, '');
            if (mobileNo !== formattedValue) {
              setFieldsValue({ mobileNo: formattedValue });
            }
          },
        },
      },
      {
        field: 'orderMobile',
        component: 'NInput',
        label: '订单手机号',
        componentProps: {
          placeholder: '请输入订单手机号',
          showButton: false,
          maxlength: 11,
          onInput: () => {
            const { orderMobile } = getFieldsValue();
            const formattedValue = orderMobile.replace(/\D/g, '');
            if (orderMobile !== formattedValue) {
              setFieldsValue({ orderMobile: formattedValue });
            }
          },
        },
      },
      {
        field: 'ownerIdList',
        label: '归属人',
        slot: 'blongUserId',
        hidden: route.path == '/dashboard/orderManage',
      },
      {
        field: 'managementCode',
        component: 'NSelect',
        label: '进件资方',
        componentProps: {
          placeholder: '请选择进件资方',
          options: [
            // { label: '易顺', value: '3' },
            { label: '德易', value: '4' },
            // { label: '联众', value: '5' },
          ],
          clearable: true,
          onUpdateValue: async (value) => {
            // 更新各字段的选项
            businessOrderNodeOptions.value = [];
            loanNodeSubCodeOptions.value = [];
            loanNodeSubCodeData.value = [];
            nodeStatusOptions.value = [];
            if (value) {
              await fetchOrderNodeOptions(value);
            }
            // 使用 nextTick 确保 DOM 更新完成后再清空字段
            await nextTick();
            const formModel = getFormModel();
            formModel.businessOrderNode = null;
            formModel.loanNodeSubCode = null;
            formModel.nodeStatus = null;
          },
        },
      },
      {
        field: 'productName',
        component: 'NInput',
        label: '产品信息',
        componentProps: {
          placeholder: '请输入产品信息',
          clearable: true,
        },
      },
      {
        field: 'loanAmount',
        component: 'NInput',
        label: '批复贷款金额',
        componentProps: {
          placeholder: '请输入贷款金额',
        },
      },
      {
        field: 'term',
        component: 'NSelect',
        label: '实际贷款期限',
        componentProps: {
          placeholder: '请选择贷款期限（月）',
          options: termOptions.value,
          clearable: true,
        },
      },
      {
        field: 'status',
        component: 'NSelect',
        label: '订单状态',
        defaultValue: '1',
        componentProps: {
          placeholder: '请选择订单状态',
          options: statusOptions.value,
          clearable: true,
        },
      },
      {
        field: 'businessOrderNode',
        component: 'NSelect',
        label: '订单节点',
        componentProps: {
          placeholder: '请选择订单节点',
          options: businessOrderNodeOptions.value,
          clearable: true,
          onUpdateValue: async (value) => {
            // 当选择订单节点时，获取对应的子节点数据
            loanNodeSubCodeOptions.value = [];
            loanNodeSubCodeData.value = [];
            nodeStatusOptions.value = [];
            // 清空子节点和节点状态字段的值
            await nextTick();
            const formModel = getFormModel();
            formModel.loanNodeSubCode = null;
            formModel.nodeStatus = null;
            if (value) {
              await fetchSubOrderNodeOptions(value);
            }
          },
        },
      },
      {
        field: 'loanNodeSubCode',
        component: 'NSelect',
        label: '订单子节点',
        componentProps: {
          placeholder: '请选择订单子节点',
          options: loanNodeSubCodeOptions.value,
          clearable: true,
          onUpdateValue: async (value) => {
            // 更新节点状态选项
            updateNodeStatusOptions(value);

            // 清空节点状态字段的值
            await nextTick();
            const formModel = getFormModel();
            formModel.nodeStatus = null;
          },
        },
      },
      {
        field: 'nodeStatus',
        component: 'NSelect',
        label: '节点状态',
        componentProps: {
          placeholder: '请选择节点状态',
          options: nodeStatusOptions.value,
          clearable: true,
        },
      },
      {
        field: 'gpsLoadStatus',
        component: 'NSelect',
        label: 'GPS安装节点状态',
        componentProps: {
          placeholder: '请选择GPS安装节点',
          options: [
            { label: '待提交', value: '11' },
            { label: '暂存', value: '16' },
            { label: '下单成功', value: '17' },
            { label: '安装成功', value: '18' },
          ],
          clearable: true,
        },
      },
      {
        field: 'faceSignStatus',
        component: 'NSelect',
        label: '面签节点状态',
        componentProps: {
          placeholder: '请选择面签节点状态',
          options: [
            { label: '未发起', value: '19' },
            // { label: '开始面签', value: '12' },
            { label: '签署中', value: '13' },
            // { label: '处理中', value: '14' },
            { label: '已完成', value: '15' },
            // { label: '签署失败', value: '3' },
          ],
          clearable: true,
        },
      },
      {
        field: 'hypoStatus',
        component: 'NSelect',
        label: '抵押节点状态',
        componentProps: {
          placeholder: '请选择抵押节点状态',
          options: [
            { label: '待提交', value: '11' },
            { label: '审批中', value: '8' },
            { label: '通过', value: '21' },
            { label: '退回', value: '6' },
            { label: '拒绝', value: '7' },
          ],
          clearable: true,
        },
      },
      {
        field: 'orderStartTimeQuery',
        label: '创建订单时间',
        component: 'NDatePicker',
        childKey: ['createTimeStartInt', 'createTimeEndInt'],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: multiDateClearable.value.orderStartTimeQuery,
        },
        noHidden: multiDateNoHidden.value.orderStartTimeQuery,
      },
      {
        field: 'lastFollowTimeQuery',
        label: '最近跟进时间',
        component: 'NDatePicker',
        childKey: ['lastFollowTimeStartInt', 'lastFollowTimeEndInt'],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: multiDateClearable.value.lastFollowTimeQuery,
        },
        noHidden: multiDateNoHidden.value.lastFollowTimeQuery,
      },
      {
        field: 'orderEndTimeQuery',
        label: '订单结束时间',
        component: 'NDatePicker',
        childKey: ['loanOverAtStartInt', 'loanOverAtEndInt'],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: multiDateClearable.value.orderEndTimeQuery,
        },
        noHidden: multiDateNoHidden.value.orderEndTimeQuery,
      },
    ];
  });

  const cancelingOrderNo = ref<string | null>(null);
  async function handleCancelOrder(innerOrderNo: string) {
    try {
      cancelingOrderNo.value = innerOrderNo;
      await cancelOrderApi(innerOrderNo);
      message.success('订单取消成功');
      reloadTable();
    } catch (error: any) {
    } finally {
      cancelingOrderNo.value = null;
    }
  }
  const actionColumn = reactive({
    width: 180,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render(row: ListData) {
      return (
        <n-space>
          <n-button
            v-permission={{ action: 'follow_up' }}
            type="primary"
            text
            onClick={() =>
              openDrawerOrder({ rowData: row }).then(() => {
                reloadTable();
              })
            }
          >
            跟进
          </n-button>
          {row.statusStr === '跟进中' && route.path !== '/dashboard/orderManage' && (
            <n-popconfirm
              negative-text="取消"
              positive-text="确认"
              onPositiveClick={() => handleCancelOrder(row.innerOrderNo)}
            >
              {{
                trigger: () => (
                  <n-button
                    v-permission={{ action: 'cancel_order' }}
                    text
                    loading={cancelingOrderNo.value === row.innerOrderNo}
                  >
                    订单取消
                  </n-button>
                ),
                default: () => '确认取消该订单？',
              }}
            </n-popconfirm>
          )}
        </n-space>
      );
    },
  });
  const tableColumns = ref([...columns]);

  function updateHeaderCounts(counts?: {
    faceSignNum?: number;
    gpsLoadNum?: number;
    dyNum?: number;
  }) {
    const faceSign = counts?.faceSignNum ?? 0;
    const gpsLoad = counts?.gpsLoadNum ?? 0;
    const dy = counts?.dyNum ?? 0;
    const cols = tableColumns.value;
    const faceCol = cols.find((c: any) => c.key === 'faceSignStatusStr');
    const gpsCol = cols.find((c: any) => c.key === 'gpsLoadStatusStr');
    const dyCol = cols.find((c: any) => c.key === 'hypoStatusStr');
    if (faceCol) faceCol.title = `面签节点状态（${faceSign}）`;
    if (gpsCol) gpsCol.title = `GPS安装节点状态（${gpsLoad}）`;
    if (dyCol) dyCol.title = `抵押节点状态（${dy}）`;
  }
  const [register, { getFieldsValue, getFormModel, getActiveColumns, setFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 130,
    schemas,
  });

  // 使用组合式函数创建 clearable 状态
  const { multiDateClearable, multiDateNoHidden } = useDateOptions(getFormModel, getActiveColumns);
  const userInfo: UserInfoType = userStore.getUserInfo || {};
  const loadDataTable = async (params) => {
    const requestParams = { ...getFieldsValue(), ...params, managementCode: 4 };
    // 判断当前路由是否为 dashboard/orderManage，如果是则添加 ownerId
    if (route.path === '/dashboard/orderManage' && userInfo.id) {
      requestParams.ownerId = userInfo.id;
    }
    const [listRes, countRes] = await Promise.all([
      getOrderListApi(requestParams),
      getOrderDataCountApi(requestParams).catch(() => null),
    ]);
    try {
      updateHeaderCounts(countRes?.data);
    } catch (e) {
      // ignore
    }
    return listRes.data;
  };
  function reloadTable() {
    actionRef.value!.reload();
  }
  function resetTableForm() {
    reloadTable();
    businessOrderNodeOptions.value = [];
    loanNodeSubCodeOptions.value = [];
    loanNodeSubCodeData.value = [];
    nodeStatusOptions.value = [];
    const formModel = getFormModel();
    formModel.businessOrderNode = null;
    formModel.loanNodeSubCode = null;
    formModel.nodeStatus = null;
  }
  function handleExport() {
    try {
      exportLoading.value = true;
      const exportParams: any = {
        ...getFieldsValue(),
        pageNumber: 1,
        pageSize: 10000,
        token: userStore.getToken,
        managementCode: 4,
      };
      // 判断当前路由是否为 dashboard/orderManage，如果是则添加 ownerId
      if (route.path === '/dashboard/orderManage' && userInfo.id) {
        exportParams.ownerId = userInfo.id;
      }
      window.open(exportOrderListApi(exportParams));
    } finally {
      exportLoading.value = false;
    }
  }

  // 获取归属人列表
  async function getUserList() {
    try {
      const { data } = await getUserListApi();
      userList.value = data;
    } catch (err) {
      console.log(err);
    }
  }

  // 根据缓存恢复的值重新加载动态选项
  const restoreDynamicOptions = async () => {
    const formModel = getFormModel();
    const { managementCode, businessOrderNode, loanNodeSubCode } = formModel;

    // 1. 如果有进件资方，加载订单节点选项
    if (managementCode) {
      await fetchOrderNodeOptions(managementCode);
      await nextTick();

      // 2. 如果有订单节点，加载订单子节点选项
      if (businessOrderNode) {
        await fetchSubOrderNodeOptions(businessOrderNode);
        await nextTick();

        // 3. 如果有订单子节点，更新节点状态选项
        if (loanNodeSubCode) {
          updateNodeStatusOptions(loanNodeSubCode);
        }
      }
    }
  };

  onMounted(async () => {
    await nextTick();
    // 获取归属人列表
    getUserList();
    // 恢复动态选项
    const formModel = getFormModel();
    formModel.status = '1';
    await restoreDynamicOptions();
    onActivated(() => {
      reloadTable();
    });
  });
</script>

<style lang="less" scoped>
  :deep(.n-tabs) {
    .n-tab-pane {
      padding: 0 !important;
    }
  }
</style>
