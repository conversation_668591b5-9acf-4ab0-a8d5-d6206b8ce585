<template>
  <n-space vertical>
    <n-card>
      <n-collapse arrow-placement="right" :expanded-names="expandedNames">
        <n-collapse-item name="6-1">
          <template #header>
            <Title ref="nodeRef" :mainNode="6" :subNode="1" @click="toggleExpanded('6-1')" />
          </template>
          <n-space vertical>
            <div v-if="!isCurrentStagePending('6-1')">
              <SubTitle title="放款结果" />
              <n-grid :cols="3">
                <n-grid-item span="2">
                  <n-descriptions
                    label-style="width: 120px"
                    label-placement="top"
                    :column="3"
                    bordered
                  >
                    <n-descriptions-item label="放款结果">
                      <n-tag
                        v-if="formData.loanPhaseResult && LoanStatusMap[formData.loanPhaseResult]"
                        size="small"
                        :type="
                          String(formData.loanPhaseResult) === String(LoanStatus.Pass)
                            ? 'success'
                            : 'default'
                        "
                      >
                        {{ LoanStatusMap[formData.loanPhaseResult] }}
                      </n-tag>
                      <span v-else>-</span>
                    </n-descriptions-item>
                    <n-descriptions-item label="审批备注">
                      {{ formData.loanPhaseRemark || '-' }}
                    </n-descriptions-item>
                    <n-descriptions-item label="操作">
                      <n-button
                        size="small"
                        type="primary"
                        :loading="searchLoading"
                        :disabled="
                          !!formData.loanPhaseResult &&
                          [
                            String(LoanStatus.Pending),
                            String(LoanStatus.Pass),
                            String(LoanStatus.Refuse),
                            String(LoanStatus.Back),
                          ].includes(formData.loanPhaseResult)
                        "
                        v-cooldown
                        @click="getLoanStatus"
                      >
                        状态查询
                      </n-button>
                    </n-descriptions-item>
                  </n-descriptions>
                </n-grid-item>
              </n-grid>
            </div>
            <div class="mt-6" v-if="formData.loanPhaseResult === String(LoanStatus.Pass)">
              <n-space class="mb-2">
                <SubTitle title="还款计划" />
                <n-button
                  size="small"
                  type="primary"
                  :loading="searchLoading2"
                  @click="getPaymentPlan"
                >
                  计划查询
                </n-button>
              </n-space>
              <n-data-table :scroll-x="0" :columns="columns1" :data="data1" :max-height="400" />
            </div>
          </n-space>
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import { computed, onMounted, ref, watch, inject, type Ref } from 'vue';
  import { getRepaymentSchedule, sendLoanStatus, getFieldData } from '@/api/dashboard/deyi';
  import { repaymentStatus, examineRemark } from '@/components/DrawerOrder/types';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import { LoanStatusMap, LoanStatus } from '@/components/DrawerOrder/enum';
  import { useFormDisabled } from '@/components/DrawerOrder/composables/useFormDisabled';

  const orderDrawerStore = useOrderDrawerStore();
  const searchLoading = ref(false);
  const props = defineProps({
    innerOrderNo: {
      type: String,
      required: true,
    },
  });
  const expandedNames = inject<Ref<string[]>>('expandedNames')!;
  const toggleExpanded = inject('toggleExpanded') as (name: string) => void;
  const { isCurrentStagePending, isCurrentStageCompleted } = useFormDisabled();
  const formData = ref({
    loanPhaseResult: '',
    loanPhaseRemark: '',
  });
  const columns1 = [
    {
      title: '还款状态',
      key: 'status',
      align: 'center',
      width: 150,
      render(row) {
        return repaymentStatus?.[row.status] || '--';
      },
    },
    {
      title: '结算状态',
      key: 'balanceType',
      align: 'center',
      width: 150,
      render(row) {
        return examineRemark?.[row.balanceType] || '--';
      },
    },
    {
      title: '期数',
      key: 'termNo',
      align: 'center',
      width: 150,
    },
    {
      title: '应还款日',
      key: 'paymentDate',
      align: 'center',
      width: 150,
    },
    {
      title: '应还金额-月供',
      key: 'repayment',
      align: 'center',
      width: 150,
    },
    {
      title: '应还本金',
      key: 'principal',
      align: 'center',
      width: 150,
    },
    {
      title: '应还利息',
      key: 'interest',
      align: 'center',
      width: 150,
    },
    {
      title: '应还罚息',
      key: 'fine',
      align: 'center',
      width: 150,
    },
    {
      title: '其他费用',
      key: 'feeOth',
      width: 150,
      align: 'center',
    },
    {
      title: '总应还金额',
      key: 'totalRepayment',
      width: 150,
      align: 'center',
    },
    {
      title: '实还金额',
      key: 'factRepayment',
      width: 150,
      align: 'center',
    },
  ];
  const data1 = ref([]);
  const nodeRef = ref({ status: '' });
  const searchLoading1 = ref(false);

  const searchLoading2 = ref(false);

  const isLoanDisbursementCompleted = computed(() => isCurrentStageCompleted('6-1'));

  watch(
    [() => expandedNames.value, isLoanDisbursementCompleted],
    ([names, completed]) => {
      if (!Array.isArray(names) || !completed) {
        return;
      }
      if (!names.includes('6-1')) {
        expandedNames.value = [...names, '6-1'];
      }
    },
    { immediate: true }
  );

  onMounted(async () => {
    orderDrawerStore.triggerRefresh();
    getData();
  });
  watch(
    () => formData.value,
    (val) => {
      if (val.loanPhaseResult === String(LoanStatus.Pass)) {
        getPaymentPlan();
      }
    },
    {
      immediate: true,
    }
  );
  async function getPaymentPlan() {
    searchLoading2.value = true;
    await getRepaymentSchedule({ innerOrderNo: props.innerOrderNo }).catch(() => []);
    return getData().finally(() => {
      searchLoading2.value = false;
    });
  }
  async function getLoanStatus() {
    searchLoading1.value = true;
    await sendLoanStatus({ innerOrderNo: props.innerOrderNo }).catch(() => []);
    return getData().finally(() => {
      searchLoading1.value = false;
    });
  }
  async function getData() {
    let { data } = await getFieldData({
      innerOrderNo: props.innerOrderNo,
      fieldName: ['loanPhaseResult', 'loanPhaseRemark', 'loanPhasePlan'],
    });
    if (data && data.length > 0) {
      data.forEach((item) => {
        if (item.fieldName in formData.value) {
          formData.value[item.fieldName] = item.fieldValue;
        }
        if (item.fieldName === 'loanPhasePlan') {
          data1.value = item.fieldValue ? JSON.parse(item.fieldValue) : [];
        }
      });
    }
    return data;
  }
</script>

<style lang="less" scoped></style>
