# 只在开发模式中被载入
VITE_PORT = 8001

# 网站根目录
VITE_PUBLIC_PATH = /

# 是否开启 mock
VITE_USE_MOCK = false

# 是否开启控制台打印 mock 请求信息
VITE_LOGGER_MOCK = false

# 是否删除console
VITE_DROP_CONSOLE = true

# 跨域代理，可以配置多个，请注意不要换行
# VITE_PROXY = [["/appApi","http://localhost:8001"],["/upload","http://localhost:8001/upload"]]
# 陈杰的配置
# VITE_PROXY = [["/api","http://**************:18082"]]
# 王偲翰
# VITE_PROXY = [["/api","http://*************:18082"]]
# 龙太平
# VITE_PROXY = [["/api","http://*************:18082"]]
# 廖欣
# VITE_PROXY = [["/api","http://**************:18082"]]
# 陈启渝
#  VITE_PROXY = [["/api","http://**************:18082"]]
# 测试环境
VITE_PROXY = [["/api","https://tcrm-api.yyrhz.cn"]]

# API 接口地址
VITE_GLOB_API_URL = /api

# 文件上传地址
VITE_GLOB_UPLOAD_URL=

# 文件前缀地址
VITE_GLOB_FILE_URL=
