import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
import { computed, ref, toRef, watch } from 'vue';

export function useExpandedNames() {
  const orderDrawerStore = useOrderDrawerStore();
  const currentStage = toRef(orderDrawerStore, 'currentStage');
  const subStages = computed(() => currentStage.value?.subStages || []);

  const expandedNames = ref<string[]>([]);

  watch(
    () => subStages.value,
    () => {
      expandedNames.value = subStages.value
        .filter((subStage) => subStage.status === 'ongoing')
        .map((subStage) => subStage.id as string);
    },
    { immediate: true }
  );

  const toggleExpanded = (id: string) => {
    if (expandedNames.value.includes(id)) {
      expandedNames.value = expandedNames.value.filter((name) => name !== id);
    } else {
      expandedNames.value.push(id);
    }
  };

  return {
    expandedNames,
    toggleExpanded,
  };
}
