<template>
  <div>
    <n-drawer
      v-model:show="active"
      placement="right"
      :default-width="1000"
      :min-width="800"
      resizable
      :mask-closable="false"
    >
      <n-drawer-content :native-scrollbar="false" closable>
        <Header
          v-loading="isSubmitLoading"
          :stages-data="stagesList"
          :basic-info="basicInfo"
          :current-stage="currentStage"
          @stage-click="handleStageClick"
        />
        <template v-if="currentStage">
          <PreTrial v-if="currentStage.id === 1" :inner-order-no="innerOrderNo" />
          <ApplicationContract v-else-if="currentStage.id === 2" :inner-order-no="innerOrderNo" />
          <FaceToFaceSigning v-else-if="currentStage.id === 3" :inner-order-no="innerOrderNo" />
          <GPSInstallation
            v-else-if="currentStage.id === 4"
            :inner-order-no="innerOrderNo"
            :rowData="rowData"
          />
          <Mortgage v-else-if="currentStage.id === 5" :inner-order-no="innerOrderNo" />
          <LoanDisbursement v-else-if="currentStage.id === 6" :inner-order-no="innerOrderNo" />
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
  import type { DrawerOrderEmits } from './types';
  import Header from '@/components/DrawerOrder/components/Header/index.vue';
  import PreTrial from '@/components/DrawerOrder/components/PreTrial/index.vue'; // 预审阶段
  import ApplicationContract from '@/components/DrawerOrder/components/ApplicationContract/index.vue'; // 进件签约
  import FaceToFaceSigning from '@/components/DrawerOrder/components/FaceToFaceSigning/index.vue'; // 面签阶段
  import GPSInstallation from '@/components/DrawerOrder/components/GPSInstallation/index.vue'; // GPS安装
  import Mortgage from '@/components/DrawerOrder/components/Mortgage/index.vue'; // 抵押阶段
  import LoanDisbursement from '@/components/DrawerOrder/components/LoanDisbursement/index.vue'; // 放款阶段
  import { computed, onMounted, onUnmounted, provide, ref, watch } from 'vue';
  import type { PropType } from 'vue';
  import { storeToRefs } from 'pinia';
  import type { Stage } from './types';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import type { ListData } from '@/views/dashboard/workplace/orderManage/columns';
  import { useExpandedNames } from '@/components/DrawerOrder/composables/useExpandedNames';

  const props = defineProps({
    rowData: {
      type: Object as PropType<ListData>,
      default: undefined,
    },
  });
  const emit = defineEmits<DrawerOrderEmits>();

  const { expandedNames, toggleExpanded } = useExpandedNames();

  provide('expandedNames', expandedNames);
  provide('toggleExpanded', toggleExpanded);
  provide('rowData', props.rowData);

  // 提供关闭抽屉的函数
  const closeDrawer = () => {
    active.value = false;
  };
  provide('closeDrawer', closeDrawer);

  // 从 props.innerOrderNo 或 rowData.innerOrderNo 中获取 innerOrderNo
  const innerOrderNo = computed(() => props.rowData?.innerOrderNo || '');

  const active = ref(false);
  // 使用 store 获取订单数据相关状态和方法
  const orderDrawerStore = useOrderDrawerStore();
  // 使用 storeToRefs 保持响应式
  const { stagesList, basicInfo, isSubmitLoading, currentStage } = storeToRefs(orderDrawerStore);

  watch(
    () => active.value,
    (val) => {
      if (!val) {
        orderDrawerStore.setCurrentStage(null);
        setTimeout(() => {
          emit('close');
        }, 300);
      }
    }
  );

  // 监听刷新信号，触发重新加载
  watch(
    () => orderDrawerStore.refreshSignal,
    () => {
      orderDrawerStore.initOrderData(innerOrderNo.value);
    }
  );

  // 监听 innerOrderNo 变化，确保 props 传递后立即初始化
  watch(
    () => innerOrderNo.value,
    (newVal) => {
      if (newVal) {
        orderDrawerStore.initOrderData(newVal);
      }
    },
    { immediate: true }
  );
  onMounted(async () => {
    active.value = true;
    // emitter.on(EventNames.HANDLE_DRAWER_ORDER_POP, (val: boolean) => {
    //   active.value = val;
    // });
  });
  onUnmounted(() => {
    // emitter.off(EventNames.HANDLE_DRAWER_ORDER_POP);
  });
  // 处理阶段点击事件
  const handleStageClick = (stage: Stage) => {
    orderDrawerStore.setCurrentStage(stage);
  };
</script>

<style lang="less" scoped></style>
