<template>
  <n-card>
    <!-- 资方进件 -->
    <n-collapse arrow-placement="right" :expanded-names="expandedNames">
      <n-collapse-item name="2-3">
        <template #header>
          <Title :mainNode="2" :subNode="3" @click="toggleExpanded('2-3')" />
        </template>

        <div>
          <template v-if="isCurrentStageOngoing('2-3') || initialData.funderApContractFunder1">
            <SubTitle title="资方进件" desc="前置要求：机构申请结果通过，跳过该签署阶段" />

            <!-- 审批结果改为 n-descriptions -->
            <n-descriptions label-placement="top" bordered :column="4" class="mb-4">
              <n-descriptions-item label="签约资方">
                {{ initialData.funderApContractFunder1 || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="审批结果">
                <n-tag
                  v-if="initialData.funderApApprovalResult"
                  size="small"
                  :type="
                    String(initialData.funderApApprovalResult) === String(SignStatus.Pass)
                      ? 'success'
                      : 'warning'
                  "
                >
                  {{ SignStatusMap[initialData.funderApApprovalResult] }}
                </n-tag>
                <template v-else> - </template>
              </n-descriptions-item>
              <n-descriptions-item label="审批备注">
                {{ initialData.funderApApprovalRemarks || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="操作">
                <n-button
                  size="small"
                  type="primary"
                  v-cooldown
                  :disabled="
                    !isCurrentStageOngoing('2-3') ||
                    String(initialData.funderApApprovalResult) === String(SignStatus.Pass)
                  "
                  @click="onQueryFunderOrderStatus"
                >
                  状态查询
                </n-button>
              </n-descriptions-item>
            </n-descriptions>
          </template>

          <!-- 资方授权书签署改为 n-descriptions -->
          <n-descriptions
            v-if="initialData.funderApContractFunder2"
            label-placement="top"
            bordered
            :column="4"
          >
            <n-descriptions-item label="签约资方">
              {{ initialData.funderApContractFunder2 || '-' }}
            </n-descriptions-item>
            <n-descriptions-item label="资方授权书签署地址">
              <n-space
                v-if="
                  (String(initialData.funderApSignStatus) === String(AuthSignStatus.UnSign) &&
                    (!initialData.funderApSignTime ||
                      (initialData.funderApSignTime &&
                        dayjs(initialData.funderApSignTime).isAfter(
                          dayjs(initialData.serverTime).subtract(1, 'day')
                        )))) ||
                  String(initialData.funderApSignStatus) === String(AuthSignStatus.SignFinish)
                "
                align="center"
                justify="center"
              >
                <n-text type="primary">
                  <!-- {{ initialData.funderApAuthSignAddress || '-' }} -->
                  <n-qr-code
                    class="p-0"
                    v-if="initialData.funderApAuthSignAddress"
                    id="funderApAuthSignAddress_qrcode"
                    :value="initialData.funderApAuthSignAddress"
                  />
                </n-text>
                <n-button
                  v-if="initialData.funderApAuthSignAddress"
                  size="small"
                  type="primary"
                  @click="copyQrcode"
                >
                  <n-icon>
                    <CopyOutlined />
                  </n-icon>
                  复制二维码
                </n-button>
              </n-space>
              <n-space v-else align="center" justify="center">-</n-space>
            </n-descriptions-item>
            <n-descriptions-item label="签署状态">
              <n-tag
                v-if="initialData.funderApSignStatus"
                size="small"
                :type="
                  String(initialData.funderApSignStatus) === String(AuthSignStatus.SignFinish)
                    ? 'success'
                    : 'warning'
                "
              >
                {{ AuthSignStatusMap[initialData.funderApSignStatus] }}
              </n-tag>
              <template v-else> - </template>
            </n-descriptions-item>
            <n-descriptions-item label="操作">
              <n-button
                v-if="
                  String(initialData.funderApSignStatus) === String(AuthSignStatus.UnSign) &&
                  initialData.funderApSignTime &&
                  dayjs(initialData.funderApSignTime).isBefore(
                    dayjs(initialData.serverTime).subtract(1, 'day')
                  )
                "
                size="small"
                type="primary"
                @click="onReCreateFunderSignUrl"
              >
                重新发起
              </n-button>
              <n-button
                v-else
                size="small"
                type="primary"
                v-cooldown
                :disabled="
                  String(initialData.funderApSignStatus) === String(AuthSignStatus.SignFinish)
                "
                @click="onQueryFunderSignStatus"
              >
                状态查询
              </n-button>
            </n-descriptions-item>
          </n-descriptions>
        </div>
      </n-collapse-item>
    </n-collapse>
  </n-card>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import { CopyOutlined } from '@vicons/antd';

  import { onMounted, inject } from 'vue';
  import { useBaseData } from '../../composables/useBaseData';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import { useFormDisabled } from '@/components/DrawerOrder/composables/useFormDisabled';
  import { copyCanvasToClipboard } from '@/utils/copyCanvasToClipboard';

  const { isCurrentStageOngoing } = useFormDisabled();

  import {
    getFunderOrderStatus,
    getFunderSignStatus,
    getFunderSignUrl,
  } from '@/api/dashboard/deyi';
  import {
    AuthSignStatus,
    AuthSignStatusMap,
    SignStatus,
    SignStatusMap,
  } from '@/components/DrawerOrder/enum';
  import dayjs from 'dayjs';

  interface Props {
    innerOrderNo: string;
  }
  const props = defineProps<Props>();
  const expandedNames = inject('expandedNames');
  const toggleExpanded = inject('toggleExpanded') as (name: string) => void;
  const orderDrawerStore = useOrderDrawerStore();

  // 拉取数据（直接用 initialData 展示）
  const {
    data: initialData,
    getData,
    getOptions,
  } = useBaseData({
    innerOrderNo: props.innerOrderNo,
    keys: [
      'funderApContractFunder1',
      'funderApApprovalResult',
      'funderApApprovalRemarks',
      'funderApContractFunder2',
      'funderApAuthSignAddress',
      'funderApSignStatus',
      'funderApSignTime',
    ],
  });

  // 查询操作
  const onQueryFunderOrderStatus = async () => {
    await getFunderOrderStatus({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };
  const onQueryFunderSignStatus = async () => {
    await getFunderSignStatus({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };
  const onReCreateFunderSignUrl = async () => {
    await getFunderSignUrl({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };
  //复制签约二维码
  async function copyQrcode() {
    const canvas = document
      .getElementById('funderApAuthSignAddress_qrcode')
      ?.querySelector('canvas');
    canvas && (await copyCanvasToClipboard(canvas));
    window.$message.success('复制成功');
  }
  onMounted(() => {
    getData();
    getOptions();
  });
</script>

<style lang="less" scoped></style>
