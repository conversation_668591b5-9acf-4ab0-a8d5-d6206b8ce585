<template>
  <n-space vertical>
    <!-- GPS安装要求 -->
    <n-card>
      <n-collapse arrow-placement="right" :expanded-names="expandedNames">
        <n-collapse-item
          :name="`${NodeCodeMap.gpsInstallation.mainNode}-${NodeCodeMap.gpsInstallation.subNode.gpsInstallationRequirement}`"
        >
          <template #header>
            <Title
              :mainNode="NodeCodeMap.gpsInstallation.mainNode"
              :subNode="NodeCodeMap.gpsInstallation.subNode.gpsInstallationRequirement"
              @click="
                toggleExpanded(
                  `${NodeCodeMap.gpsInstallation.mainNode}-${NodeCodeMap.gpsInstallation.subNode.gpsInstallationRequirement}`
                )
              "
            />
          </template>

          <n-space class="mt-[10px]" :wrap="false">
            <n-descriptions
              label-style="width: 120px"
              class="w-[240px] flex-shrink-0"
              label-placement="left"
              :column="1"
              bordered
            >
              <n-descriptions-item label="有线GPS数量">
                {{ formModel.gpsProcWiredCount || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="无线GPS数量">
                {{ formModel.gpsProcWirelessCount || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="水箱GPS数量">
                {{ formModel.gpsProcWaterCount || '-' }}
              </n-descriptions-item>
            </n-descriptions>
            <n-data-table
              style="flex-grow: 1"
              max-height="200px"
              :columns="gpsProviderColumns"
              :data="gpsProviderData"
            />
          </n-space>
        </n-collapse-item>
      </n-collapse>
    </n-card>

    <!-- GPS安装 -->
    <n-card>
      <n-collapse arrow-placement="right" :expanded-names="expandedNames">
        <n-collapse-item
          :name="`${NodeCodeMap.gpsInstallation.mainNode}-${NodeCodeMap.gpsInstallation.subNode.gpsInstallation}`"
        >
          <template #header>
            <Title
              ref="nodeRef2"
              :mainNode="NodeCodeMap.gpsInstallation.mainNode"
              :subNode="NodeCodeMap.gpsInstallation.subNode.gpsInstallation"
              @click="
                toggleExpanded(
                  `${NodeCodeMap.gpsInstallation.mainNode}-${NodeCodeMap.gpsInstallation.subNode.gpsInstallation}`
                )
              "
            />
          </template>

          <div>
            <!-- 安装区域地址 + 联系方式 + 提交 -->
            <n-form
              ref="formRef"
              label-placement="left"
              size="medium"
              :model="formModel"
              :rules="currentRules"
              label-width="140px"
              :disabled="formDisabled2"
            >
              <n-grid :cols="2">
                <n-grid-item span="2">
                  <n-form-item label="安装区域地址" path="gpsProcInstallDetail" required>
                    <AddressInput v-model="installAddressModel" :disabled="formDisabled2" />
                    <SyncAddressButton
                      :innerOrderNo="innerOrderNo"
                      @address="installAddressModel = $event"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <n-grid :cols="2">
                <n-grid-item span="1">
                  <n-form-item
                    label="供应商选择"
                    path="nowGpsProvider"
                    required
                    :disabled="formModel.gpsProcPlan !== String(gpsInstallationStatus.Pending)"
                  >
                    <n-select
                      v-model:value="formModel.nowGpsProvider"
                      :options="gpsProviderOptions"
                      placeholder="请选择供应商"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>

              <n-flex justify="center">
                <n-button
                  :type="canSubmit ? 'primary' : 'default'"
                  :disabled="formDisabled2"
                  :loading="formLoading2"
                  @click="canSubmit ? handleSubmit() : handleSave()"
                >
                  {{ formDisabled2 ? '已提交' : canSubmit ? '提交' : '保存' }}
                </n-button>
              </n-flex>
            </n-form>
            <n-divider dashed />
            <template v-if="!isEmptyVal(formModel.gpsProcPlan)">
              <!-- 安装要求概览 -->
              <n-data-table class="my-2" :columns="overviewColumns" :data="overviewData" />

              <!-- GPS安装进度 -->
              <SubTitle title="GPS安装" />
              <n-grid :cols="2">
                <n-grid-item span="1">
                  <n-descriptions
                    label-style="width: 120px"
                    label-placement="top"
                    :column="2"
                    bordered
                  >
                    <n-descriptions-item label="安装进度查询">
                      <n-tag
                        size="small"
                        :type="
                          formModel.gpsProcPlan === String(gpsInstallationStatus.Completed)
                            ? 'success'
                            : 'default'
                        "
                      >
                        {{
                          formModel.gpsProcPlan
                            ? gpsInstallationStatusMap?.[formModel.gpsProcPlan] ?? '-'
                            : '-'
                        }}
                      </n-tag>
                    </n-descriptions-item>
                    <n-descriptions-item label="操作">
                      <n-button
                        size="small"
                        type="primary"
                        :loading="searchLoading"
                        :disabled="
                          formModel.gpsProcPlan === String(gpsInstallationStatus.Completed)
                        "
                        v-cooldown
                        @click="handleSearch"
                      >
                        状态查询
                      </n-button>
                    </n-descriptions-item>
                  </n-descriptions>
                </n-grid-item>
              </n-grid>
            </template>
          </div>
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import AddressInput from '@/components/AddressInput/index.vue';
  import SyncAddressButton from '@/components/DrawerOrder/components/ApplicationContract/components/SyncAddressButton/index.vue';

  import { ref, reactive, computed, onMounted, watch, inject, h } from 'vue';
  import type { FormInst } from 'naive-ui';
  import { useBaseData } from './composables/useBaseData';
  import type { Fields } from '@/components/DrawerOrder/types';
  import { NodeCodeMap } from '@/components/DrawerOrder/types';
  import {
    gpsInstallationStatusMap,
    gpsInstallationStatus,
    GpsTypeMap,
    GpsInstallWayMap,
  } from '@/components/DrawerOrder/enum';
  import {
    saveFieldData,
    submitForm,
    sendGPSInstallStatus,
    getGPSPaymentStatus,
  } from '@/api/dashboard/deyi';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import { promisifyDialog } from '@/utils/nativeUtils';
  import { useRouter, useRoute } from 'vue-router';
  import { EventNames, bufferedEmitter } from '@/utils/eventBus';
  const closeDrawer = inject('closeDrawer') as () => void;

  const router = useRouter();
  const route = useRoute();
  interface Props {
    innerOrderNo: string;
    rowData: any;
  }
  const props = defineProps<Props>();
  const expandedNames = inject('expandedNames');
  const toggleExpanded = inject('toggleExpanded') as (name: string) => void;

  const formRef = ref<FormInst | null>(null);
  const orderDrawerStore = useOrderDrawerStore();
  const nodeRef2 = ref({ status: '' });
  const nodeStatus2 = computed(() => nodeRef2.value?.status);
  const formDisabled2 = computed(
    () => !!nodeStatus2.value && !['pending', 'ongoing', 'returned'].includes(nodeStatus2.value)
  );
  const formLoading2 = ref(false);
  // 表单模型（GPS安装）
  const formModel = reactive<Partial<Fields>>({
    gpsProcInstallProvince: '',
    gpsProcInstallCity: '',
    gpsProcInstallDistrict: '',
    gpsProcInstallDetail: '',
    gpsProcInstallRequirement: '', // json
    gpsProcWiredCount: '',
    gpsProcWirelessCount: '',
    gpsProcWaterCount: '',
    gpsProcPlan: '',
    gpsProvider: '',
    nowGpsProvider: '', //gps供应商选择
  });

  // useBaseData 拉取数据与选项
  const {
    data: initialData,
    options: fieldOptions,
    getData,
    getOptions,
  } = useBaseData({
    innerOrderNo: props.innerOrderNo,
    keys: [
      // 表单 + 表格
      'gpsProcInstallProvince',
      'gpsProcInstallCity',
      'gpsProcInstallDistrict',
      'gpsProcInstallDetail',
      'gpsProcInstallRequirement',
      'gpsProcWiredCount',
      'gpsProcWirelessCount',
      'gpsProcWaterCount',
      'gpsProcPlan',
      'gpsProvider',
      'nowGpsProvider',
    ],
  });

  // AddressInput 适配
  const installAddressModel = computed({
    get: () => ({
      province: formModel.gpsProcInstallProvince || '',
      city: formModel.gpsProcInstallCity || '',
      district: formModel.gpsProcInstallDistrict || '',
      detail: formModel.gpsProcInstallDetail || '',
    }),
    set: (val: any) => {
      formModel.gpsProcInstallProvince = val?.province || '';
      formModel.gpsProcInstallCity = val?.city || '';
      formModel.gpsProcInstallDistrict = val?.district || '';
      formModel.gpsProcInstallDetail = val?.detail || '';
    },
  });

  // GPS安装要求
  const overviewColumns = [
    { title: 'GPS序号', key: 'key1', align: 'center', render: (row, rowIndex) => rowIndex + 1 },
    {
      title: 'GPS类型',
      key: 'type',
      align: 'center',
      render(row) {
        return h('span', {}, GpsTypeMap?.[row.type] ?? '-');
      },
    },
    {
      title: 'GPS编号',
      key: 'gpsNo',
      align: 'center',
    },
    {
      title: '安装方式',
      key: 'installWay',
      align: 'center',
      render(row) {
        return h('span', {}, GpsInstallWayMap?.[row.installWay] ?? '-');
      },
    },
    {
      title: 'GPS供应商',
      key: 'key5',
      align: 'center',
      render: (row) => formModel.nowGpsProvider ?? '-',
    },
  ];
  const gpsProviderColumns = [
    {
      title: '供应商列表',
      key: 'index',
      align: 'center',
      render: (row, rowIndex) => rowIndex + 1,
    },
    {
      title: '供应商名称',
      key: 'gpsProvider',
      align: 'center',
      width: 200,
    },
  ];
  const gpsProviders = computed(() => {
    return !isEmptyVal(formModel.gpsProvider) ? formModel.gpsProvider?.split(',') ?? [] : [];
  });
  const gpsProviderData = computed(() => {
    return (
      gpsProviders.value?.map((item, index) => ({
        gpsProvider: item,
        index,
      })) ?? []
    );
  });
  const gpsProviderOptions = computed(() => {
    return (
      gpsProviders.value?.map((item) => ({
        label: item,
        value: item,
      })) ?? []
    );
  });
  const overviewData = computed<any[]>(() => {
    const data = JSON.parse(formModel.gpsProcInstallRequirement || '[]');
    return data;
  });
  const searchLoading = ref(false);
  // 数据回填
  watch(
    () => initialData,
    (val) => {
      if (!val) return;
      // 表单
      Object.keys(formModel).forEach((k) => {
        formModel[k] = (val as any)[k] ?? formModel[k];
      });
    },
    { immediate: true, deep: true }
  );

  // 校验与按钮切换
  const baseFormRules = {
    gpsProcInstallDetail: [
      {
        required: true,
        validator(_r, v) {
          const { province, city, district, detail } = installAddressModel.value || {};
          if (!province) return new Error('请选择省份');
          if (!city) return new Error('请选择城市');
          if (!district) return new Error('请选择区县');
          if (!detail || String(detail).trim() === '') return new Error('请填写详细地址');

          return true;
        },
        trigger: 'change',
      },
    ],
    nowGpsProvider: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  } as Record<string, any[]>;

  const requiredFields = Object.keys(baseFormRules);

  const isEmptyVal = (v: any) =>
    v === undefined || v === null || (typeof v === 'string' && v.trim() === '');

  const canSubmit = computed(() => {
    /**
     * 只有进行中||退回 才能提交
     */
    if (!['ongoing', 'returned'].includes(nodeStatus2.value)) {
      return false;
    }
    for (const k of requiredFields) {
      // @ts-ignore
      if (isEmptyVal(formModel[k])) return false;
    }

    return true;
  });

  const currentRules = computed(() => {
    if (canSubmit.value) return baseFormRules;
    const rules: any = {};
    Object.keys(baseFormRules).forEach(
      (k) => (rules[k] = (baseFormRules[k] as any[]).filter((r) => !r.required))
    );
    return rules;
  });

  const handleSave = async () => {
    try {
      await formRef.value?.validate();
    } catch {
      window.$message?.error('填写内容格式不正确');
      return;
    }
    try {
      const innerOrderNo = props.innerOrderNo;
      if (!innerOrderNo) return window.$message?.error('缺少订单编号');
      await saveFieldData({ innerOrderNo, fieldData: formModel });
      window.$message?.success('保存成功');
    } catch (e) {
      window.$message?.error('保存失败');
      console.error(e);
    }
  };

  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();
    } catch {
      return;
    }
    try {
      const innerOrderNo = props.innerOrderNo;
      if (!innerOrderNo) return window.$message?.error('缺少订单编号');

      formLoading2.value = true;

      let res = await getGPSPaymentStatus({ clueId: props.rowData.clueId });
      if (!res.data) {
        formLoading2.value = false;
        return gpsGoPay();
      }
      await saveFieldData({ innerOrderNo, fieldData: formModel });
      await submitForm({
        innerOrderNo,
        mainNodeCode: NodeCodeMap.gpsInstallation.mainNode,
        subNodeCode: NodeCodeMap.gpsInstallation.subNode.gpsInstallation,
      });
      handleSearch();
      window.$message?.success('提交成功');
    } catch (e) {
      console.error(e);
    } finally {
      formLoading2.value = false;
    }
  };
  function gpsGoPay() {
    promisifyDialog(window.$dialog.warning)({
      title: '提示',
      content: '当前订单未支付，请确认客户是否完成支付！',
      positiveText: '确定',
      negativeText: '取消',
      maskClosable: false,
      onPositiveClick: async () => {
        try {
          const eventData = {
            tab: 'loanRecord',
            clueId: props.rowData.clueId,
          };

          // 如果当前已经在目标页面，直接发出事件，事件会被已注册的监听器处理
          if (route.path === '/client/my-clients') {
            bufferedEmitter.emit(EventNames.JUMP_MY_CLIENTS_DETAIL_TAB, eventData);
            closeDrawer();
          } else {
            // 如果不在目标页面，先发出事件（会被缓冲），然后跳转
            bufferedEmitter.emit(EventNames.JUMP_MY_CLIENTS_DETAIL_TAB, eventData);
            closeDrawer();
            router.push({ path: '/client/my-clients' });
          }
          // emitter.emit(EventNames.HANDLE_DRAWER_ORDER_POP, false);
        } catch (error) {
          return false;
        }
      },
    });
  }
  onMounted(() => {
    getData();
    getOptions();
  });
  async function handleSearch() {
    searchLoading.value = true;
    sendGPSInstallStatus({ innerOrderNo: props.innerOrderNo })
      .then(() => {
        getData();
        orderDrawerStore.triggerRefresh();
      })
      .finally(() => {
        searchLoading.value = false;
      });
  }
</script>

<style lang="less" scoped></style>
