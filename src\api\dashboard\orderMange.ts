import { get, post } from '@/utils/lib/axios.package';
import qs from 'qs';

/**
 * @description: 查询订单列表
 */
export function getOrderListApi(params) {
  return get('/cms/loan-order-record/query/orderList', params);
}

/**
 * @description: 订单列表导出
 */
export function exportOrderListApi(params) {
  return `${
    import.meta.env.VITE_GLOB_API_URL
  }/cms/loan-order-record/export/orderList?${qs.stringify(params)}`;
}
// 订单节点查询
export function getOrderNodeApi(loanCode, params = {}) {
  return post(`/cms/loan-order-record/query/orderNode/${loanCode}`, params);
}

// 订单子节点查询
export function getSubOrderNodeApi(orderNode, params = {}) {
  return get(`/cms/loan-order-record/query/subOrderNode/${orderNode}`, params);
}
// 订单数据统计
export function getOrderDataCountApi(params = {}) {
  return get(`/cms/loan-order-record/count/waitingOrderNum`, params);
}

// 订单取消
export function cancelOrderApi(innerOrderNo, params = {}) {
  return get(`/cms/loan-order-record/cancel/order/${innerOrderNo}`, params);
}
