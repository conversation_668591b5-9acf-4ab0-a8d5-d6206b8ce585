<template>
  <div class="order-stages-header">
    <!-- 基础信息 -->
    <div class="basic-info-container">
      <n-descriptions
        title="基本信息"
        :column="6"
        label-placement="left"
        size="small"
        class="basic-info"
      >
        <n-descriptions-item label="贷款订单ID">
          {{ basicInfo.innerOrderNo || '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="姓名">
          {{ basicInfo.preAuditName || '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="城市">
          {{ cityName || '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="手机号">
          {{ basicInfo.preAuditPhoneNum || '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="期望贷款金额">
          {{ basicInfo.psvaLoanAmout || '-' }}
        </n-descriptions-item>
        <n-descriptions-item label="期望贷款期限">
          {{ basicInfo.psvaLoanTime || '-' }}
        </n-descriptions-item>
      </n-descriptions>
    </div>
    <div class="stages-container-wrapper">
      <!-- 阶段流程 -->
      <div class="stages-container">
        <div
          v-for="stage in stages"
          :key="stage.id"
          class="stage-item"
          :class="[
            getStageClass(stage.status),
            'cursor-pointer',
            { 'stage-selected': isStageSelected(stage) },
          ]"
          @click="handleStageClick(stage)"
        >
          <div class="stage-content">
            <div class="stage-title">{{ stage.title }}</div>
          </div>
        </div>
      </div>

      <!-- 小阶段步骤条 -->
      <div
        v-if="
          props.currentStage &&
          props.currentStage.subStages &&
          props.currentStage.subStages.length > 0
        "
        class="sub-stages-container"
      >
        <div class="custom-steps">
          <!-- 连接线层（背景层） -->
          <div class="lines-container" :style="contentGridStyle">
            <div
              v-for="(subStage, index) in props.currentStage.subStages"
              :key="`line-${subStage.id}`"
              class="line-cell"
            >
              <div
                v-if="index < props.currentStage.subStages!.length - 1"
                class="step-line"
                :class="`line-${subStage.status}`"
              ></div>
            </div>
          </div>

          <!-- 图标和文字层（前景层） -->
          <div class="steps-grid" :style="contentGridStyle">
            <div
              v-for="subStage in props.currentStage.subStages"
              :key="subStage.id"
              class="step-item"
              :class="`step-${subStage.status}`"
            >
              <div class="step-icon" :class="`icon-${subStage.status}`">
                <div class="inner-circle"></div>
              </div>
              <div class="step-content">
                <div class="step-title">{{ subStage.title }}</div>
                <div v-if="subStage.description" class="step-description">
                  {{ subStage.description }}
                </div>
                <div v-if="subStage.createTime" class="step-time">
                  {{ formatTime(subStage.createTime) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import type { Stage, StageStatus, StageNodeData } from '../../types';
  import { transformStageData } from '../../types';
  import { useAddressOptions } from '@/components/AddressInput/useAddressOptions';
  import { storeToRefs } from 'pinia';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import dayjs from 'dayjs';
  // 定义基本信息类型
  interface BasicInfo {
    innerOrderNo: string;
    preAuditName: string;
    preAuditPhoneNum: string;
    psvaLoanAmout: string;
    psvaLoanTime: string;
    preAuditPhaseCity: string;
  }
  // 定义 props
  const props = withDefaults(
    defineProps<{
      stagesList?: Stage[];
      stagesData?: StageNodeData[]; // 支持扁平化数据
      basicInfo?: BasicInfo; // 基本信息
      currentStage?: Stage | null;
    }>(),
    {
      stagesList: () => [],
      stagesData: () => [],
      basicInfo: () => ({
        innerOrderNo: '',
        preAuditName: '',
        preAuditPhoneNum: '',
        psvaLoanAmout: '',
        psvaLoanTime: '',
        preAuditPhaseCity: '',
      }),
      currentStage: null,
    }
  );
  const { getNameByCode } = useAddressOptions();

  // 获取 store 中的抵押字段数据
  const orderDrawerStore = useOrderDrawerStore();
  const { mortgageFields } = storeToRefs(orderDrawerStore);

  // 使用传入的数据或默认数据
  const stages = computed(() => {
    // 优先使用扁平化数据
    if (props.stagesData && props.stagesData.length > 0) {
      return transformStageData(props.stagesData, mortgageFields.value);
    }
    // 其次使用嵌套数据
    if (props.stagesList && props.stagesList.length > 0) {
      return props.stagesList;
    }
    // 最后使用默认数据
    return [];
  });

  // 计算步骤内容区域的 grid 布局
  const contentGridStyle = computed(() => {
    const count = props.currentStage?.subStages?.length || 0;
    return {
      gridTemplateColumns: `repeat(${count}, 1fr)`,
    };
  });

  // 获取阶段样式类
  const getStageClass = (status: StageStatus) => {
    return `status-${status}`;
  };

  // 判断阶段是否被选中
  const isStageSelected = (stage: Stage) => {
    return props.currentStage?.id === stage.id;
  };

  // 点击大阶段：只 emit，不维护本地 state
  const emit = defineEmits<{
    stageClick: [stage: Stage];
  }>();
  const handleStageClick = (stage: Stage) => {
    emit('stageClick', stage);
  };

  // 根据城市 code 通过接口获取城市名称
  const cityName = ref<string | null>(null);

  const fetchCityName = async () => {
    const cityCode = props.basicInfo?.preAuditPhaseCity;
    if (!cityCode) {
      cityName.value = null;
      return;
    }
    cityName.value = await getNameByCode(cityCode);
  };

  // 监听城市 code 变化，重新获取城市名称
  watch(
    () => props.basicInfo?.preAuditPhaseCity,
    () => {
      fetchCityName();
    },
    { immediate: true }
  );

  // 格式化时间
  const formatTime = (time: string) => {
    if (!time) return '-';
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
  };
</script>

<style lang="less" scoped>
  .basic-info-container {
    background-color: #fff;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 12px;
  }
  .order-stages-header {
    .basic-info {
      margin-bottom: 0;
    }
    .stages-container-wrapper {
      background-color: #fff;
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 12px;
    }
    .stages-container {
      display: flex;
      align-items: center;
      width: 100%;
      overflow-x: auto;
      margin-bottom: 20px;
    }

    .stage-item {
      position: relative;
      flex: 1;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #1890ff;
      color: white;
      font-size: 16px;
      font-weight: 500;
      transition: all 0.3s ease;
      padding: 0 20px;
      margin-right: 5px;

      // 右侧三角形箭头
      &::after {
        content: '';
        position: absolute;
        right: -10px;
        top: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 24px 0 24px 10px;
        border-color: transparent transparent transparent #1890ff;
        z-index: 2;
        transition: border-color 0.3s ease;
      }

      // 左侧凹陷
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 24px 0 24px 10px;
        border-color: transparent transparent transparent #fff;
        z-index: 1;
      }

      &:first-child {
        border-radius: 8px 0 0 8px;
        padding-left: 30px;

        &::before {
          display: none;
        }
      }

      &:last-child {
        border-radius: 0 8px 8px 0;
        padding-right: 30px;
        margin-right: 0;

        &::after {
          display: none;
        }
      }

      &:first-child:last-child {
        border-radius: 8px;
      }

      .stage-content {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 0;

        .stage-title {
          position: relative;
          z-index: 1;
          color: white;
          white-space: nowrap;
        }
      }

      // 已完成状态 - 绿色
      &.status-completed {
        background: #52c41a;
        color: white;

        &::after {
          border-left-color: #52c41a;
        }
      }

      // 进行中状态 - 蓝色
      &.status-ongoing {
        background: #1890ff;
        color: white;

        &::after {
          border-left-color: #1890ff;
        }
      }

      // 退回状态 - 黄色
      &.status-returned {
        background: #faad14;
        color: white;

        &::after {
          border-left-color: #faad14;
        }
      }

      // 失败状态 - 红色
      &.status-failed {
        background: #ff4d4f;
        color: white;

        &::after {
          border-left-color: #ff4d4f;
        }
      }

      // 未开始状态 - 灰色（不可操作）
      &.status-pending {
        background: #d9d9d9;
        color: #8c8c8c;
        cursor: not-allowed;

        &::after {
          border-left-color: #d9d9d9;
        }
      }

      // 选中状态 - 外发光 + 缩放 + 边框高亮组合
      &.stage-selected {
        border-bottom: 3px solid #dbc290;
      }
    }

    // 小阶段步骤条容器
    .sub-stages-container {
      background: #ffffff;
      border-radius: 8px;
      margin-top: 16px;
    }

    // 自定义步骤条样式
    .custom-steps {
      width: 100%;
      background-color: #f4f9fd;
      padding: 12px;
      position: relative;

      // 连接线层（背景层）
      .lines-container {
        position: absolute;
        top: 20px; // 图标中心位置（padding 12px + 图标高度的一半 8px）
        left: 12px;
        right: 12px;
        display: grid;
        gap: 0;
        pointer-events: none;

        .line-cell {
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .step-line {
            position: absolute;
            left: 50%;
            width: 100%;
            height: 2px;
            transition: all 0.3s ease;

            &.line-completed {
              background: #52c41a;
            }

            &.line-returned {
              background: #faad14;
            }

            &.line-failed {
              background: #ff4d4f;
            }

            &.line-ongoing,
            &.line-pending {
              background: #d9d9d9;
            }
          }
        }
      }

      // 图标和文字层（前景层）
      .steps-grid {
        display: grid;
        width: 100%;
        gap: 0;
        position: relative;
        z-index: 1;

        .step-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          min-width: 0;

          .step-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
            margin-bottom: 12px;

            .inner-circle {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              transition: all 0.3s ease;
            }

            &.icon-completed {
              background: #c3ffa5;
              border: 1px solid #51d311;

              .inner-circle {
                background: #71dd3c;
              }
            }

            &.icon-ongoing {
              background: #c2e6ff;
              border: 1px solid #1890ff;

              .inner-circle {
                background: #2c8df4;
              }
            }

            &.icon-returned {
              background: #ffe7ba;
              border: 1px solid #faad14;

              .inner-circle {
                background: #faad14;
              }
            }

            &.icon-failed {
              background: #ffccc7;
              border: 1px solid #ff4d4f;

              .inner-circle {
                background: #ff4d4f;
              }
            }

            &.icon-pending {
              background: #eceeee;
              border: 1px solid #d9d9d9;

              .inner-circle {
                background: #ccccca;
              }
            }
          }

          .step-content {
            text-align: center;
            min-width: 0;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;

            .step-title {
              font-size: 14px;
              color: #262626;
              font-weight: 500;
              line-height: 1.5;
              margin-bottom: 4px;
              word-break: break-word;
            }

            .step-description {
              font-size: 12px;
              color: #8c8c8c;
              line-height: 1.5;
              word-break: break-word;
              margin-bottom: 4px;
            }

            .step-time {
              font-size: 12px;
              color: #8c8c8c;
              line-height: 1.2;
              word-break: break-word;
            }
          }

          // 已完成步骤
          &.step-completed {
            .step-content .step-title {
              color: #262626;
            }
          }

          // 进行中步骤
          &.step-ongoing {
            .step-content .step-title {
              font-weight: 600;
              color: #1890ff;
            }
          }

          // 退回步骤
          &.step-returned {
            .step-content .step-title {
              color: #faad14;
            }
          }

          // 失败步骤
          &.step-failed {
            .step-content .step-title {
              color: #ff4d4f;
            }
          }

          // 待处理步骤
          &.step-pending {
            .step-content .step-title {
              color: #8c8c8c;
            }
          }
        }
      }
    }
  }
</style>
