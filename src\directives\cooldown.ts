/*
  统一按钮防重复点击指令：在一次点击后，指定时间内（默认 5000ms）禁止再次触发点击事件。

  用法：
  - 固定 5s：<n-button v-cooldown>提交</n-button>
  - 自定义时长：<n-button v-cooldown="3000">提交</n-button> // 3s

  说明：
  - 指令会在点击后拦截后续点击（stopImmediatePropagation + preventDefault），
    并临时设置 pointer-events: none 与 aria-disabled 提示状态。
  - 对 naive-ui 的 n-button 也适用（不依赖设置组件的 disabled 属性）。
*/
import type { Directive, DirectiveBinding } from 'vue';

interface ElType extends HTMLElement {
  __cooldown_click_handler__?: (e: Event) => void;
  __cooldown_timer__?: number | null;
  __cooldown_locked__?: boolean;
  __cooldown_prev_pe__?: string;
  __cooldown_prev_opacity__?: string;
  __cooldown_prev_cursor__?: string;
}

function lock(el: ElType) {
  if (el.__cooldown_locked__) return;
  el.__cooldown_locked__ = true;
  // 记录并设置样式
  el.__cooldown_prev_pe__ = el.style.pointerEvents;
  el.__cooldown_prev_opacity__ = el.style.opacity;
  el.__cooldown_prev_cursor__ = el.style.cursor;

  el.style.pointerEvents = 'none';
  el.style.opacity = el.style.opacity || '0.4';
  el.style.cursor = 'not-allowed';
  el.classList.add('is-cooldown-disabled');
  el.setAttribute('aria-disabled', 'true');
}

function unlock(el: ElType) {
  el.__cooldown_locked__ = false;
  // 恢复 pointer-events
  if (el.__cooldown_prev_pe__ !== undefined) {
    el.style.pointerEvents = el.__cooldown_prev_pe__ as string;
  } else {
    el.style.removeProperty('pointer-events');
  }
  // 恢复其他样式
  if (el.__cooldown_prev_opacity__ !== undefined) {
    el.style.opacity = el.__cooldown_prev_opacity__ as string;
  } else {
    el.style.removeProperty('opacity');
  }
  if (el.__cooldown_prev_cursor__ !== undefined) {
    el.style.cursor = el.__cooldown_prev_cursor__ as string;
  } else {
    el.style.removeProperty('cursor');
  }
  el.classList.remove('is-cooldown-disabled');
  el.removeAttribute('aria-disabled');
}

const cooldown: Directive = {
  mounted(el: ElType, binding: DirectiveBinding<number | undefined>) {
    const getDuration = () => {
      const v = binding.value;
      const n = typeof v === 'number' && isFinite(v) ? Math.max(0, v) : 5000;
      return n;
    };

    const handler = (e: Event) => {
      if (el.__cooldown_locked__) {
        // 在冷却中：拦截本次点击
        e.stopImmediatePropagation?.();
        e.stopPropagation();
        e.preventDefault();
        return;
      }
      // 首次点击：进入冷却
      lock(el);
      const duration = getDuration();
      if (el.__cooldown_timer__) {
        window.clearTimeout(el.__cooldown_timer__);
      }
      el.__cooldown_timer__ = window.setTimeout(() => {
        unlock(el);
        el.__cooldown_timer__ = null;
      }, duration);
    };

    el.__cooldown_click_handler__ = handler;
    // 捕获阶段监听，尽早拦截
    el.addEventListener('click', handler, true);
  },
  beforeUnmount(el: ElType) {
    if (el.__cooldown_click_handler__) {
      el.removeEventListener('click', el.__cooldown_click_handler__, true);
    }
    if (el.__cooldown_timer__) {
      window.clearTimeout(el.__cooldown_timer__);
      el.__cooldown_timer__ = null;
    }
    // 清理样式与状态
    unlock(el);
  },
};

export default cooldown;
