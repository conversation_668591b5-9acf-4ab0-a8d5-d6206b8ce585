<template>
  <n-space vertical>
    <n-card>
      <!-- 预审生成 -->
      <n-collapse arrow-placement="right" :expanded-names="expandedNames">
        <n-collapse-item name="1-1">
          <template #header>
            <Title :mainNode="1" :subNode="1" @click="toggleExpanded('1-1')" />
          </template>

          <div>
            <n-form
              ref="formRef1"
              label-placement="left"
              size="medium"
              :model="formModel1"
              :rules="currentRules1"
              :disabled="formDisabled1"
              label-width="120px"
              @submit.prevent
            >
              <!-- 身份证上传区域 -->
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="身份证国徽面" path="preAuditIdCardReverse" required>
                    <UploadFile
                      :file-list="jsonToImage(formModel1.preAuditIdCardReverse || '')"
                      accept=".jpg,.jpeg,.png,.bmp"
                      :max-size="10"
                      :force-array="true"
                      :disabled="formDisabled1"
                      @update:file-list="
                        (v) => {
                          formModel1.preAuditIdCardReverse = imageToJson(v);
                          onOcr(formModel1.preAuditIdCardReverse, 'preAuditIdCardReverse');
                        }
                      "
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="身份证人像面" path="preAuditIdCardFront" required>
                    <UploadFile
                      :file-list="jsonToImage(formModel1.preAuditIdCardFront || '')"
                      accept=".jpg,.jpeg,.png,.bmp"
                      :max-size="10"
                      :force-array="true"
                      :disabled="formDisabled1"
                      @update:file-list="
                        (v) => {
                          formModel1.preAuditIdCardFront = imageToJson(v);
                          onOcr(formModel1.preAuditIdCardFront, 'preAuditIdCardFront');
                        }
                      "
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="姓名" path="preAuditName">
                    <n-input
                      v-model:value="formModel1.preAuditName"
                      placeholder="请输入姓名"
                      required
                      maxlength="20"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="手机号" path="preAuditPhoneNum" required>
                    <n-input
                      v-model:value="formModel1.preAuditPhoneNum"
                      placeholder="请输入手机号"
                      maxlength="11"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="身份证号" path="preAuditIdCardNum" required>
                    <n-input
                      v-model:value="formModel1.preAuditIdCardNum"
                      placeholder="请输入身份证号"
                      maxlength="18"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <n-flex justify="center">
                <n-button
                  :type="canSubmit ? 'primary' : 'default'"
                  form-type="submit"
                  :disabled="formDisabled1"
                  :loading="formLoading1"
                  @click="canSubmit ? handleSubmit() : handleSave()"
                >
                  {{ canSubmit ? '提交' : '保存' }}
                </n-button>
              </n-flex>
            </n-form>

            <n-divider dashed />

            <SubTitle title="预审生成" desc="前置要求：姓名、手机号、身份证号完整" />

            <n-descriptions label-placement="top" bordered :column="2" class="w-1/3">
              <n-descriptions-item label="进件状态">
                <n-tag size="small" type="primary">
                  {{
                    PreApprovalStatusMap[
                      initialData.preAuditApplyStatus || PreApprovalStatus.WaitStart
                    ]
                  }}
                </n-tag>
              </n-descriptions-item>
            </n-descriptions>
          </div>
        </n-collapse-item>
      </n-collapse>
    </n-card>

    <n-card>
      <!-- 授权书签署 -->
      <n-collapse arrow-placement="right" :expanded-names="expandedNames">
        <n-collapse-item name="1-2">
          <template #header>
            <Title :mainNode="1" :subNode="2" @click="toggleExpanded('1-2')" />
          </template>
          <template v-if="isCurrentStageOngoing('1-2') || initialData.authorizationSignStatus">
            <SubTitle title="授权书签署" />
            <n-descriptions label-placement="top" bordered :column="3">
              <n-descriptions-item label="授权书签署地址">
                <n-space
                  v-if="
                    (String(initialData.authorizationSignStatus) ===
                      String(PreApprovalSignStatus.UnSign) &&
                      (!initialData.authorizationSignTime ||
                        (initialData.authorizationSignTime &&
                          dayjs(initialData.authorizationSignTime).isAfter(
                            dayjs(initialData.serverTime).subtract(1, 'day')
                          )))) ||
                    String(initialData.authorizationSignStatus) ===
                      String(PreApprovalSignStatus.SignFinish)
                  "
                  align="center"
                  justify="center"
                >
                  <n-text type="primary">
                    <!-- {{ initialData.authorizationSignUrl }} -->
                    <n-qr-code
                      class="p-0"
                      v-if="initialData.authorizationSignUrl"
                      id="authorizationSignUrl_qrcode"
                      :value="initialData.authorizationSignUrl"
                    />
                  </n-text>
                  <n-button
                    v-if="initialData.authorizationSignUrl"
                    size="small"
                    type="primary"
                    @click="copyQrcode"
                  >
                    复制二维码
                  </n-button>
                </n-space>
                <n-space v-else align="center" justify="center">-</n-space>
              </n-descriptions-item>
              <n-descriptions-item label="签署状态">
                <n-tag
                  size="small"
                  :type="
                    String(initialData.authorizationSignStatus) ===
                    String(PreApprovalSignStatus.SignFinish)
                      ? 'success'
                      : 'warning'
                  "
                >
                  {{
                    PreApprovalSignStatusMap[
                      initialData.authorizationSignStatus || PreApprovalSignStatus.UnSign
                    ]
                  }}
                </n-tag>
              </n-descriptions-item>
              <n-descriptions-item label="操作">
                <template
                  v-if="
                    String(initialData.authorizationSignStatus) ===
                      String(PreApprovalSignStatus.UnSign) &&
                    initialData.authorizationSignTime &&
                    dayjs(initialData.authorizationSignTime).isBefore(
                      dayjs(initialData.serverTime).subtract(1, 'day')
                    )
                  "
                >
                  <n-button size="small" type="primary" @click="reSignAuthorization">
                    重新签署
                  </n-button>
                </template>
                <template v-else>
                  <n-button
                    size="small"
                    type="primary"
                    :disabled="
                      String(initialData.authorizationSignStatus) ===
                      String(PreApprovalSignStatus.SignFinish)
                    "
                    v-cooldown
                    @click="queryAuthorizationSignStatus"
                  >
                    状态查询
                  </n-button>
                </template>
              </n-descriptions-item>
            </n-descriptions>
          </template>
        </n-collapse-item>
      </n-collapse>
    </n-card>

    <n-card>
      <!-- 预审阶段 -->
      <n-collapse arrow-placement="right" :expanded-names="expandedNames">
        <n-collapse-item name="1-3">
          <template #header>
            <Title :mainNode="1" :subNode="3" @click="toggleExpanded('1-3')" />
          </template>

          <n-form
            ref="formRef2"
            label-placement="left"
            size="medium"
            :model="formModel2"
            :rules="currentRules2"
            :disabled="formDisabled2"
            label-width="120px"
          >
            <n-grid :cols="GRID_COLS">
              <n-grid-item>
                <n-form-item label="申请城市" path="preAuditPhaseCity" required>
                  <CitySelect
                    v-model="formModel2.preAuditPhaseCity"
                    change-on-select
                    :multiple="false"
                    :disabled="formDisabled2"
                  />
                </n-form-item>
              </n-grid-item>
            </n-grid>
            <n-flex justify="center">
              <n-button
                :type="canSubmit2 ? 'primary' : 'default'"
                :disabled="formDisabled2"
                :loading="formLoading2"
                @click="canSubmit2 ? handleSubmit2() : handleSave2()"
              >
                {{ canSubmit2 ? '提交' : '保存' }}
              </n-button>
            </n-flex>
          </n-form>

          <template v-if="initialData.preAuditPhaseStatus">
            <n-divider dashed />
            <SubTitle
              title="授权书签署"
              desc="前置要求：姓名、手机号、申请城市、身份证号完整、身份证正反面"
            />
            <n-descriptions label-placement="top" bordered :column="4">
              <n-descriptions-item label="预审状态">
                <n-tag
                  size="small"
                  :type="
                    String(initialData.preAuditPhaseStatus) === String(PreAuditPhaseSignStatus.Pass)
                      ? 'success'
                      : 'warning'
                  "
                >
                  {{ PreAuditPhaseSignStatusMap[initialData.preAuditPhaseStatus] }}
                </n-tag>
              </n-descriptions-item>
              <n-descriptions-item>
                <template #label>
                  <div class="flex items-center">
                    <n-text>客户评级</n-text>
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      客户等级分为：A、B、C、C-
                    </n-tooltip>
                  </div>
                </template>
                {{ initialData.preCustomerLevel || '-' }}
              </n-descriptions-item>
              <n-descriptions-item label="不通过原因">
                <n-space class="max-w-[300px]">
                  {{ initialData.preAuditPhaseCause || '-' }}
                </n-space>
              </n-descriptions-item>
              <n-descriptions-item label="操作">
                <n-button
                  size="small"
                  type="primary"
                  :disabled="
                    String(initialData.preAuditPhaseStatus) ===
                      String(PreAuditPhaseSignStatus.Pass) ||
                    String(initialData.preAuditPhaseStatus) ===
                      String(PreAuditPhaseSignStatus.Refuse)
                  "
                  v-cooldown
                  @click="queryPreAuditPhaseStatus"
                >
                  状态查询
                </n-button>
              </n-descriptions-item>
            </n-descriptions>
          </template>
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import UploadFile from '@/components/UploadFile/index.vue';
  import CitySelect from '@/components/CitySelect/v2.vue';
  import IdCardModal from './components/IdCardModal/index.vue';
  import { QuestionCircleTwotone } from '@vicons/antd';

  import {
    saveFieldData,
    submitForm,
    getAuthorizationSign,
    getAuthorizationSignStatus,
    getPreAuditPhaseStatus,
  } from '@/api/dashboard/deyi';
  import { ref, reactive, computed, onMounted, watch, inject } from 'vue';
  import { GRID_COLS } from '@/components/DrawerOrder/config';
  import type { FormInst } from 'naive-ui';
  import type { Fields } from '@/components/DrawerOrder/types';
  import {
    PreApprovalStatus,
    PreApprovalStatusMap,
    PreApprovalSignStatus,
    PreApprovalSignStatusMap,
    PreAuditPhaseSignStatus,
    PreAuditPhaseSignStatusMap,
  } from '@/components/DrawerOrder/enum';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import { idCardOcr } from '@/components/DrawerOrder/ocr';
  import dayjs from 'dayjs';
  import { useBaseData } from './composables/useBaseData';
  import { useMountComponent } from '@/composables/useMountComponent';
  import { imageToJson, jsonToImage, validateCityCode } from '@/components/DrawerOrder/utils';
  import { useFormDisabled } from '@/components/DrawerOrder/composables/useFormDisabled';
  import { copyCanvasToClipboard } from '@/utils/copyCanvasToClipboard';

  const props = defineProps<{ innerOrderNo: string }>();

  const { mountPromisify } = useMountComponent();
  const orderDrawerStore = useOrderDrawerStore();
  const { isCurrentStagePending, isCurrentStageCompleted, isCurrentStageOngoing } =
    useFormDisabled();

  const expandedNames = inject('expandedNames');
  const toggleExpanded = inject('toggleExpanded') as (name: string) => void;

  const formRef1 = ref<FormInst | null>(null);
  const formLoading1 = ref(false);
  const formModel1 = reactive<Partial<Fields>>({
    preAuditName: '',
    preAuditPhoneNum: '',
    preAuditIdCardNum: '',
    preAuditIdCardReverse: '', // 身份证国徽面
    preAuditIdCardFront: '', // 身份证人像面
  });
  //复制签约二维码
  async function copyQrcode() {
    const canvas = document.getElementById('authorizationSignUrl_qrcode')?.querySelector('canvas');
    canvas && (await copyCanvasToClipboard(canvas));
    window.$message.success('复制成功');
  }
  const formRules = {
    preAuditName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    preAuditPhoneNum: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
    ],
    preAuditIdCardNum: [
      { required: true, message: '请输入身份证号', trigger: 'blur' },
      {
        pattern:
          /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: '请输入正确的身份证号',
        trigger: 'blur',
      },
    ],
    preAuditIdCardReverse: [{ required: true, message: '请上传身份证国徽面', trigger: 'change' }],
    preAuditIdCardFront: [{ required: true, message: '请上传身份证人像面', trigger: 'change' }],
  };
  const formDisabled1 = computed(() => isCurrentStageCompleted('1-1'));

  // 必填字段列表（自动遍历formSubmitRules中有required:true的fields）
  const requiredFields = Object.keys(formRules).filter(
    (field) => Array.isArray(formRules[field]) && formRules[field].some((rule) => rule.required)
  );
  const canSubmit = computed(() => {
    for (const field of requiredFields) {
      // 1. 首先必须填写
      if (!formModel1[field]) return false;
    }

    // 当前节点处于未开始
    if (isCurrentStagePending('1-1')) {
      return false;
    }

    return true;
  });

  const currentRules1 = computed(() => {
    if (canSubmit.value) {
      // 严格：包含所有required
      return formRules;
    }
    // 宽松，去掉required
    const rules: any = {};
    Object.keys(formRules).forEach((key) => {
      rules[key] = (formRules[key] as any[]).filter((rule) => !rule.required);
    });
    return rules;
  });

  const onOcr = async (val, formKey) => {
    if (!val) {
      return;
    }

    let data: any = {};
    const urls = jsonToImage(val).map((item) => item.url);
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i];
      const res = await idCardOcr({ url });

      Object.assign(data, res || {});
    }

    // 判断身份证正反面
    if (formKey === 'preAuditIdCardFront' && !data.preAuditName) {
      window.$message?.error('请上传身份证人像面');
      formModel1.preAuditIdCardFront = '';
      return;
    }

    if (formKey === 'preAuditIdCardReverse' && !data.ipIdExpireDate) {
      window.$message?.error('请上传身份证国徽面');
      formModel1.preAuditIdCardReverse = '';
      return;
    }

    const handler = (key, label) => {
      if (data[key]) {
        if (!formModel1[key]) {
          formModel1[key] = data[key];
        } else if (formModel1[key] && formModel1[key] !== data[key]) {
          window.$message?.warning(`${label}不一致，请检查`);
          formModel1[key] = '';
          return true;
        }
      }
    };

    const clearFields = await Promise.all([
      handler('preAuditName', '姓名'),
      handler('preAuditIdCardNum', '身份证号码'),
    ]);

    if (clearFields.includes(true)) {
      data = {};
    }

    // 保存
    saveFieldData({
      innerOrderNo: props.innerOrderNo,
      fieldData: {
        [formKey]: val,
        // 单独把身份证解析信息保存
        ...Object.keys(data).reduce((pre, key) => {
          if (data[key] && typeof data[key] === 'string') {
            pre[key] = data[key];
          }
          return pre;
        }, {}),
      },
    });
  };

  const handleSave = async () => {
    try {
      await formRef1.value?.validate();
    } catch {
      window.$message?.error('填写内容格式不正确');
      return;
    }
    try {
      formLoading1.value = true;

      const innerOrderNo = props.innerOrderNo;
      await saveFieldData({ innerOrderNo, fieldData: { ...formModel1 } });
      window.$message?.success('保存成功');
      getData();
    } catch (e) {
      window.$message?.error('保存失败');
      console.error(e);
    } finally {
      formLoading1.value = false;
    }
  };

  const handleSubmit = async () => {
    try {
      await formRef1.value?.validate();
    } catch {
      window.$message?.error('填写内容格式不正确');
      return;
    }

    try {
      formLoading1.value = true;
      const innerOrderNo = props.innerOrderNo;

      await saveFieldData({ innerOrderNo, fieldData: { ...formModel1 } });
      await submitForm({ innerOrderNo, mainNodeCode: 1, subNodeCode: 1 });
      window.$message?.success('提交成功');

      orderDrawerStore.triggerRefresh();
      getData();
    } catch (e) {
      window.$message?.error('提交失败');
      console.error(e);
    } finally {
      formLoading1.value = false;
    }
  };

  const formRef2 = ref<FormInst | null>(null);
  const formLoading2 = ref(false);
  const formModel2 = reactive<Partial<Fields>>({
    preAuditPhaseCity: '',
  });
  const formRules2 = {
    preAuditPhaseCity: [
      {
        validator(_rule, value) {
          return validateCityCode(value as string);
        },
        trigger: 'blur',
      },
    ],
  };
  const formDisabled2 = computed(() => isCurrentStageCompleted('1-3'));
  const currentRules2 = computed(() => {
    // 若可提交则严格用 formRules2，否则去掉required只保留自定义校验
    if (canSubmit2.value) return formRules2;
    const rules = {};
    Object.keys(formRules2).forEach((key) => {
      rules[key] = (formRules2[key] as any[]).filter((rule) => !rule.required);
    });
    return rules;
  });
  const canSubmit2 = computed(() => {
    const val = formModel2.preAuditPhaseCity;
    if (!val) return false;
    if (isCurrentStagePending('1-3')) return false;

    // 通过自定义校验
    return true;
  });
  const handleSave2 = async () => {
    try {
      await formRef2.value?.validate();
    } catch {
      window.$message?.error('填写内容格式不正确');
      return;
    }
    try {
      formLoading2.value = true;
      const innerOrderNo = props.innerOrderNo;
      await saveFieldData({ innerOrderNo, fieldData: { ...formModel2 } });
      window.$message?.success('保存成功');
    } catch (e) {
      window.$message?.error('保存失败');
      console.error(e);
    } finally {
      formLoading2.value = false;
    }
  };
  const handleSubmit2 = async () => {
    try {
      await formRef2.value?.validate();
    } catch (error) {
      window.$message?.error('填写内容格式不正确');
      console.error('表单验证失败:', error);
      return;
    }
    try {
      formLoading2.value = true;
      const innerOrderNo = props.innerOrderNo;
      await saveFieldData({ innerOrderNo, fieldData: formModel2 });
      await submitForm({ innerOrderNo, mainNodeCode: 1, subNodeCode: 3 });
    } catch (e) {
      formLoading2.value = false;
      const { code } = e as any;
      if (code === 50004) {
        const modalRes = await showReSubmitIdCardModal();
        if (modalRes !== 'ok') {
          return;
        }
      } else {
        window.$message?.error('提交失败');
        console.error(e);
        return;
      }
    } finally {
      formLoading2.value = false;
    }

    window.$message?.success('提交成功');
    orderDrawerStore.triggerRefresh();
    getData();
  };

  const reSignAuthorization = async () => {
    await getAuthorizationSign({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };
  const queryAuthorizationSignStatus = async () => {
    await getAuthorizationSignStatus({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };
  const queryPreAuditPhaseStatus = async () => {
    await getPreAuditPhaseStatus({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };
  const showReSubmitIdCardModal = () => {
    return mountPromisify<'ok' | 'cancel'>({
      render: (ctx) => <IdCardModal innerOrderNo={props.innerOrderNo} onClose={ctx.unmount} />,
    });
  };

  const { data: initialData, getData } = useBaseData({
    innerOrderNo: props.innerOrderNo,
    keys: [
      ...Object.keys(formModel1),
      ...Object.keys(formModel2),
      ...[
        'preAuditApplyStatus',
        'authorizationSignUrl',
        'authorizationSignStatus',
        'authorizationSignTime',
        'preAuditPhaseStatus',
        'preAuditPhaseCause',
        'preCustomerLevel',
      ],
    ],
  });

  // 将返回数据同步到表单模型（授权书/预审阶段直接用 initialData）
  watch(
    initialData,
    (val) => {
      Object.keys(val).forEach((k) => {
        if (k in formModel1) {
          formModel1[k] = initialData[k];
        }
        if (k in formModel2) {
          formModel2[k] = initialData[k];
        }
      });
    },
    { immediate: true }
  );

  onMounted(() => {
    getData();
  });
</script>

<style lang="less" scoped></style>
