<template>
  <n-card>
    <!-- 资方绑卡合同签署 -->
    <n-collapse arrow-placement="right" :expanded-names="expandedNames">
      <n-collapse-item name="2-4">
        <template #header>
          <Title :mainNode="2" :subNode="4" @click="toggleExpanded('2-4')" />
        </template>

        <div>
          <n-form
            ref="formRef"
            label-placement="left"
            size="medium"
            :model="formModel"
            :rules="currentRules"
            :disabled="formDisabled"
            :loading="formLoading"
            label-width="160px"
          >
            <!-- 附件资料 -->
            <n-grid :cols="GRID_COLS">
              <n-grid-item span="24">
                <n-form-item label="附件资料" path="attachments" required>
                  <n-space>
                    <CombineSelect
                      v-model:value="selectValue"
                      v-model:appendix="formModel"
                      :options="selectedAttachmentOptions"
                      :disabled="formDisabled"
                    />
                    <n-space v-if="selectValue === 'funderCardGpsContract'">
                      <n-button
                        type="primary"
                        size="small"
                        :disabled="formDisabled"
                        @click="handleGenerateSign"
                      >
                        生成签约
                      </n-button>
                    </n-space>
                  </n-space>
                </n-form-item>
              </n-grid-item>
            </n-grid>

            <n-flex justify="center">
              <n-button
                :type="canSubmit ? 'primary' : 'default'"
                :disabled="formDisabled"
                :loading="formLoading"
                @click="canSubmit ? handleSubmit() : handleSave()"
              >
                {{ canSubmit ? '提交' : '保存' }}
              </n-button>
            </n-flex>
          </n-form>

          <template v-if="initialData.funderCardBindSignAddress">
            <n-divider dashed />

            <SubTitle title="资方绑卡合同签署" desc="前置要求:完善文件资料内容上传" />

            <!-- 改造：用 n-descriptions 展示签署信息 -->
            <n-descriptions label-placement="top" bordered :column="3">
              <n-descriptions-item label="资方绑卡签约合同签署地址">
                <n-space
                  v-if="
                    (String(initialData.funderCardSignResult) ===
                      String(ContractSignStatus.UnSign) &&
                      (!initialData.funderCardSignTime ||
                        (initialData.funderCardSignTime &&
                          dayjs(initialData.funderCardSignTime).isAfter(
                            dayjs(initialData.serverTime).subtract(1, 'day')
                          )))) ||
                    String(initialData.funderCardSignResult) ===
                      String(ContractSignStatus.SignFinish)
                  "
                  align="center"
                  justify="center"
                >
                  <n-text type="primary">
                    <!-- {{ initialData.funderCardBindSignAddress }} -->
                    <n-qr-code
                      class="p-0"
                      v-if="initialData.funderCardBindSignAddress"
                      id="funderCardBindSignAddress_qrcode"
                      :value="initialData.funderCardBindSignAddress"
                    />
                  </n-text>
                  <n-button
                    v-if="initialData.funderCardBindSignAddress"
                    size="small"
                    type="primary"
                    @click="copyQrcode"
                  >
                    复制二维码
                  </n-button>
                </n-space>
                <n-space v-else align="center" justify="center">-</n-space>
              </n-descriptions-item>
              <n-descriptions-item label="签署结果">
                <template v-if="initialData.funderCardSignResult">
                  <n-tag
                    size="small"
                    :type="
                      String(initialData.funderCardSignResult) ===
                      String(ContractSignStatus.SignFinish)
                        ? 'success'
                        : 'warning'
                    "
                  >
                    {{ ContractSignStatusMap[initialData.funderCardSignResult] }}
                  </n-tag>
                </template>
                <template v-else>-</template>
              </n-descriptions-item>
              <n-descriptions-item label="操作">
                <template
                  v-if="
                    String(initialData.funderCardSignResult) ===
                      String(ContractSignStatus.UnSign) &&
                    initialData.funderCardSignTime &&
                    dayjs(initialData.funderCardSignTime).isBefore(
                      dayjs(initialData.serverTime).subtract(1, 'day')
                    )
                  "
                >
                  <n-button size="small" type="primary" @click="onReCreateFunderCardSignUrl">
                    重新签署
                  </n-button>
                </template>
                <template v-else>
                  <n-button
                    size="small"
                    type="primary"
                    :disabled="
                      String(initialData.funderCardSignResult) ===
                      String(ContractSignStatus.SignFinish)
                    "
                    v-cooldown
                    @click="onQueryFunderCardSign"
                  >
                    状态查询
                  </n-button>
                </template>
              </n-descriptions-item>
            </n-descriptions>
          </template>
        </div>
      </n-collapse-item>
    </n-collapse>
  </n-card>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import CombineSelect from '@/components/DrawerOrder/components/CombineSelect/index.vue';

  import { ref, reactive, computed, onMounted, watch, inject, onUnmounted } from 'vue';
  import type { FormInst } from 'naive-ui';
  import { GRID_COLS } from '@/components/DrawerOrder/config';
  import { useBaseData } from '../../composables/useBaseData';
  import type { Fields } from '@/components/DrawerOrder/types';
  import {
    getFunderCardSignUrl,
    getFunderCardSignStatus,
    saveFieldData,
    submitForm,
  } from '@/api/dashboard/deyi';
  import { useOrderDrawerStore } from '@/store/modules/orderDrawer';
  import dayjs from 'dayjs';
  import { ContractSignStatus, ContractSignStatusMap } from '@/components/DrawerOrder/enum';
  import { ListData } from '@/views/dashboard/workplace/orderManage/columns';
  import { useFormDisabled } from '@/components/DrawerOrder/composables/useFormDisabled';
  import { bufferedEmitter, EventNames } from '@/utils/eventBus';
  import { useRouter, useRoute } from 'vue-router';
  import { jsonToImage } from '@/components/DrawerOrder/utils';
  import { copyCanvasToClipboard } from '@/utils/copyCanvasToClipboard';

  const rowData = inject('rowData') as ListData;
  const closeDrawer = inject('closeDrawer') as () => void;

  const router = useRouter();
  const route = useRoute();

  interface Props {
    innerOrderNo: string;
  }
  const props = defineProps<Props>();
  const expandedNames = inject('expandedNames');
  const toggleExpanded = inject('toggleExpanded') as (name: string) => void;
  const orderDrawerStore = useOrderDrawerStore();
  const { isCurrentStagePending, isCurrentStageCompleted } = useFormDisabled();
  const formDisabled = computed(() => isCurrentStageCompleted('2-4'));

  const formRef = ref<FormInst | null>(null);
  const formLoading = ref(false);
  const formModel = reactive<Partial<Fields>>({
    // 附件：资方绑卡合同阶段
    funderCardVehiclePhoto: '',
    funderCardPersonalInfoPage: '',
    // funderCardViolationPage: '',
    funderCardGpsContract: '',
    funderCardFrontLeft45: '',
    funderCardRearRight45: '',
    funderCardAffiliationAgreementThree: '',
    // funderCardEnginePanorama: '',
    funderCardClientAffiliationCo: '',
    funderCardShareholderResolution: '',
    funderCardMortgageContractOne: '',
    funderCardMortgageContractTwo: '',
    funderCardAffiliationAgreementOne: '',
    funderCardAffiliationAgreementTwo: '',
    funderCardGpsContractCommit: '',
    ipWealthProve: '',
  });

  // useBaseData - 拉取表单数据与枚举
  const { data: initialData, getData } = useBaseData({
    innerOrderNo: props.innerOrderNo,
    keys: [
      ...Object.keys(formModel),
      'funderCardBindSignAddress',
      'funderCardSignResult',
      'funderCardSignTime',

      // 车辆估值
      'psvaCarEvalPrice',
    ],
  });

  const isPublicNotice = ref<boolean>(false);

  const selectValue = ref('');
  const selectedAttachmentOptions = computed<any[]>(() => {
    const base = [
      {
        label: '人车合影(客户+车)',
        value: 'funderCardVehiclePhoto',
        finish: jsonToImage(formModel.funderCardVehiclePhoto)?.length >= 1,
        required: true,
        min: 1,
        max: 1,
      },
      // {
      //   label: '个人信息页',
      //   value: 'funderCardPersonalInfoPage',
      //   finish: jsonToImage(formModel.funderCardPersonalInfoPage)?.length >= 1,
      //   required: true,
      //   min: 1,
      //   max: 1,
      // },
      // {
      //   label: '违章信息页',
      //   value: 'funderCardViolationPage',
      //   finish: jsonToImage(formModel.funderCardViolationPage)?.length >= 1,
      //   required: true,
      //   min: 1,
      //   max: 1,
      // },
      // {
      //   label: 'GPS安装合同',
      //   value: 'funderCardGpsContract',
      //   finish: jsonToImage(formModel.funderCardGpsContract)?.length >= 1,
      //   required: true,
      //   min: 1,
      //   max: 1,
      // },
      // {
      //   label: '手持确认函(GPS安装合同)',
      //   value: 'funderCardGpsContractCommit',
      //   finish: jsonToImage(formModel.funderCardGpsContractCommit)?.length >= 1,
      //   required: false,
      //   min: 1,
      //   max: 1,
      // },
      {
        label: '车身左前方45度照',
        value: 'funderCardFrontLeft45',
        finish: jsonToImage(formModel.funderCardFrontLeft45)?.length >= 1,
        required: true,
        min: 1,
        max: 1,
      },
      {
        label: '车身右后方45度照',
        value: 'funderCardRearRight45',
        finish: jsonToImage(formModel.funderCardRearRight45)?.length >= 1,
        required: true,
        min: 1,
        max: 1,
      },
      // {
      //   label: '发动机全景照',
      //   value: 'funderCardEnginePanorama',
      //   finish: jsonToImage(formModel.funderCardEnginePanorama)?.length >= 1,
      //   required: true,
      //   min: 1,
      //   max: 1,
      // },
    ];

    // 附加
    const additional = [
      {
        label: '挂靠协议-三方',
        value: 'funderCardAffiliationAgreementThree',
        finish: jsonToImage(formModel.funderCardAffiliationAgreementThree)?.length >= 1,
        required: true,
        min: 1,
        max: 1,
      },
      {
        label: '客户&挂靠公司',
        value: 'funderCardClientAffiliationCo',
        finish: jsonToImage(formModel.funderCardClientAffiliationCo)?.length >= 1,
        required: true,
        min: 1,
        max: 1,
      },
      {
        label: '股东会决议',
        value: 'funderCardShareholderResolution',
        finish: jsonToImage(formModel.funderCardShareholderResolution)?.length >= 1,
        required: true,
        min: 1,
        max: 1,
      },
      {
        label: '抵押合同-公牌1',
        value: 'funderCardMortgageContractOne',
        finish: jsonToImage(formModel.funderCardMortgageContractOne)?.length >= 1,
        required: true,
        min: 1,
        max: 1,
      },
      {
        label: '抵押合同-公牌2',
        value: 'funderCardMortgageContractTwo',
        finish: jsonToImage(formModel.funderCardMortgageContractTwo)?.length >= 1,
        required: true,
        min: 1,
        max: 1,
      },
      {
        label: '挂靠协议1',
        value: 'funderCardAffiliationAgreementOne',
        finish: jsonToImage(formModel.funderCardAffiliationAgreementOne)?.length >= 1,
        required: true,
        min: 1,
        max: 1,
      },
      {
        label: '挂靠协议2',
        value: 'funderCardAffiliationAgreementTwo',
        finish: jsonToImage(formModel.funderCardAffiliationAgreementTwo)?.length >= 1,
        required: true,
        min: 1,
        max: 1,
      },
    ];

    // 如果公牌，需要添加公牌公司地址的附件
    if (isPublicNotice.value) {
      base.push(...additional);
    }

    // 如果车辆估值大于 20，需要添加资产证明
    if (Number(initialData.psvaCarEvalPrice) > 200000) {
      base.push({
        label: '资产证明',
        value: 'ipWealthProve',
        finish: jsonToImage(formModel.ipWealthProve)?.length >= 1,
        required: true,
        min: 1,
        max: 9,
      });
    }

    return [...base];
  });

  // 初始化数据回填
  watch(
    () => initialData,
    (val) => {
      if (!val) return;
      Object.keys(formModel).forEach((k) => {
        (formModel as any)[k] = (val as any)[k] ?? (formModel as any)[k];
      });
    },
    { immediate: true, deep: true }
  );

  // 校验规则与按钮切换
  const baseFormRules = {
    attachments: [
      {
        validator(_r) {
          const keys = selectedAttachmentOptions.value
            .filter((x) => x.required)
            .map((x) => x.value);

          for (const key of keys) {
            if (!formModel[key].length) {
              return new Error(
                `请上传${selectedAttachmentOptions.value.find((x) => x.value === key)?.label}`
              );
            }
          }
          return true;
        },
        trigger: 'change',
      },
    ],
  } as Record<string, any[]>;

  const canSubmit = computed(() => {
    if (isCurrentStagePending('2-4')) return false;
    const keys = selectedAttachmentOptions.value.filter((x) => x.required).map((x) => x.value);

    for (const key of keys) {
      if (!selectedAttachmentOptions.value.find((x) => x.value === key)?.finish) {
        return false;
      }
    }
    return true;
  });

  const currentRules = computed(() => {
    if (canSubmit.value) return baseFormRules;
    const rules: any = { attachments: [] };
    return rules;
  });

  // 生成签署
  const handleGenerateSign = () => {
    const eventData = {
      tab: 'loanRecord',
      clueId: Number(rowData.clueId),
    };

    // 如果当前已经在目标页面，直接发出事件，事件会被已注册的监听器处理
    if (route.path === '/client/my-clients') {
      bufferedEmitter.emit(EventNames.JUMP_MY_CLIENTS_DETAIL_TAB, eventData);
      closeDrawer();
    } else {
      // 如果不在目标页面，先发出事件（会被缓冲），然后跳转
      bufferedEmitter.emit(EventNames.JUMP_MY_CLIENTS_DETAIL_TAB, eventData);
      closeDrawer();
      router.push({ path: '/client/my-clients' });
    }
  };

  const onReCreateFunderCardSignUrl = async () => {
    await getFunderCardSignUrl({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };

  const onQueryFunderCardSign = async () => {
    await getFunderCardSignStatus({ innerOrderNo: props.innerOrderNo });
    orderDrawerStore.triggerRefresh();
    getData();
  };

  const handleSave = async () => {
    try {
      await formRef.value?.validate();
    } catch {
      window.$message?.error('填写内容格式不正确');
      return;
    }
    try {
      formLoading.value = true;
      const innerOrderNo = props.innerOrderNo;

      await saveFieldData({ innerOrderNo, fieldData: formModel });
      window.$message?.success('保存成功');
    } catch (e) {
      window.$message?.error('保存失败');
      console.error(e);
    } finally {
      formLoading.value = false;
    }
  };

  // 表单提交
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();
    } catch {
      return;
    }
    try {
      formLoading.value = true;
      const innerOrderNo = props.innerOrderNo;

      await saveFieldData({ innerOrderNo, fieldData: formModel });
      await submitForm({ innerOrderNo, mainNodeCode: 2, subNodeCode: 4 });

      orderDrawerStore.triggerRefresh();
      getData();
      window.$message?.success('提交成功');
    } catch (e) {
      window.$message?.error('提交失败');
      console.error(e);
    } finally {
      formLoading.value = false;
    }
  };
  //复制签约二维码
  async function copyQrcode() {
    const canvas = document
      .getElementById('funderCardBindSignAddress_qrcode')
      ?.querySelector('canvas');
    canvas && (await copyCanvasToClipboard(canvas));
    window.$message.success('复制成功');
  }
  onMounted(() => {
    getData();

    bufferedEmitter.on(EventNames.DEYI_POPUP_IS_PUBLIC_NOTICE, (isPublic: boolean) => {
      isPublicNotice.value = isPublic;
    });
  });

  onUnmounted(() => {
    bufferedEmitter.off(EventNames.DEYI_POPUP_IS_PUBLIC_NOTICE);
  });
</script>

<style lang="less" scoped></style>
